import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const content = {
  path: '/shopPing',
  meta: { title: '商城', icon: 'peoples', menuId: 'S114' },
  redirect: 'noredirect',
  component: () => import('@/views/layout/Layout.vue'),
  children: [
    {
      path: 'shopManage',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '商品管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11401'
      },
      children: [
        {
          path: 'shopManageList',
          component: () =>
            import('@/views/shopPing/shopManage/shopManageList/index.vue'),
          name: 'shopManageList',
          meta: {
            title: '淘宝商品列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140101'
          }
        },
        {
          path: 'shopCProprietary',
          component: () =>
            import('@/views/shopPing/shopManage/proprietary/index.vue'),
          name: 'shopCProprietary',
          meta: {
            title: 'C端自营商品列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140104'
          }
        },
        {
          path: 'LiveManageCar',
          component: () =>
            import('@/views/shopPing/shopManage/liveManageCar/index.vue'),
          name: 'LiveManageCar',
          meta: {
            title: 'C端自营车辆列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140105'
          }
        },
        {
          path: 'editShop',
          component: () =>
            import('@/views/shopPing/shopManage/editShop/index.vue'),
          name: 'editShop',
          meta: {
            title: '新建/编辑商品',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'commodityPool',
          component: () =>
            import('@/views/shopPing/shopManage/commodityPool/index.vue'),
          name: 'commodityPool',
          meta: {
            title: '淘宝客商品池',
            icon: 'list',
            noCache: true,
            menuId: 'S1140102'
          }
        },
        {
          path: 'editCommodity',
          component: () =>
            import('@/views/shopPing/shopManage/editCommodity/index.vue'),
          name: 'editCommodity',
          meta: {
            title: '导入商品',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'editSku',
          component: () =>
            import('@/views/shopPing/shopManage/editSku/index.vue'),
          name: 'editSku',
          meta: {
            title: '子商品分类详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'jdShopManageList',
          component: () =>
            import('@/views/shopPing/shopManage/jdShopManageList/index.vue'),
          name: 'jdShopManageList',
          meta: {
            title: '京东商品列表',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'jdSukidList',
          component: () =>
            import('@/views/shopPing/shopManage/jdShopManageList/sku-list.vue'),
          name: 'jdSukidList',
          meta: {
            title: '京东sukid列表',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        }
      ]
    },
    {
      path: 'shopClassification',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '商品分类管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11402'
      },
      children: [
        {
          path: 'shopClassificationManage',
          component: () =>
            import(
              '@/views/shopPing/shopClassification/shopClassificationManage/index.vue'
            ),
          name: 'shopClassificationManage',
          meta: {
            title: '平台商品分类',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'brandClassification',
          component: () =>
            import(
              '@/views/shopPing/shopClassification/brandClassification/index.vue'
            ),
          name: 'brandClassification',
          meta: {
            title: '品牌分类',
            icon: 'list',
            noCache: true,
            menuId: 'S1140202'
          }
        }
      ]
    },
    {
      path: 'shopOperate',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '运营管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11403'
      },
      children: [
        {
          path: 'shopOperateManage',
          component: () =>
            import('@/views/shopPing/shopOperate/shopOperateManage/index.vue'),
          name: 'shopOperateManage',
          meta: {
            title: '精选配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140301'
          }
        },
        {
          path: 'shopNavigation',
          component: () =>
            import('@/views/shopPing/shopOperate/shopNavigation/index.vue'),
          name: 'shopNavigation',
          meta: {
            title: '首页导航栏配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140302'
          }
        },
        {
          path: 'editShopNavigation',
          component: () =>
            import('@/views/shopPing/shopOperate/editShopNavigation/index.vue'),
          name: 'editShopNavigation',
          meta: {
            title: '添加首页导航栏配置',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'platformSelectionLibrary',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/platformSelectionLibrary/index.vue'
            ),
          name: 'platformSelectionLibrary',
          meta: {
            title: '平台选品库',
            icon: 'list',
            noCache: true,
            menuId: 'S1140303'
          }
        },
        {
          path: 'editSelectionLibrary',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/editSelectionLibrary/index.vue'
            ),
          name: 'editSelectionLibrary',
          meta: {
            title: '添加/管理平台选品库',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'operationDiamond',
          component: () =>
            import('@/views/shopPing/shopOperate/operationDiamond/index.vue'),
          name: 'operationDiamond',
          meta: {
            title: '运营金刚位配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140304'
          }
        },
        {
          path: 'activePageConfiguration',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/activePageConfiguration/index.vue'
            ),
          name: 'activePageConfiguration',
          meta: {
            title: 'H5活动页面配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140305'
          }
        },
        {
          path: 'energyActivityAllocation',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/energyActivityAllocation/index.vue'
            ),
          name: 'energyActivityAllocation',
          meta: {
            title: '能量活动配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140313'
          }
        },
        {
          path: 'activeConfiguration',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/activeConfiguration/index.vue'
            ),
          name: 'activeConfiguration',
          meta: {
            title: '活动配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140306'
          }
        },
        {
          path: 'customerServiceWeChat',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/customerServiceWeChat/index.vue'
            ),
          name: 'customerServiceWeChat',
          meta: {
            title: '客服微信号管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1140307'
          }
        },
        {
          path: 'platformLabelProductLibrary',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/platformLabelProductLibrary/index.vue'
            ),
          name: 'platformLabelProductLibrary',
          meta: {
            title: '平台标品库',
            icon: 'list',
            noCache: true,
            menuId: 'S1140308'
          }
        },
        {
          path: 'editPlatformLabelProductLibrary',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/editPlatformLabelProductLibrary/index.vue'
            ),
          name: 'editPlatformLabelProductLibrary',
          meta: {
            title: '添加/管理平台标品库',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'shopBackground',
          component: () =>
            import('@/views/shopPing/shopOperate/shopBackground/index.vue'),
          name: 'shopBackground',
          meta: {
            title: '商城背景配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140309'
          }
        },
        {
          path: 'secondsKillActivity',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/secondsKillActivity/index.vue'
            ),
          name: 'secondsKillActivity',
          meta: {
            title: '秒杀活动列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140310'
          }
        },
        {
          path: 'interest-free-activity-allocation',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/interestFreeActivityAllocation/index.vue'
            ),
          name: 'interest-free-activity-allocation',
          meta: {
            title: '免息活动配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140318'
          }
        },
        {
          path: 'replaceSendShopList',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/replaceSendShopList/index.vue'
            ),
          name: 'replaceSendShopList',
          meta: {
            title: '代发商家维护列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140311'
          }
        },
        {
          path: 'priceAdjustmentConfig',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/price-adjustment-config/index.vue'
            ),
          name: 'priceAdjustmentConfig',
          meta: {
            title: '批量调价配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140312'
          }
        },
        {
          path: 'activityPicSetting',
          component: () =>
            import('@/views/shopPing/shopOperate/activityPicSetting/index.vue'),
          name: 'activityPicSetting',
          meta: {
            title: '活动图标配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140314'
          }
        },
        {
          path: 'goodsCommentManagement',
          component: () =>
            import(
              '@/views/shopPing/shopOperate/goodsCommentManagement/index.vue'
            ),
          name: 'activityPicSetting',
          meta: {
            title: '商品评论管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1140315'
          }
        },
        {
          path: 'goodsOrderBlack',
          component: () =>
            import('@/views/shopPing/shopOperate/goodsOrderBlack/index.vue'),
          name: 'goodsOrderBlack',
          meta: {
            title: '下单黑名单',
            icon: 'list',
            noCache: true,
            menuId: 'S1140316'
          }
        },
        {
          path: 'goodsGift',
          component: () =>
            import('@/views/shopPing/shopOperate/goodsGift/index.vue'),
          name: 'goodsGift',
          meta: {
            title: '赠品配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140317'
          }
        }
      ]
    },
    {
      path: 'operatingManage',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '店铺管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11406'
      },
      children: [
        {
          path: 'operatingManageList',
          component: () =>
            import('@/views/shopPing/operatingManage/list/index.vue'),
          name: 'operatingManageList',
          meta: {
            title: '店铺列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140601'
          }
        }
      ]
    },
    {
      path: 'energySettlement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '能量结算',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11404'
      },
      children: [
        {
          path: 'energySettlement',
          component: () =>
            import(
              '@/views/shopPing/energySettlement/energySettlement/index.vue'
            ),
          name: 'energySettlement',
          meta: {
            title: '能量结算表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140401'
          }
        }
      ]
    },
    {
      path: 'returnSettlement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '订单管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11405'
      },
      children: [
        {
          path: 'returnSettlementOrder',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/returnSettlementOrder/index.vue'
            ),
          name: 'returnSettlementOrder',
          meta: {
            title: '淘宝补贴结算订单',
            icon: 'list',
            noCache: true,
            menuId: 'S1140501'
          }
        },
        {
          path: 'userAccountBalance',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/userAccountBalance/index.vue'
            ),
          name: 'userAccountBalance',
          meta: {
            title: '用户账户余额',
            icon: 'list',
            noCache: true,
            menuId: 'S1140502'
          }
        },
        {
          path: 'jdSettlementOrder',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/jdSettlementOrder/index.vue'
            ),
          name: 'jdSettlementOrder',
          meta: {
            title: '京东补贴结算订单',
            icon: 'list',
            noCache: true,
            menuId: 'S1140503'
          }
        },
        {
          path: 'proprietaryCOrderList',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/proprietaryOrderList/index.vue'
            ),
          name: 'proprietaryCOrderList',
          meta: {
            title: '自营商品订单',
            icon: 'list',
            noCache: true,
            menuId: 'S1140504'
          }
        },
        {
          path: 'guaranteeList',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/proprietaryOrderList/guaranteeList.vue'
            ),
          name: 'guaranteeList',
          meta: {
            title: '退货宝退款列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140508'
          }
        },
        {
          path: 'proprietaryCOrderDetail',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/proprietaryOrderList/detail.vue'
            ),
          name: 'proprietaryCOrderDetail',
          meta: {
            title: '自营商品订单详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'proprietaryCRefundSalesList',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/proprietaryOrderList/refundSalesList.vue'
            ),
          name: 'proprietaryCRefundSalesList',
          meta: {
            title: '退款审核列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140505'
          }
        },
        {
          path: 'afterSalesList',
          component: () =>
            import(
              '@/views/shopPing/returnSettlement/afterSalesList/index.vue'
            ),
          name: 'afterSalesList',
          meta: {
            title: '售后列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140506'
          }
        },
        {
          path: 'rechargeList',
          component: () =>
            import('@/views/shopPing/returnSettlement/rechargeList/index.vue'),
          name: 'rechargeList',
          meta: {
            title: '话费订单列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1140507'
          }
        }
      ]
    },
    {
      path: 'couponManagement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '优惠券管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11407'
      },
      children: [
        {
          path: 'couponConfig',
          component: () =>
            import('@/views/shopPing/couponManagement/couponConfig/index.vue'),
          name: 'CouponConfig',
          meta: {
            title: '优惠券配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1140701'
          }
        },
        {
          path: 'couponProvide',
          component: () =>
            import('@/views/shopPing/couponManagement/couponProvide/index.vue'),
          name: 'CouponProvide',
          meta: {
            title: '优惠券发放',
            icon: 'list',
            noCache: true,
            menuId: 'S1140702'
          }
        },
        {
          path: 'couponUseDetail',
          component: () =>
            import(
              '@/views/shopPing/couponManagement/couponUseDetail/index.vue'
            ),
          name: 'CouponUseDetail',
          meta: {
            title: '券使用明细',
            icon: 'list',
            noCache: true,
            menuId: 'S1140703'
          }
        }
      ]
    }
  ]
}

export default content
