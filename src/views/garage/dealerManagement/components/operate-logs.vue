<template>
  <el-table
    :data="logs"
    v-loading="loading"
    height="480"
    stripe
    border
    style="width: 100%; overflow-y: auto"
  >
    <el-table-column
      prop="penaltyInfoVo.businessType"
      label="业务"
      align="center"
      width="180px"
    >
      <template v-slot="scope">
        <span>{{
          formatBusinessType_filter(scope.row.penaltyInfoVo.businessType)
        }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="operateType" width="80" label="操作" align="center">
      <template v-slot="scope">
        <span>{{
          scope.row.operateType === ''
            ? ''
            : scope.row.operateType === '1'
            ? '开启'
            : '关闭'
        }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="remark" label="处罚原因" />
    <el-table-column
      prop="operateDate"
      label="操作时间"
      align="center"
      width="160px"
    >
    </el-table-column>
    <el-table-column prop="userName" width="120" label="操作人" align="center">
      <template v-slot="scope">
        <span>{{ scope.row.userName }}</span>
        <el-button
          v-if="
            scope.row.userName && /^\d{3}.{4}\d{4}$/.test(scope.row.userName)
          "
          size="small"
          style="margin-left: 10px"
          @click="seeMobile(scope.row.userName)"
          >查看电话</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    style="
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
      margin-right: 100px;
    "
    v-model:current-page="params.page"
    :page-sizes="[10, 20, 50]"
    :total="total"
    background
    :page-size="params.limit"
    layout="total,prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="currentChange"
  />
  <ChooseSeePhone ref="seePhone" />
</template>

<script>
import { mapGetters } from 'vuex'
import { getOperateLog } from '@/api/garage'
import { paymentStatus, payCategory } from '@/utils/enum'
import { uidEnum } from '@/utils/enum/uid'
import { convertKeyValueEnum } from '@/utils/convert'
import ChooseSeePhone from '@/components/Dialog/ChooseSeePhone.vue'

export default {
  name: 'OperateLogs',
  props: {
    shopId: {
      type: Number,
      default: 0
    }
  },
  components: {
    ChooseSeePhone
  },
  data() {
    return {
      dialogVisible: false,
      loading: true,
      uidEnum,
      logs: [],
      total: 0,
      convertPaymentStatus: convertKeyValueEnum(paymentStatus), // 订单状态
      alreadyChoose: [], // 已选择的升级订单
      convertPayCategory: convertKeyValueEnum(payCategory),
      refundDialog: false, // 退款弹框
      refundForm: {
        id: '', // id
        price: '' // 退款金额
      },
      params: {
        limit: 10,
        page: 1
      }
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  methods: {
    formatBusinessType_filter(type) {
      const enums = {
        1: '询价客户',
        2: '试驾客户',
        3: '电话线索',
        4: '直通车',
        5: '一口价',
        6: '二手车',
        7: '店铺活动',
        8: '驾校报名',
        9: '租车业务',
        10: '维修',
        11: '救援',
        12: '加油站',
        13: '售新车',
        14: '微信线索',
        15: '广告营销',
        16: '直播售车',
        17: '投诉管理',
        18: '二手车电话线索',
        19: '二手车微信线索',
        20: '二手车店铺活动',
        21: '驾考店铺活动',
        22: '分期(售新车)',
        23: '分期(二手车)',
        24: '收车',
        28: '询价(二手车)',
        188: '人员管理' // 特殊类型
      }
      return enums[type]
    },
    handleSizeChange(limit) {
      this.params.limit = limit
      this.getPayLogs()
    },
    currentChange(page) {
      this.params.page = page
      this.getPayLogs()
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({ mobile: mobile, source: 'decode-mobile' })
    },
    // 外部调用 初始化
    init() {
      const me = this
      me.ready()
    },
    ready() {
      const me = this
      me.alreadyChoose = []
      me.getPayLogs()
    },
    // 获取付费记录
    getPayLogs() {
      const me = this
      getOperateLog({ shopId: me.shopId, ...this.params })
        .then((response) => {
          if (response.data.code === 0) {
            this.loading = false
            me.logs = response.data.data.list
            this.total = response.data.data.total
          }
        })
        .catch((err) => {
          this.loading = false
          me.$message.error(`${err.data.message}` || '获取失败')
        })
    },
    handleRefundClose() {
      this.refundDialog = false
    },
    // 确认升级组件传来信息
    closeView(paySuccess) {
      const me = this
      setTimeout(() => {
        me.ready()
      }, 1000)
    }
  }
}
</script>

<style lang="scss">
.selected {
  color: #00ff66;
  cursor: pointer;
}
.no-selected {
  color: #cccccc;
  cursor: pointer;
}
.is-selected {
  color: #ffcc00;
  cursor: pointer;
}
</style>
