<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="100px"
      class="detail-entry"
    >
      <div class="content">
        <div class="content-title">
          <span>店铺资料</span>
          <el-button
            v-if="ruleForm.applyStatus === 1"
            type="primary"
            link
            disabled
            >已审核通过</el-button
          >
          <!-- <el-button
            v-if="ruleForm.riskApplyStatus === 1"
            type="primary"
            link
            disabled
            >初审通过</el-button
          > -->
          <el-button
            v-if="ruleForm.applyStatus === 2"
            type="primary"
            link
            disabled
            >审核不通过</el-button
          >
        </div>
        <div class="content-content">
          <el-form-item
            :class="{
              red: original && ruleForm.shopName !== original.shopName
            }"
            label="店铺名称"
            prop="shopName"
          >
            <div class="wd-flex wd-items-start">
              <el-input
                v-focus
                v-model="ruleForm.shopName"
                type="text"
                style="width: 180px"
                maxlength="'30'"
                class="wd-flex-shrink-0 wd-mr-10px"
              />
              <div style="line-height: 15px">
                <div>{{ shopDays }}天，使用{{ changeNameCount }}个店铺名称</div>
                <div v-if="shopNameList" class="wd-mt-5px">
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="shopNameList"
                    placement="top-start"
                  >
                    <div class="wd-flex">
                      <span class="wd-mr-5px wd-flex-shrink-0">名称列表:</span>
                      <span class="wd-line-clamp-3">{{ shopNameList }}</span>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </el-form-item>
          <!-- <el-form-item label="服务类型" class="service-list">
                          <el-checkbox v-for="(s, i) in serviceList" :key="i" v-model="s.selected" @change="changePackname(s.selected, s.values)">{{
                            s.values
                          }}</el-checkbox>
                        </el-form-item> -->

          <el-form-item label="店铺号码">
            <el-input
              v-focus
              v-model="ruleForm.mobile"
              maxlength="'20'"
              style="width: 180px"
            />
            <el-button
              v-if="ruleForm.mobile"
              type="primary"
              size="small"
              @click="seeMobile(ruleForm.mobile, 1)"
              >查看号码</el-button
            >
          </el-form-item>
          <!-- <el-form-item label="联系方式" prop="virtualMobile">
                          <el-input v-focus v-model="ruleForm.virtualMobile" maxlength="'20'" style="width: 180px" />
                          <el-button v-if="ruleForm.virtualMobile" type="primary" size="small" @click="seeMobile(ruleForm.virtualMobile, 1)">查看号码</el-button>
                        </el-form-item> -->
          <!-- <el-form-item :class="{ red: original && ruleForm.telephone !== original.telephone }" label="固定电话">
                          <el-input v-focus v-model="ruleForm.telephone" maxlength="'20'" />
                        </el-form-item> -->
          <!-- <el-form-item :class="{ red: original && ruleForm.wechat !== original.wechat }" label="微信号">
                          <el-input v-focus v-model="ruleForm.wechat" maxlength="'20'" style="width: 280px" />
                          <el-button v-if="ruleForm.wechat" type="primary" size="small" @click="seeMobile(ruleForm.wechat, 2)">查看微信号</el-button>
                        </el-form-item> -->
          <el-form-item
            :class="{
              red: original && ruleForm.cityAddress !== original.cityAddress
            }"
            label="所属地区"
            prop="cityAddress"
          >
            <div @click="getRef('area')">
              <el-input v-focus v-model="ruleForm.cityAddress" type="text" />
            </div>
          </el-form-item>
          <el-form-item
            :class="{
              red: original && ruleForm.shopAddress !== original.shopAddress
            }"
            label="详细地址"
            prop="shopAddress"
          >
            <el-input v-focus v-model="ruleForm.shopAddress" type="text" />
          </el-form-item>
          <el-form-item
            :class="{
              red:
                original &&
                ruleForm.locationAddress !== original.locationAddress
            }"
            label="校对定位"
            prop="locationAddress"
          >
            <div @click="showPostion">
              <el-input
                v-focus
                v-model="ruleForm.locationAddress"
                readonly
                type="text"
              />
            </div>
          </el-form-item>
          <!-- <el-form-item label="角色" prop="shopTagList">
            <el-select
              v-model="ruleForm.shopTagList"
              multiple
              placeholder="请选择"
              style="width: 400px"
            >
              <el-option
                v-for="(value, index) in labelTitle"
                :key="index"
                :label="value.name"
                :value="value.code"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="座机电话">
            <el-input v-focus v-model="ruleForm.telephone" type="text" />
          </el-form-item>
          <!-- <el-form-item :class="{ red: original && ruleForm.shopArea !== original.shopArea }" label="店铺面积">
                          <el-input v-focus v-model="ruleForm.shopArea" maxlength="'20'" />
                        </el-form-item> -->
          <!-- <el-form-item :class="{ red: original && ruleForm.openTime !== original.openTime }" label="开店时间">
                          <el-date-picker v-model="ruleForm.openTime" format="YYYY-MM-DD" type="datetime" value-format="x" placeholder="选择日期时间" />
                        </el-form-item> -->
          <el-form-item label="店铺门头">
            <div>
              <el-upload
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="updateOutSideImg"
                accept=".jpg, .png"
                style="display: inline-block"
                class="avatar-uploader"
                action
              >
                <el-button type="primary">修改</el-button>
              </el-upload>
              <div v-if="ruleForm.outsideImage">
                <el-image
                  :src="ruleForm.outsideImage"
                  :class="{ 'small-img-content': true }"
                  class="img-content"
                  fit="cover"
                  @click="seeBigImg(ruleForm.outsideImage)"
                >
                  <template v-slot:placeholder>
                    <div class="image-slot">
                      加载中
                      <span class="dot">...</span>
                    </div>
                  </template>
                </el-image>
              </div>
              <div v-else>无图片</div>
            </div>
          </el-form-item>
          <el-form-item label="店内环境">
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :before-upload="beforeInsideImgUpload"
              :on-success="updateInsideImg"
              :limit="18"
              multiple
              accept=".jpg, .png"
              style="display: inline-block"
              class="avatar-uploader"
              action
            >
              <el-button type="primary">新增</el-button>
            </el-upload>
            <br />
            <div v-if="ruleForm.insideImage">
              <div
                v-for="(imageUrl, index) in ruleForm.insideImage
                  .split(',')
                  .filter((imageUrl) => imageUrl)"
                :key="index"
                class="img-content"
              >
                <el-image
                  :src="imageUrl"
                  :class="{ 'small-img-content': true }"
                  fit="cover"
                  @click="seeBigImg(imageUrl)"
                >
                  <template v-slot:placeholder>
                    <div class="image-slot">
                      加载中
                      <span class="dot">...</span>
                    </div>
                  </template>
                </el-image>
                <div
                  @click="deleteInsideImg(imageUrl)"
                  style="display: flex; justify-content: center"
                >
                  <el-icon
                    style="
                      cursor: pointer;
                      display: block;
                      text-align: center;
                      margin-top: 10px;
                    "
                    ><IconDelete
                  /></el-icon>
                  <span class="ml5">删除</span>
                </div>
              </div>
            </div>
            <div v-else>无图片</div>
          </el-form-item>
          <div style="margin: 20px" v-if="ruleForm.applyStatus === 0">
            <template
              v-if="ruleForm.riskApplyStatus === 1 && [36].includes(uid)"
            >
              <el-button type="success" @click="confirm">终审通过</el-button>
              <el-button type="danger" @click="$refs.rejectNotice.init('shop')"
                >不通过</el-button
              >
            </template>
            <template
              v-if="ruleForm.riskApplyStatus === 0 && ruleForm.riskTransRegist"
            >
              <el-button type="success" @click="confirm">初审通过</el-button>
              <el-button type="danger" @click="$refs.rejectNotice.init('shop')"
                >不通过</el-button
              >
            </template>
            <template v-if="!ruleForm.riskTransRegist">
              <el-button type="success" @click="confirm">审核通过</el-button>
              <el-button type="danger" @click="$refs.rejectNotice.init('shop')"
                >不通过</el-button
              >
            </template>
          </div>
        </div>
      </div>
    </el-form>
    <c-m-position ref="cPosition" @mapData="mapPosition" />
    <c-area ref="cArea" @backArea="backArea" />
    <choose-show-image ref="showImage" />
    <ChooseSeePhoneNew ref="seePhone" />
    <reject-notice ref="rejectNotice" @confirmRejection="confirmRejection" />
    <RegisterDialog @sucess="sucess" ref="RegisterDialog"></RegisterDialog>
  </div>
</template>

<script>
import { Delete as IconDelete } from '@element-plus/icons-vue'
import CArea from '@/components/area/c-area.vue'
import cMPosition from '@/components/map/c-m-position-new.vue'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import ChooseSeePhoneNew from '@/components/Dialog/ChooseSeePhoneNew.vue'
import RejectNotice from '@/components/Notice/rejectNotice.vue'
import RegisterDialog from '@/components/Dialog/registerDialog.vue'
import { mapGetters } from 'vuex'
import { serviceListV2, labelTitle } from '@/utils/enum'
import { laterPickerOptions } from '@/utils/configData'
import { ShopApplyAgreeV2, ShopApplyUnagreeV2 } from '@/api/garage'
import { matchChiEng } from '@/utils/validate'
import { deepCopy } from '@/utils'

export default {
  components: {
    CArea,
    cMPosition,
    ChooseShowImage,
    ChooseSeePhoneNew,
    RejectNotice,
    RegisterDialog,
    IconDelete
  },
  name: 'EditDetailsV2',
  data() {
    return {
      pickerOptions: laterPickerOptions,
      mapStatus: false, // 地图是否显示
      showMap: false,
      isAboutStatus: false, // 是否是左右布局
      isEditStatus: false, // 是否是不可编辑状态
      serviceList: [], // 服务类型列表
      original: null, // 原申请信息
      data: {}, // 临时存储数据
      labelTitle: labelTitle, // 标签
      ruleForm: {
        // 申请信息
        uid: '', // 用户id
        shopName: '', // 经销商名称
        // telephone: '', // 经销商座机
        wechat: '', // 微信号
        provinceName: '', // 省
        cityName: '', // 市
        districtName: '', // 区
        shopAddress: '', // 详细地址
        locationAddress: '', // 校对定位
        longitude: '', // 经度
        latitude: '', // 纬度
        // shopAttr: '', // 服务类型
        outsideImage: '', // 封面门头图片
        insideImage: '', // 店内环境
        cityAddress: '', // 省市区地址
        // shopArea: '', // 店铺面积
        // openTime: '', // 开店时间
        shopTags: '', // 标签
        telephone: '', // 座机电话
        // shopTagList: [], // 标签相关
        virtualMobile: '', // 虚拟号码
        shopDays: '',
        changeNameCount: '',
        shopNameList: ''
      },
      oldRuleForm: {},
      rules: {
        shopName: [
          { required: true, message: '请输入店铺名称', trigger: 'blur' }
        ],
        cityAddress: [
          { required: true, message: '请选择所属地区', trigger: 'blur' }
        ],
        shopAddress: [
          { required: true, message: '请输入详细地址', trigger: 'blur' }
        ],
        locationAddress: [
          { required: true, message: '请选择校对定位', trigger: 'submit' }
        ]
        // shopTagList: [
        //   { required: true, message: '请至少选择一个标签', trigger: 'change' }
        // ]
        // virtualMobile: [{ required: true, message: '请输入联系方式', trigger: 'blur' }]
      } // 验证信息
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  methods: {
    async httpRequest(option) {
      this.$oss.ossUploadImage(option)
    },
    init(apply, original, shopDays, changeNameCount, shopNameList) {
      console.log(shopNameList, 'shopNameList=======')
      this.shopDays = shopDays || 1
      this.changeNameCount = changeNameCount || 1
      this.shopNameList = shopNameList || ''
      if (!matchChiEng(apply.shopName)) {
        this.$message.info('商家名称仅支持中英文及大小写，不可包含特殊符号!')
      }
      this.setData(apply, original)
      this.isAboutStatus = apply && original
      this.$refs['ruleForm'].resetFields() // 重置验证
    },
    // 设置数据
    setData(data, original) {
      const me = this
      me.ruleForm = data || {}
      me.oldRuleForm = deepCopy(data) || {}
      me.original = original
      // if (me.ruleForm.shopTagList) {
      //   me.ruleForm.shopTagList = me.ruleForm.shopTagList.map((res) => {
      //     return res.code && Number(res.code)
      //   }) // 标签相关
      // }
      me.ruleForm.cityAddress = `${me.ruleForm.provinceName}${me.ruleForm.cityName}${me.ruleForm.districtName}` // 省市区地址
      const shopAttrArr = data.shopAttr.split(',')
      this.serviceList = serviceListV2.map((service) => {
        const item = {
          ...service
        }
        if (shopAttrArr.includes(service.code)) {
          item.selected = true
        } else {
          item.selected = false
        }
        return item
      })
    },
    // 上传门头
    updateOutSideImg(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        const imgOrgUrl = res.imgOrgUrl
        this.ruleForm.outsideImage = imgOrgUrl
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },

    updateInsideImg(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        const imgOrgUrl = res.imgOrgUrl
        const imgArr = this.ruleForm.insideImage
          .split(',')
          .filter((imageUrl) => imageUrl)
        if (imgArr.length < 18) {
          this.ruleForm.insideImage += ',' + imgOrgUrl
        }
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },

    beforeInsideImgUpload() {
      const imgArr = this.ruleForm.insideImage
        .split(',')
        .filter((imageUrl) => imageUrl)
      if (imgArr.length >= 18) {
        this.$message.warning('店内环境照片最多上传18张')
        return false
      }
    },
    deleteInsideImg(imageUrl) {
      const imgArr = this.ruleForm.insideImage
        .split(',')
        .filter((imageUrl) => imageUrl)
      const delIndex = imgArr.findIndex((img) => img === imageUrl)
      imgArr.splice(delIndex, 1)
      this.ruleForm.insideImage = imgArr.join(',')
    },
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(link)
    },
    changePackname() {
      const data = []
      this.serviceList.map((value) => {
        if (value.selected) {
          data.push(value.code)
        }
      })
      this.ruleForm.shopAttr = data.join(',')
    },
    // 展示地图
    showPostion() {
      this.$refs.cPosition.initPostion(
        [this.ruleForm.longitude, this.ruleForm.latitude],
        this.ruleForm.cityAddress + this.ruleForm.shopAddress
      )
    },
    // 位置信息
    mapPosition() {
      const me = this
      const address = JSON.parse(localStorage.position || '{}')
      me.ruleForm.locationAddress = address.address
      me.ruleForm.latitude = address.lat
      me.ruleForm.longitude = address.lng
    },
    // 点击不通过或者获取位置
    getRef() {
      if (this.isEditStatus) {
        return
      }
      this.$refs.cArea.setArea(
        this.ruleForm.provinceName,
        this.ruleForm.cityName,
        this.ruleForm.districtName
      )
      this.$refs.cArea.init('cache')
    },
    // 返回的地址
    backArea(data) {
      const me = this
      me.ruleForm.provinceName = data.provinceName
      me.ruleForm.cityName = data.cityName
      me.ruleForm.districtName = data.distName
      me.ruleForm.cityAddress =
        data.provinceName + data.cityName + data.distName
      const templateObject = me.ruleForm
      me.ruleForm = []
      me.ruleForm = templateObject
    },
    // 通过
    confirm() {
      const me = this
      // if (!me.ruleForm.shopAttr) {
      //   me.$message.error('请选择服务类型')
      //   return
      // }
      // if (me.ruleForm.telephone && !/\d+-\d+/.test(me.ruleForm.telephone)) {
      //   me.$message.error('固定电话区号和电话号码之前请用“-”隔开')
      //   return
      // }
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          // 需要检测是否注册信息逻辑
          if (me.oldRuleForm.mobile !== me.ruleForm.mobile) {
            this.$refs.RegisterDialog.init({
              phone: me.ruleForm.mobile
            })
          } else {
            me.saveShopApplyAgreeV2()
          }
        } else {
          window.scrollTo(0, 200)
        }
      })
    },
    // 通过
    saveShopApplyAgreeV2() {
      const me = this
      ShopApplyAgreeV2({
        id: me.$route.query.id,
        ...me.ruleForm
        // shopTags: me.ruleForm.shopTagList.join(',') // 标签相关
      })
        .then(() => {
          me.ruleForm.applyStatus = 1
          me.isEditStatus = true
          me.$router.push({
            name: 'entryAuditV2'
          })
        })
        .catch(() => {})
    },
    // 确认拒绝
    confirmRejection(mes) {
      const me = this
      ShopApplyUnagreeV2({
        id: me.$route.query.id,
        ...this.ruleForm,
        refuseReason: mes
      })
        .then(() => {
          me.ruleForm.applyStatus = 2
          me.isEditStatus = true
          me.$router.push({
            name: 'entryAuditV2'
          })
        })
        .catch(() => {})
    },
    // 查看手机号码
    seeMobile(mobile, type) {
      const data = {
        decryptContent: mobile,
        source: 'detailsEntry',
        type, // 1手机号解密，2微信号解密
        logModule: 19, // 19经销商资料审核详情
        shopStatus: '' // 1为用户，商家可以传空
      }
      this.$refs.seePhone && this.$refs.seePhone.init(data)
    },
    // 注册成功
    sucess() {
      this.saveShopApplyAgreeV2()
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-entry {
  padding: 10px;
  border-radius: 5px;
  .content {
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    margin-top: 20px;
    .content-title {
      padding: 10px;
      margin: 0;
      border-bottom: 1px solid #dcdfe6;
    }
    .content-content {
      padding-top: 10px;
      .el-form-item {
        // width: 500px;
        display: flex;
        position: relative;
        vertical-align: top;
      }
      .service-list {
        width: auto;
        // .el-checkbox:nth-of-type(1){
        //   margin-left: 30px;
        // }
      }
      .img-content {
        margin-top: 10px;
        overflow: hidden;
        cursor: pointer;
        display: inline-block;
      }
      .small-img-content {
        height: 120px;
        width: 120px;
        margin-right: 5px;
      }
      .see-more-img {
        position: relative;
        top: 50px;
        left: 90px;
      }
    }
  }
  .image-info {
    .el-row {
      margin-bottom: 100px;
    }
  }
  .el-row {
    padding: 20px;
  }
}
</style>
