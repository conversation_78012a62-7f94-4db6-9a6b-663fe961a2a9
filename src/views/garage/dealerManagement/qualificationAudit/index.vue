<template>
  <div v-loading="loading">
    <el-form
      ref="form"
      :model="form"
      :inline="true"
      label-width="80px"
      style="margin: 10px 0"
      @submit.prevent
    >
      <el-form-item label="用户名">
        <el-input
          v-model="form.userName"
          placeholder="请输入用户名"
          clearable
          style="width: 200px"
          @keyup.enter="search"
        />
      </el-form-item>
      <el-form-item label="商家名称">
        <el-select
          v-model="form.shopId"
          :remote-method="remoteMethodShop"
          :loading="searchLoading"
          placeholder="请输入经销商名称"
          filterable
          remote
          clearable
          style="width: 200px"
          @clear="clearShopName"
        >
          <el-option
            v-for="item in shopSearchList"
            :key="item.shopId"
            :label="item.name"
            :value="item.shopId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="经销商ID">
        <el-input
          @input="inputShop"
          v-model="shopId"
          placeholder="请输入经销商ID"
          clearable
          style="width: 200px"
          @keyup.enter="search"
        />
      </el-form-item>
      <el-form-item label="统一社会信用代码" label-width="auto">
        <el-input
          v-model="form.registrationNumber"
          placeholder="请输入统一社会信用代码"
          clearable
          style="width: 200px"
          @keyup.enter="search"
        />
      </el-form-item>
      <el-form-item label="状态" label-width="auto">
        <el-select v-model="form.applyStatus">
          <el-option
            v-for="(label, value) in auditList"
            :key="label"
            :label="label"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="业务">
        <el-select v-model="form.shopAttr" style="width: 150px">
          <el-option
            v-for="label in labelTitle"
            :key="label.code"
            :label="label.name"
            :value="label.code"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="商家角色" prop="shopTagList">
        <el-select
          v-model="form.attrTypes"
          multiple
          placeholder="请选择"
          style="width: 300px"
        >
          <el-option
            v-for="(value, index) in labelTitle"
            :key="index"
            :label="value.name"
            :value="value.code"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="创建时间" label-width="80px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="createDateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 250px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核时间" label-width="80px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="auditDateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 250px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="修改时间" label-width="80px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="modifyDateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 250px"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <div style="padding: 0 20px 20px">
      <el-button type="primary" @click="search">查询</el-button>
      <el-button @click="initGetList">重置</el-button>
      <el-button type="primary" @click="addQualification(1)"
        >新增/编辑公司资质</el-button
      >
      <el-button type="primary" @click="addQualification(2)"
        >新增/编辑个体户资质</el-button
      >
    </div>
    <div style="max-height: 80vh">
      <el-table
        ref="shopList"
        :data="shopList"
        highlight-current-row
        row-key="shopList"
        style="height: 75vh; overflow-y: auto"
        max-height="75vh"
        border
        @row-dblclick="rowDoubleClick"
      >
        <el-table-column prop="shopName" label="经销商ID" align="center">
          <template v-slot="scope">
            <el-button
              type="primary"
              link
              @click="skipShopDetail(scope.row.shopId)"
              >{{ scope.row.shopId }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="uid" label="用户ID" align="center" />
        <el-table-column prop="userName" label="用户名" align="center" />
        <el-table-column prop="shopName" label="商家名称" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.shopName }}</span>
            <span v-if="scope.row.apply === 1" style="color: red"
              >（新申请）</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="registrationName"
          label="营业执照名称"
          align="center"
        />
        <el-table-column
          prop="registrationNumber"
          label="统一社会信用代码"
          align="center"
        />
        <el-table-column prop="applyStatus" label="业务审核状态" align="center">
          <template v-slot="scope">
            <template
              v-if="scope.row.shopAttrJson && scope.row.shopAttrJson.length"
            >
              <div
                v-for="(item, index) in scope.row.shopAttrJson"
                :key="index"
                :class="{
                  'is-text-examine': !['0', '1'].includes(item.attrStatus),
                  'is-text-delete': item.attrStatus == 1
                }"
              >
                {{ labelItem[item.shopAttr] }}:
                {{
                  item.attrStatus == 0
                    ? '审核通过'
                    : item.attrStatus == 1
                    ? '审核不通过'
                    : '申请中'
                }}
              </div>
            </template>
            <div v-else>
              <div class="is-text-examine" v-if="scope.row.applyStatus == 0">
                审请中
              </div>
              <div v-if="scope.row.applyStatus == 1">审核通过</div>
              <div class="is-text-delete" v-if="scope.row.applyStatus == 2">
                审核不通过
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="200" label="审核不通过原因" align="center">
          <template #default="{ row }">
            <el-tooltip
              class="box-item"
              popper-class="reason-popper"
              effect="dark"
              placement="top-start"
            >
              <template #content>
                <div class="reason-content">{{ row.refuseReason }}</div>
              </template>
              <div class="refuseReason">{{ row.refuseReason }}</div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column
          prop="applyRole"
          align="center"
          label="申请角色"
          width="100"
        >
          <template v-slot="scope">
            {{ scope.row.applyRole }}
          </template>
        </el-table-column>
        <el-table-column
          prop="applyTime"
          align="center"
          label="申请时间"
          width="100"
        >
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.applyTime)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="reviewTime"
          align="center"
          label="审核时间"
          width="100"
        >
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.approveTime)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          align="center"
          label="修改时间"
          width="100"
        >
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.updateTime)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="center"
          label="创建时间"
          width="100"
        >
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.createTime)
          }}</template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" align="center" />
        <!-- 加操作权限按钮，只开给伏星羽 -->
        <el-table-column
          v-if="[36, 381, 170].includes(uid)"
          label="操作"
          align="center"
        >
          <template v-slot="scope">
            <el-button
              type="primary"
              v-if="scope.row.applyStatus === 1"
              text
              @click="edit(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="text-align: center; margin-top: 10px"
        @size-change="currentChange"
        @current-change="currentChange"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { ShopQualificationList, GetShopApplyByName } from '@/api/garage'
import { forwardPickerOptions } from '@/utils/configData'
import { labelTitle } from '@/utils/enum'
const labelItem = {
  2: '售新车',
  // 5: '俱乐部',
  // 6: '加油站',
  14: '二手车',
  17: '租车商家',
  18: '驾考',
  19: '直播商家',
  20: '维修救援'
}
export default {
  data() {
    return {
      pickerOptions: forwardPickerOptions,
      loading: false,
      // 所有申请列表
      shopList: [],
      auditList: {
        '': '全部',
        0: '申请中',
        1: '审核通过',
        2: '审核不通过'
      },
      form: {
        userName: '', // 用户名
        shopId: '', // 商家Id
        shopName: '',
        applyType: '', // 申请类型
        registrationNumber: '',
        applyStatus: '', // 状态
        limit: 20, // 数量
        shopStatus: '', // 状态
        beginCreateTime: '', // 创建开始时间 YYYY-MM-DD HH:mm:ss
        endCreateTime: '', // 创建结束时间 YYYY-MM-DD HH:mm:ss
        beginApproveTime: '',
        endApproveTime: '',
        beginUpdateTime: '',
        endUpdateTime: '',
        attrTypes: [] // 商家角色
      },
      // 经销商id
      shopId: '',
      // 页码
      page: 1,
      // 总数
      total: 0,
      searchLoading: false,
      // 模糊查询经销商数据
      shopSearchList: [],
      // 经销商角色
      labelTitle: labelTitle,
      labelItem
    }
  },
  name: 'QualificationAudit',
  components: {},
  computed: {
    ...mapGetters(['uid']),
    createDateRange: {
      get() {
        if (this.form.beginCreateTime && this.form.endCreateTime) {
          return [this.form.beginCreateTime, this.form.endCreateTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.beginCreateTime = value[0]
          this.form.endCreateTime = value[1]
        } else {
          this.form.beginCreateTime = ''
          this.form.endCreateTime = ''
        }
      }
    },
    auditDateRange: {
      get() {
        if (this.form.beginApproveTime && this.form.endApproveTime) {
          return [this.form.beginApproveTime, this.form.endApproveTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.beginApproveTime = value[0]
          this.form.endApproveTime = value[1]
        } else {
          this.form.beginApproveTime = ''
          this.form.endApproveTime = ''
        }
      }
    },
    modifyDateRange: {
      get() {
        if (this.form.beginUpdateTime && this.form.endUpdateTime) {
          return [this.form.beginUpdateTime, this.form.endUpdateTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.beginUpdateTime = value[0]
          this.form.endUpdateTime = value[1]
        } else {
          this.form.beginUpdateTime = ''
          this.form.endUpdateTime = ''
        }
      }
    }
  },
  activated() {
    this.getList({ page: this.page })
  },
  methods: {
    addQualification(type) {
      this.$router.push({
        name: 'DetailsQualification',
        query: {
          type
        }
      })
    },
    edit(data) {
      this.$router.push({
        name: 'DetailsQualification',
        query: {
          id: data.id,
          isEdit: true
        }
      })
    },
    // 跳转经销商详情
    skipShopDetail(shopId) {
      this.$router.push({
        name: 'DistributorDetails',
        query: {
          id: shopId
        }
      })
    },
    // 查询经销商名称索引
    remoteMethodShop(query) {
      this.searchLoading = true
      this.shopId = ''
      this.form.shopName = query
      this.$tools.debounce(this.getShopList, 300)()
    },
    inputShop() {
      this.form.shopId = ''
      this.form.shopName = ''
      this.shopSearchList = []
    },
    // 查询经销商
    getShopList() {
      GetShopApplyByName({
        shopName: this.form.shopName, // 经销商名称
        limit: 100
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.shopSearchList = response.data.data.map((item) => ({
              name: item.shopName,
              shopId: item.shopId
            }))
          }
        })
        .finally(() => {
          this.searchLoading = false
        })
    },
    clearShopName() {
      this.form.shopId = ''
      this.form.shopName = ''
    },
    // 获取列表数据
    getList(paramsObj) {
      const me = this
      const requestParams = {
        ...this.form,
        ...paramsObj
      }
      if (me.shopId) {
        requestParams.shopId = me.shopId
      }
      requestParams.attrTypes =
        (requestParams.attrTypes && requestParams.attrTypes.toString()) || ''
      me.loading = true
      ShopQualificationList(requestParams)
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data

            me.shopList = data.listData
            // 商家角色标签处理
            me.shopList.length > 0 &&
              me.shopList.map((item) => {
                item.applyRole = ''
                if (item.shopAttrApply) {
                  item.shopAttrApply.split(',').map((it) => {
                    if (labelItem[it]) {
                      item.applyRole = item.applyRole + ',' + labelItem[it]
                    }
                  })
                }
                if (item.shopAttrJson) {
                  item.shopAttrJson = JSON.parse(item.shopAttrJson)
                }
                item.applyRole = item.applyRole.slice(1)
              })
            me.total = data.total
            me.page = requestParams.page
          }
        })
        .catch(() => {
          me.loading = false
        })
    },
    // 更新页码
    currentChange(page) {
      this.page = page
      this.getList({
        page: page
      })
    },
    search() {
      this.page = 1
      this.getList({ page: 1 })
    },
    // 重置
    initGetList() {
      this.page = 1
      this.shopId = ''
      this.form = {
        userName: '', // 用户名
        shopId: '', // 商家Id
        shopName: '',
        applyType: '', // 申请类型
        registrationNumber: '',
        applyStatus: '', // 状态
        limit: 20, // 数量
        shopStatus: '', // 状态
        beginCreateTime: '', // 创建开始时间 YYYY-MM-DD HH:mm:ss
        endCreateTime: '', // 创建结束时间 YYYY-MM-DD HH:mm:ss
        beginApproveTime: '',
        endApproveTime: '',
        beginUpdateTime: '',
        endUpdateTime: '',
        attrTypes: []
      }
      this.getList({ page: 1 })
    },
    // 双击点中
    rowDoubleClick(data) {
      this.$router.push({
        name: 'DetailsQualification',
        query: {
          id: data.id
        }
      })
    }
  }
}
</script>
<style scoped>
.refuseReason {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.reason-content {
  width: 400px;
}
</style>
<style></style>
