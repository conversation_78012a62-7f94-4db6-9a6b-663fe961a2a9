<template>
  <div class="shop-order" style="padding: 10px 20px">
    <div>
      <!-- 新车会员订单 -->
      <p
        style="
          margin-top: 20px;
          border-bottom: 1px solid rgb(207, 207, 191);
          color: #b49901;
        "
      >
        会员服务类型
      </p>
      <el-table
        ref="vipOrderList"
        :data="vipOrderList"
        highlight-current-row
        row-key="vipOrderList"
        border
      >
        <el-table-column prop="orderNumber" label="订单号" />
        <el-table-column prop="serviceType" label="会员服务">
          <template v-slot="scope">
            <span>{{ convertShopServiceType[scope.row.serviceType] }}</span
            ><span style="color: red">{{
              scope.row.trial ? '(试用)' : ''
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="shopId" label="经销商ID" />
        <el-table-column prop="shopName" label="经销商名称" width="120px" />
        <el-table-column prop="level" label="会员等级">
          <template v-slot="scope">
            <span>{{ convertMemberLevelEnum[scope.row.level] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="originalPrice" label="应付价格" />
        <el-table-column prop="discount" label="价格折扣" />
        <el-table-column prop="price" label="实付价格" />
        <el-table-column prop label="服务开始时间" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.beginTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop label="服务结束时间" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.deadlineTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否赠送">
          <template v-slot="scope">
            {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="赠送原因" />
        <el-table-column prop="status" label="支付状态">
          <template v-slot="scope">
            <span>{{ convertPaymentStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态">
          <template v-slot="scope">
            <span>{{ convertShopPaymentAuitStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template v-slot="scope">
            <!-- orderInfo.source: {0:"创建(默认)",1:"升级订单",2:"转移订单"} 注： 主订单的类型
            scope.row.source:{0,"创建(默认)",1,"升级子订单",2,"被升级子订单",3,"被转移子订单"} 注：2,"被升级子订单",3,"被转移子订单":是子订单升级和转移后的标识，升级和转移后，子订单不能在被操作-->
            <template
              v-if="
                [3, 18].includes(scope.row.status) &&
                [0, 1].includes(scope.row.source) &&
                scope.row.permitUniteRefundState
              "
            >
              <!-- 升级单：只能作废和升级 -->
              <template v-if="orderInfo.source === 1">
                <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <el-tag
                  effect="plain"
                  type="text"
                  v-if="
                    [1, 2, 3, 4].includes(scope.row.level) && !scope.row.trial
                  "
                  @click="businessUpgrade(scope.row)"
                  >升级</el-tag
                >
              </template>
              <!-- 转移单：只能作废 -->
              <template v-if="orderInfo.source === 2">
                <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <el-tag
                  effect="plain"
                  v-if="!scope.row.beginTime"
                  type="text"
                  @click="editServiceTime(scope.row)"
                >
                  编辑服务时间
                </el-tag>
              </template>
              <!-- 创建单 -->
              <template v-if="orderInfo.source === 0">
                <el-tag
                  effect="plain"
                  v-if="scope.row.price === 0"
                  type="text"
                  @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <template v-if="scope.row.price && scope.row.status === 3">
                  <el-tag effect="plain" type="text" @click="reFund(scope.row)"
                    >退款</el-tag
                  >
                  <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                    >作废</el-tag
                  >
                  <el-tag
                    effect="plain"
                    v-if="!scope.row.beginTime"
                    type="text"
                    @click="editServiceTime(scope.row)"
                  >
                    编辑服务时间
                  </el-tag>

                  <el-tag
                    effect="plain"
                    type="text"
                    @click="businessTransfer(scope.row)"
                    >业务转移</el-tag
                  >
                  <el-tag
                    effect="plain"
                    type="text"
                    v-if="
                      [1, 2, 3, 4].includes(scope.row.level) && !scope.row.trial
                    "
                    @click="businessUpgrade(scope.row)"
                    >升级</el-tag
                  >
                </template>
              </template>
            </template>
            <el-tag
              effect="plain"
              v-if="scope.row.price"
              type="text"
              size="small"
              @click="reFundDesc(scope.row)"
              >退款详情</el-tag
            >
            <el-tag
              effect="plain"
              type="text"
              size="small"
              @click="generateContract(scope.row)"
              >合同生成</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 线索服务订单 -->
      <p
        style="
          margin-top: 20px;
          border-bottom: 1px solid rgb(207, 207, 191);
          color: #b49901;
        "
      >
        线索服务类型
      </p>
      <el-table
        ref="clueOrderList"
        :data="clueOrderList"
        highlight-current-row
        row-key="clueOrderList"
        border
      >
        <el-table-column prop="orderNumber" label="订单号" />
        <el-table-column prop="serviceType" label="线索服务">
          <template v-slot="scope">
            <span>{{ convertShopServiceType[scope.row.serviceType] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="shopId" label="经销商ID" />
        <el-table-column prop="shopName" label="经销商名称" width="120px" />
        <el-table-column
          prop="serviceTypeName"
          label="线索包类型"
          width="100px"
        />
        <el-table-column prop="originalPrice" label="应付价格" />
        <el-table-column prop="discount" label="价格折扣" />
        <el-table-column prop="price" label="实付价格" />
        <el-table-column prop label="年包开始时间" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.beginTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop label="年包结束时间" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.deadlineTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否赠送">
          <template v-slot="scope">
            {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="赠送原因" />
        <el-table-column prop="status" label="支付状态">
          <template v-slot="scope">
            <span>{{ convertPaymentStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态">
          <template v-slot="scope">
            <span>{{ convertShopPaymentAuitStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template v-slot="scope">
            <template
              v-if="
                [3, 18].includes(scope.row.status) &&
                [0, 1].includes(scope.row.source) &&
                scope.row.permitUniteRefundState
              "
            >
              <!-- 升级单：只能作废, 转移单：只能作废-->
              <el-tag
                effect="plain"
                v-if="[1, 2].includes(orderInfo.source)"
                type="text"
                @click="toVoid(scope.row)"
                >作废</el-tag
              >
              <template v-if="orderInfo.source === 0">
                <el-tag
                  effect="plain"
                  v-if="scope.row.price === 0"
                  type="text"
                  @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <template v-if="scope.row.price && scope.row.status === 3">
                  <!-- 补差价订单只允许再次补差价 -->
                  <el-tag
                    v-if="scope.row.serviceType !== 11"
                    effect="plain"
                    type="text"
                    @click="reFund(scope.row, 1)"
                    >退款</el-tag
                  >
                  <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                    >作废</el-tag
                  >
                  <el-tag
                    v-if="scope.row.serviceType !== 11"
                    effect="plain"
                    type="text"
                    @click="businessTransfer(scope.row)"
                    >业务转移</el-tag
                  >
                </template>
              </template>
            </template>
            <!-- <el-tag
              effect="plain"
              type="text"
              v-if="
                (scope.row.source === 4 || scope.row.source === 0) &&
                scope.row.serviceType === 8 &&
                scope.row.status === 3
              "
              @click="businessMakeUpDifference(scope.row)"
              >补差价</el-tag
            > -->
            <el-tag
              effect="plain"
              v-if="scope.row.price && scope.row.serviceType !== 11"
              type="text"
              size="small"
              @click="reFundDesc(scope.row)"
              >退款详情</el-tag
            >
            <!-- 询价 驾考 租车 年包才有 -->
            <el-tag
              effect="plain"
              v-if="[3, 5, 6, 8].includes(scope.row.serviceType)"
              type="text"
              size="small"
              @click="generateContractImt(scope.row)"
              >合同生成</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 微信线索订单 -->
      <p
        style="
          margin-top: 20px;
          border-bottom: 1px solid rgb(207, 207, 191);
          color: #b49901;
        "
      >
        微信服务类型
      </p>
      <el-table
        ref="wechatOrderList"
        :data="wechatOrderList"
        highlight-current-row
        row-key="wechatOrderList"
        border
      >
        <el-table-column prop="orderNumber" label="订单号" />
        <el-table-column prop="serviceType" label="线索服务">
          <template v-slot="scope">
            <span>{{ convertShopServiceType[scope.row.serviceType] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="shopId" label="经销商ID" />
        <el-table-column prop="shopName" label="经销商名称" width="120" />
        <el-table-column prop="settingType" label="配置类型">
          <template v-slot="scope">
            <span>{{ convertWechatSettingsType[scope.row.settingType] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="settingType" label="配置数量">
          <template v-slot="scope">
            <span>{{
              scope.row.settingType === 1 ? scope.row.number : ''
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="配置开始时间" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.beginTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="配置结束时间" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.deadlineTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="originalPrice" label="应付价格" />
        <el-table-column prop="discount" label="价格折扣" />
        <el-table-column prop="price" label="实付价格" />
        <el-table-column label="是否赠送">
          <template v-slot="scope">
            {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="赠送原因" />
        <el-table-column prop="status" label="支付状态">
          <template v-slot="scope">
            <span>{{ convertPaymentStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态">
          <template v-slot="scope">
            <span>{{ convertShopPaymentAuitStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template v-slot="scope">
            <template
              v-if="
                [3, 18].includes(scope.row.status) &&
                [0, 1].includes(scope.row.source) &&
                scope.row.permitUniteRefundState
              "
            >
              <el-tag
                effect="plain"
                v-if="[1, 2].includes(orderInfo.source)"
                type="text"
                @click="toVoid(scope.row)"
                >作废</el-tag
              >
              <template v-if="orderInfo.source === 0">
                <el-tag
                  effect="plain"
                  v-if="scope.row.price === 0"
                  type="text"
                  @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <template v-if="scope.row.price && scope.row.status === 3">
                  <!-- <el-tag
                    effect="plain"
                    type="text"
                    @click="reFund(scope.row, 1)"
                    >退款</el-tag
                  > -->
                  <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                    >作废</el-tag
                  >
                  <!-- <el-tag
                    effect="plain"
                    type="text"
                    @click="businessTransfer(scope.row)"
                    >业务转移</el-tag
                  > -->
                </template>
              </template>
            </template>
            <!-- <el-tag
              effect="plain"
              v-if="scope.row.price"
              type="text"
              size="small"
              @click="reFundDesc(scope.row)"
              >退款详情</el-tag
            > -->
          </template>
        </el-table-column>
      </el-table>
      <!-- 广告订单 -->
      <p
        style="
          margin-top: 20px;
          border-bottom: 1px solid rgb(207, 207, 191);
          color: #b49901;
        "
      >
        广告服务类型
      </p>
      <el-table
        ref="adOrderList"
        :data="adOrderList"
        highlight-current-row
        row-key="adOrderList"
        border
      >
        <el-table-column prop="orderNumber" label="订单号" />
        <el-table-column label="广告服务">
          <template #default>广告服务</template>
        </el-table-column>
        <el-table-column prop="shopId" label="经销商ID" />
        <el-table-column prop="shopName" label="经销商名称" />
        <el-table-column label="投放截止日期">
          <template v-slot="scope">
            <span>
              {{ $dayjs(scope.row.deadlineTime).format('YYYY-MM-DD') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="originalPrice" label="应付价格" />
        <el-table-column prop="discount" label="价格折扣" />
        <el-table-column prop="price" label="实付价格" />
        <el-table-column label="是否赠送">
          <template v-slot="scope">
            {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="赠送原因" />
        <el-table-column prop="status" label="支付状态">
          <template v-slot="scope">
            <span>{{ convertPaymentStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态">
          <template v-slot="scope">
            <span>{{ convertShopPaymentAuitStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template v-slot="scope">
            <template
              v-if="
                [3, 18].includes(scope.row.status) &&
                [0, 1].includes(scope.row.source) &&
                scope.row.permitUniteRefundState &&
                !scope.row.bindAdvert
              "
            >
              <el-tag
                effect="plain"
                v-if="[1, 2].includes(orderInfo.source)"
                type="text"
                @click="toVoid(scope.row)"
                >作废</el-tag
              >
              <template v-if="orderInfo.source === 0">
                <el-tag
                  effect="plain"
                  v-if="scope.row.price === 0"
                  type="text"
                  @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <template v-if="scope.row.price && scope.row.status === 3">
                  <el-tag
                    effect="plain"
                    type="text"
                    @click="reFund(scope.row, 1)"
                    >退款</el-tag
                  >
                  <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                    >作废</el-tag
                  >
                  <!-- <el-tag
                    effect="plain"
                    type="text"
                    @click="businessTransfer(scope.row)"
                    >业务转移</el-tag
                  > -->
                </template>
              </template>
            </template>
            <el-tag
              effect="plain"
              v-if="scope.row.price"
              type="text"
              size="small"
              @click="reFundDesc(scope.row)"
              >退款详情</el-tag
            >
            <el-tag
              effect="plain"
              type="text"
              size="small"
              @click="generateContractImt(scope.row)"
              >合同生成</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 金币订单 -->
      <p
        style="
          margin-top: 20px;
          border-bottom: 1px solid rgb(207, 207, 191);
          color: #b49901;
        "
      >
        金币服务类型
      </p>
      <el-table
        ref="goldOrderList"
        :data="goldOrderList"
        highlight-current-row
        row-key="goldOrderList"
        border
      >
        <el-table-column prop="orderNumber" label="订单号" />
        <el-table-column label="金币服务">
          <template #default>金币服务</template>
        </el-table-column>
        <el-table-column prop="shopId" label="经销商ID" />
        <el-table-column prop="shopName" label="经销商名称" />
        <el-table-column prop="number" label="金币数量" />
        <el-table-column prop="originalPrice" label="应付价格" />
        <el-table-column prop="discount" label="价格折扣" />
        <el-table-column prop="price" label="实付价格" />
        <el-table-column label="是否赠送">
          <template v-slot="scope">
            {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="赠送原因" />
        <el-table-column prop="status" label="支付状态">
          <template v-slot="scope">
            <span>{{ convertPaymentStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态">
          <template v-slot="scope">
            <span>{{ convertShopPaymentAuitStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template v-slot="scope">
            <!-- orderInfo.source: {0:"创建(默认)",1:"升级订单",2:"转移订单"} 注： 主订单的类型
            scope.row.source:{0,"创建(默认)",1,"升级子订单",2,"被升级子订单",3,"被转移子订单"} 注：2,"被升级子订单",3,"被转移子订单":是子订单升级和转移后的标识，升级和转移后，子订单不能在被操作-->
            <template
              v-if="
                [3, 18].includes(scope.row.status) &&
                [0, 1].includes(scope.row.source) &&
                scope.row.permitUniteRefundState
              "
            >
              <!-- 升级单：只能作废和升级 -->
              <template v-if="orderInfo.source === 1">
                <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <el-tag
                  effect="plain"
                  type="text"
                  v-if="
                    [1, 2, 3, 4].includes(scope.row.level) && !scope.row.trial
                  "
                  @click="businessUpgrade(scope.row)"
                  >升级</el-tag
                >
              </template>
              <!-- 转移单：只能作废 -->
              <template v-if="orderInfo.source === 2">
                <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <el-tag
                  effect="plain"
                  v-if="!scope.row.beginTime"
                  type="text"
                  @click="editServiceTime(scope.row)"
                >
                  编辑服务时间
                </el-tag>
              </template>
              <!-- 创建单 -->
              <template v-if="orderInfo.source === 0">
                <el-tag
                  effect="plain"
                  v-if="scope.row.price === 0"
                  type="text"
                  @click="toVoid(scope.row)"
                  >作废</el-tag
                >
                <template v-if="scope.row.price && scope.row.status === 3">
                  <el-tag effect="plain" type="text" @click="reFund(scope.row)"
                    >退款</el-tag
                  >
                  <el-tag effect="plain" type="text" @click="toVoid(scope.row)"
                    >作废</el-tag
                  >
                  <el-tag
                    effect="plain"
                    v-if="!scope.row.beginTime"
                    type="text"
                    @click="editServiceTime(scope.row)"
                  >
                    编辑服务时间
                  </el-tag>

                  <el-tag
                    effect="plain"
                    type="text"
                    @click="businessTransfer(scope.row)"
                    >业务转移</el-tag
                  >
                  <el-tag
                    effect="plain"
                    type="text"
                    v-if="
                      [1, 2, 3, 4].includes(scope.row.level) && !scope.row.trial
                    "
                    @click="businessUpgrade(scope.row)"
                    >升级</el-tag
                  >
                </template>
              </template>
            </template>
            <el-tag
              effect="plain"
              v-if="scope.row.price"
              type="text"
              size="small"
              @click="reFundDesc(scope.row)"
              >退款详情</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 退款Dialog -->
    <el-dialog
      v-model="refundDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
      title="退款"
      width="400px"
    >
      <el-form ref="refundForm" :model="refundForm" :rules="refundRules">
        <el-form-item label="退款金额" prop="price">
          <el-input
            v-model="refundForm.price"
            type="number"
            min="0"
            @change="changed = true"
            style="width: 200px"
            placeholder="请输入退款金额"
          />
        </el-form-item>
        <el-form-item label="订单权益" prop="refundOnlyStatus">
          <el-select
            v-model="refundForm.refundOnlyStatus"
            clearable
            placeholder="请选择订单权益"
            style="width: 200px"
            :disabled="refundOnlyFormClue"
          >
            <el-option
              v-for="(value, index) in refundOnlyStatusType"
              :key="value"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <span>
          <el-button type="primary" @click="confirmRefund">确 定</el-button>
          <el-button @click="refundDialog = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 编辑服务时间Dialog -->
    <el-dialog
      v-model="serviceTimeDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="服务时间"
    >
      <el-form
        ref="serviceTimeForm"
        :model="serviceTimeForm"
        :rules="serviceTimeRules"
      >
        <el-form-item label="服务开始时间" prop="beginTime">
          <el-date-picker
            v-model="serviceTimeForm.beginTime"
            type="date"
            placeholder="请选择服务开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="serverTimeChange"
          />
        </el-form-item>
        <el-form-item label="服务结束时间" prop="endTime">
          <el-date-picker
            disabled
            v-model="serviceTimeForm.endTime"
            type="date"
            placeholder="服务结束时间会自动生成"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <span>
          <el-button type="primary" @click="confirmServiceTime"
            >确 定</el-button
          >
          <el-button @click="serviceTimeDialog = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <ReFundCom ref="reFundCom" @getRefundInfo="getRefundInfo"></ReFundCom>
    <ReFundComDescList ref="reFundComDescList"></ReFundComDescList>
    <PopuContrate ref="popuContrate"></PopuContrate>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ReFundCom from '@/components/ReFund/index.vue'
import ReFundComDescList from '@/components/ReFund/descList.vue'
import PopuContrate from './components/popu-contrate.vue'
import {
  storeCategory,
  memberLevelEnum,
  shopServiceType,
  shopPaymentStatus,
  shopPaymentAuitStatus,
  invoiceTypeAllEnum,
  payCategory,
  shopAuditStatusNew,
  clueType,
  wechatSettingsType,
  refundOnlyStatusType
} from '@/utils/enum'
import {
  getServiceOrderDetail,
  serviceOrderInvalide,
  serviceOrderRefund,
  editMemberExpireTime,
  getPayChannel
} from '@/api/garage'
import { convertKeyValueEnum } from '@/utils/convert'
import { forwardPickerOptions } from '@/utils/configData'
import { getShopContrateExport } from '@/api/garage'

export default {
  name: 'shopOrderDetail',
  components: {
    ReFundCom,
    ReFundComDescList,
    PopuContrate
  },
  data() {
    return {
      orderInfo: {},
      vipOrderList: [], // 会员订单列表
      clueOrderList: [], // 线索订单列表
      wechatOrderList: [], // 微信线索订单列表
      adOrderList: [], // 广告订单列表
      goldOrderList: [], // 金币订单列表
      orderNumber: '', // 订单编号
      pickerOptions: forwardPickerOptions,
      // 店铺类别
      storeCategory: storeCategory,
      convertStoreCategory: convertKeyValueEnum(storeCategory),
      // 会员等级
      memberLevelEnum: memberLevelEnum,
      convertMemberLevelEnum: convertKeyValueEnum(memberLevelEnum),
      // 付费业务
      shopServiceType,
      convertShopServiceType: convertKeyValueEnum(shopServiceType),
      // 支付状态
      shopPaymentStatus,
      convertPaymentStatus: convertKeyValueEnum(shopPaymentStatus),
      // 支付状态-审核
      convertShopPaymentAuitStatus: convertKeyValueEnum(shopPaymentAuitStatus),
      // 付费方式
      payCategory: payCategory,
      // 订单审核状态
      shopAuditStatusNew,
      convertShopAuditStatusNew: convertKeyValueEnum(shopAuditStatusNew),
      // 发票状态
      invoiceTypeEnum: invoiceTypeAllEnum,
      invoiceType: convertKeyValueEnum(invoiceTypeAllEnum),
      clueType, // 线索包类型
      convertClueType: convertKeyValueEnum(clueType),
      wechatSettingsType, // 微信配置类型
      convertWechatSettingsType: convertKeyValueEnum(wechatSettingsType),
      refundOnlyStatusType, // 订单权益
      // 加载状态
      loading: false,
      dayjs,
      itemInfo: {}, // 选择的item

      refundDialog: false, // 退款弹窗
      refundForm: {
        id: '', // id
        price: '', // 退款金额
        refundOnlyStatus: 0
      },
      refundOnlyFormClue: false,
      refundRules: {
        price: [{ required: true, message: '请输入退款金额', trigger: 'blur' }],
        refundOnlyStatus: [
          { required: true, message: '请选择订单权益', trigger: 'change' }
        ]
      },
      disRefundSubmit: false, // 退款控制

      serviceTimeDialog: false, // 服务弹窗
      serviceTimeForm: {
        id: '', // id
        beginTime: '', // 服务开始时间
        endTime: '' // 服务结束时间
      },
      serviceTimeRules: {
        beginTime: [
          { required: true, message: '请选择服务开始时间', trigger: 'change' }
        ]
      },
      disServiceTimeSubmit: false // 服务时间控制
    }
  },
  activated() {
    const me = this
    me.orderNumber = this.$route.query.orderNumber || ''
    me.getOrderDetail({ page: 1 })
  },
  methods: {
    async getPayChannel() {
      try {
        const res = await getPayChannel({
          orderNum: this.orderNumber
        })
        return res.data.data
      } catch (error) {
        console.log(error)
        return ''
      }
    },
    // 获取列表数据
    getOrderDetail() {
      const me = this
      me.loading = true
      me.vipOrderList = [] // 会员订单列表
      me.clueOrderList = [] // 线索订单列表
      me.wechatOrderList = [] // 微信线索订单列表
      me.adOrderList = [] // 广告订单列表
      me.goldOrderList = [] // 金币订单列表
      getServiceOrderDetail({
        orderNumber: this.orderNumber
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            me.orderInfo = data
            data.orderSubIndexList.length &&
              data.orderSubIndexList.map((item) => {
                // 会员系列
                if ([1, 2].includes(item.serviceType)) {
                  me.vipOrderList.push(item)
                }
                // 线索系列
                if ([3, 4, 5, 6, 8, 9, 11].includes(item.serviceType)) {
                  me.clueOrderList.push(item)
                }
                // 微信线索
                if ([7].includes(item.serviceType)) {
                  me.wechatOrderList.push(item)
                }
                // 广告服务
                if ([10].includes(item.serviceType)) {
                  me.adOrderList.push(item)
                }
                // 金币服务
                if ([12].includes(item.serviceType)) {
                  me.goldOrderList.push(item)
                }
              })
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 退款详情
    reFundDesc(item) {
      this.$refs.reFundComDescList.init({
        ...item
      })
    },

    // 业务转移
    businessTransfer(item) {
      let usedNum = ''
      let refundNum = ''
      if ([1, 2, 8].includes(Number(item.serviceType))) {
        refundNum =
          Number(item.newCluePacketFlag) === 0 && Number(item.serviceType) === 8
            ? ''
            : item.refundShopMemberDays
        usedNum =
          Number(item.newCluePacketFlag) === 0 && Number(item.serviceType) === 8
            ? ''
            : item.costShopMemberDays
      } else {
        refundNum = Number(item.newCluePacketFlag) === 0 ? '' : item.restCLueNum
        usedNum =
          Number(item.newCluePacketFlag) === 0
            ? ''
            : item.number - item.restCLueNum
      }
      localStorage.setItem('previewData', JSON.stringify(item))
      this.$router.push({
        name: 'OrderService',
        query: {
          refundNum,
          usedNum,
          shopId: item.shopId,
          shopName: item.shopName,
          orderNumber: item.orderNumber,
          type: 1,
          newCluePacketFlag: item.newCluePacketFlag
        }
      })
    },

    // 升级
    businessUpgrade(item) {
      localStorage.setItem('previewData', JSON.stringify(item))
      this.$router.push({
        name: 'OrderService',
        query: {
          shopId: item.shopId,
          shopName: item.shopName,
          orderNumber: item.orderNumber,
          type: 2,
          memberType: item.serviceType,
          level: item.level
        }
      })
    },
    // 年包线索补差价
    businessMakeUpDifference(item) {
      localStorage.setItem('previewData', JSON.stringify(item))
      this.$router.push({
        name: 'OrderService',
        query: {
          shopId: item.shopId,
          shopName: item.shopName,
          orderNumber: item.orderNumber,
          type: 3,
          memberType: item.serviceType,
          level: item.level
        }
      })
    },

    // 编辑服务时间
    editServiceTime(item) {
      this.itemInfo = item
      this.serviceTimeForm.beginTime = ''
      this.serviceTimeForm.endTime = ''
      this.serviceTimeDialog = true
    },
    serverTimeChange(time) {
      this.serviceTimeForm.endTime = dayjs(time)
        .add(1, 'y')
        .format('YYYY-MM-DD HH:mm:ss')
    },
    // 确认服务时间
    confirmServiceTime() {
      this.$refs['serviceTimeForm'].validate((valid) => {
        if (valid) {
          if (this.disServiceTimeSubmit) {
            return
          }
          this.disServiceTimeSubmit = true
          editMemberExpireTime({
            orderNumber: this.itemInfo.orderNumber,
            beginTime: this.serviceTimeForm.beginTime
          })
            .then((response) => {
              if (response.data.code === 0) {
                this.$message.success('服务时间编辑成功')
                this.serviceTimeDialog = false
                this.getOrderDetail()
              } else {
                this.$message.error('服务时间编辑失败')
                this.serviceTimeDialog = false
              }
            })
            .finally(() => {
              this.disServiceTimeSubmit = false
            })
        } else {
          return false
        }
      })
    },

    // 退款
    async reFund(item, type) {
      console.log('item======', item)
      // 微信支付: 1, 支付宝: 2, 转账: 3, 收钱吧: 6, '微信(收钱吧)': 61, '支付宝(收钱吧)': 62
      // 退款 ：线下订单、收钱吧微信>180天、收钱吧支付宝>90天、微信>360天、支付宝>90天走OA发起表单申请
      if (type === 1) {
        // 年包线索
        if (item.serviceType === 8) {
          this.refundOnlyFormClue = false
        } else {
          this.refundOnlyFormClue = true
        }
      } else {
        this.refundOnlyFormClue = false
      }
      // 当前时间戳
      const t = new Date().getTime()
      const t1 = 24 * 60 * 60 * 1000
      // 90天时间
      const t90 = t1 * 90
      // 180天时间
      const t180 = t1 * 180
      // 360天时间
      const t360 = t1 * 360
      // 支付宝，支付宝收钱吧
      const payPlatform = this.orderInfo.payPlatform
      const payTime = this.orderInfo.payTime

      const orderTimeStatus =
        ([1, 61].includes(payPlatform) &&
          t - payTime <= t180 &&
          [1, 2].includes(this.orderInfo.invoiceStatus)) ||
        ([2, 62].includes(payPlatform) &&
          t - payTime <= t90 &&
          [1, 2].includes(this.orderInfo.invoiceStatus))

      if ([12].includes(item.serviceType)) {
        return this.$confirm('确认退款?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.itemInfo = item
          // 金币单独处理
          this.submitForm()
        })
      }

      if (
        payPlatform === 3 ||
        ([2, 62].includes(payPlatform) && t - payTime > t90) ||
        (payPlatform === 1 && t - payTime > t360) ||
        (payPlatform === 61 && t - payTime > t180) ||
        orderTimeStatus
      ) {
        const channel = await this.getPayChannel()
        console.log('this.refundOnlyFormClue :>> ', this.refundOnlyFormClue)
        return this.$refs.reFundCom.init({
          ...item,
          ...{
            orderType: 5,
            refundChannel: channel,
            orderNum: item.orderNumber,
            orderSerialsNum: item.transactionNumber,
            invoiceStatus: this.orderInfo.invoiceStatus,
            orderTimeStatus: orderTimeStatus,
            refundOnlyFormClue: this.refundOnlyFormClue
          }
        })
      }
      this.$confirm('确认退款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.itemInfo = item
        this.refundForm.orderNum = item.orderNumber
        this.refundForm.price = ''
        this.refundForm.refundOnlyStatus = 0
        this.refundDialog = true
      })
    },
    // OA退款成功回调
    getRefundInfo() {
      this.getOrderDetail()
    },

    // 退款弹窗
    confirmRefund() {
      this.$refs['refundForm'].validate((valid) => {
        if (valid) {
          if (
            this.refundForm.price < 0 ||
            this.refundForm.price > this.itemInfo.price
          ) {
            return this.$message.info(
              '退款金额要大于等于0并且不能大于支付金额!'
            )
          } else {
            this.submitForm()
          }
        } else {
          return false
        }
      })
    },
    submitForm() {
      if (this.disRefundSubmit) {
        return
      }
      this.disRefundSubmit = true
      let params = {
        orderNumber: this.itemInfo.orderNumber,
        price: this.refundForm.price,
        refundOnlyStatus: this.refundForm.refundOnlyStatus
      }

      // 金币接口单独处理
      if ([12].includes(this.itemInfo.serviceType)) {
        params = {
          orderNumber: this.itemInfo.orderNumber,
          price: this.itemInfo.price
        }
      }

      serviceOrderRefund(params)
        .then((response) => {
          if (response.data.code === 0) {
            this.$message.success('退款成功')
            this.refundDialog = false
            this.getOrderDetail()
          } else {
            this.$message.error('退款失败')
            this.refundDialog = false
          }
        })
        .finally(() => {
          this.disRefundSubmit = false
        })
    },

    // 作废
    toVoid(item) {
      const str = '确定作废订单：' + item.orderNumber + ' 吗？'
      this.$confirm(str, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        serviceOrderInvalide({
          orderNumber: item.orderNumber
        }).then((res) => {
          if (res.data.code === 0) {
            this.$message.info('作废成功!')
            this.getOrderDetail()
          }
        })
      })
    },

    generateContractImt(row) {
      getShopContrateExport({
        orderNumSub: row.orderNumber,
        contractType: ''
      }).then((res) => {
        if (res.data.code === 0) {
          this.$message.success('合同生成成功')
          this.download(res.data.data)
        }
      })
    },

    download(linkUrl) {
      function downloadFile(url, filename) {
        const link = document.createElement('a')
        link.href = url
        link.download = filename || 'downloaded-file' // 默认文件名
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
      if (linkUrl) {
        downloadFile(linkUrl, '')
      } else {
        this.$message.error('下载失败')
      }
    },

    generateContract(row) {
      this.$refs.popuContrate.init(row)
    }
  }
}
</script>
<style lang="scss">
.shop-order {
  .el-tag {
    margin-right: 10px;
    margin-top: 10px;
    cursor: pointer;
  }
}
</style>
