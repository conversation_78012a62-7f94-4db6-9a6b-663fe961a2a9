<template>
  <div v-loading="loading" class="shop-order-list">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item>
        <el-button class="add" type="primary" @click="addOrder()"
          >新增</el-button
        >
      </el-form-item>
      <el-form-item label="订单号">
        <el-input
          v-model="ruleForm.orderNumber"
          type="text"
          placeholder="请输入订单号"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="经销商ID">
        <el-input
          v-model="ruleForm.shopId"
          type="text"
          placeholder="请输入商家ID"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="子订单经销商ID">
        <el-input
          v-model="ruleForm.subShopId"
          type="text"
          placeholder="请输入子订单经销商ID"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="支付金额">
        <el-input
          v-model="ruleForm.price"
          type="text"
          placeholder="请输入支付金额"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="支付状态">
        <el-select
          v-model="payStatus"
          clearable
          placeholder="请选择支付状态"
          style="width: 150px"
          @change="changePayStatus"
        >
          <el-option
            v-for="(value, index) in shopPaymentStatus"
            :key="value"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态">
        <el-select
          v-model="orderStatus"
          clearable
          placeholder="请选择订单状态"
          style="width: 150px"
          @change="changeOrderStatus"
        >
          <el-option
            v-for="(value, index) in shopPaymentAuitStatus"
            :key="value"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="付费方式">
        <el-select
          v-model="ruleForm.payPlatform"
          clearable
          placeholder="请选择付费方式"
          style="width: 150px"
        >
          <el-option
            v-for="(value, index) in shopPayGoldTypeEnmu"
            :key="value"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发票状态">
        <el-select v-model="ruleForm.invoiceStatus" style="width: 90px">
          <el-option
            v-for="(value, index) in invoiceTypeEnum"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单生成时间" label-width="100px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          v-model="createDateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 210px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="付款时间">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          v-model="payTimeDateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 210px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="开票时间">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          v-model="invoiceTimeDateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 210px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="退款时间">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          v-model="refundTimeDateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 210px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="initGetList">重置</el-button>
        <el-button type="success" @click="exportExcel()">导出</el-button>
      </el-form-item>
    </el-form>
    <div>
      <el-table
        ref="orderListTab"
        :data="orderList"
        highlight-current-row
        height="67vh"
        @row-dblclick="rowDoubleClick"
        default-expand-all
        row-class-name="listTableRowClassName"
        border
      >
        <el-table-column type="expand">
          <template #default="props">
            <el-row v-if="props.row.vipOrderList.length">
              <el-col :span="17">
                <div class="wd-flex">
                  <div class="title-box">
                    <div class="table-title">会员</div>
                  </div>
                  <el-table
                    ref="vipOrderList"
                    :data="props.row.vipOrderList"
                    row-class-name="orderListTableRowClassName"
                    header-row-class-name="orderListTableHeadRowClassName"
                    row-key="vipOrderList"
                    border
                  >
                    <el-table-column
                      align="center"
                      prop="orderNumber"
                      width="191"
                      label="订单号"
                    />
                    <el-table-column
                      align="center"
                      prop="shopId"
                      width="80px"
                      label="商家ID"
                    />
                    <el-table-column
                      align="center"
                      prop="shopName"
                      width="150"
                      label="经销商名称"
                    />
                    <el-table-column
                      align="center"
                      prop="serviceType"
                      width="160"
                      label="会员服务"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopServiceType[scope.row.serviceType]
                        }}</span
                        ><span style="color: red">{{
                          scope.row.trial ? '(试用)' : ''
                        }}</span>
                      </template>
                    </el-table-column>

                    <el-table-column
                      prop="level"
                      width="170px"
                      align="center"
                      label="会员等级"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertMemberLevelEnum[scope.row.level]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="130"
                      prop="originalPrice"
                      label="应付价格"
                    />
                    <el-table-column
                      width="130"
                      align="center"
                      prop="discount"
                      label="价格折扣"
                    />
                    <el-table-column
                      align="center"
                      width="130"
                      prop="price"
                      label="实付价格"
                    />
                    <el-table-column
                      prop
                      label="服务开始时间"
                      align="center"
                      width="160px"
                    >
                      <template v-slot="scope">
                        <span>{{
                          $filters.timeFullS(scope.row.beginTime)
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop
                      label="服务结束时间"
                      align="center"
                      width="160px"
                    >
                      <template v-slot="scope">
                        <span>{{
                          $filters.timeFullS(scope.row.deadlineTime)
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="关联主订单号"
                      align="center"
                      width="180px"
                    >
                      <template v-slot="scope">
                        <el-button
                          v-if="scope.row.relatedOrderNumber"
                          link
                          type="primary"
                          size="small"
                          @click="goToDetail(scope.row, true)"
                          >{{ scope.row.relatedOrderNumber }}</el-button
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="120px"
                      label="是否赠送"
                    >
                      <template v-slot="scope">
                        {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="description"
                      width="180px"
                      label="赠送原因"
                    />
                    <el-table-column
                      align="center"
                      prop="status"
                      width="122"
                      label="支付状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertPaymentStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="status"
                      align="center"
                      label="订单状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopPaymentAuitStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="props.row.clueOrderList.length">
              <el-col :span="17">
                <div class="wd-flex">
                  <div class="title-box">
                    <div class="table-title">线索</div>
                  </div>
                  <el-table
                    ref="clueOrderList"
                    :data="props.row.clueOrderList"
                    row-class-name="orderListTableRowClassName"
                    header-row-class-name="orderListTableHeadRowClassName"
                    row-key="clueOrderList"
                    border
                  >
                    <el-table-column
                      align="center"
                      prop="orderNumber"
                      width="191"
                      label="订单号"
                    />
                    <el-table-column
                      align="center"
                      prop="shopId"
                      width="80px"
                      label="商家ID"
                    />
                    <el-table-column
                      align="center"
                      prop="shopName"
                      width="150"
                      label="经销商名称"
                    />
                    <el-table-column
                      align="center"
                      prop="serviceType"
                      width="160"
                      label="线索服务"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopServiceType[scope.row.serviceType]
                        }}</span>
                      </template>
                    </el-table-column>

                    <el-table-column
                      align="center"
                      width="170px"
                      prop="serviceTypeName"
                      label="线索包类型"
                    />
                    <el-table-column
                      align="center"
                      width="130"
                      prop="originalPrice"
                      label="应付价格"
                    />
                    <el-table-column
                      align="center"
                      prop="discount"
                      width="130"
                      label="价格折扣"
                    />
                    <el-table-column
                      align="center"
                      prop="price"
                      width="130"
                      label="支付价格"
                    />
                    <el-table-column
                      align="center"
                      width="160px"
                      prop
                      label="年包开始时间"
                    >
                      <template v-slot="scope">
                        <span>{{
                          $filters.timeFullS(scope.row.beginTime)
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="160px"
                      prop
                      label="年包结束时间"
                    >
                      <template v-slot="scope">
                        <span>{{
                          $filters.timeFullS(scope.row.deadlineTime)
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="关联主订单号"
                      align="center"
                      width="180px"
                    >
                      <template v-slot="scope">
                        <el-button
                          v-if="scope.row.relatedOrderNumber"
                          link
                          type="primary"
                          size="small"
                          @click="goToDetail(scope.row, true)"
                          >{{ scope.row.relatedOrderNumber }}</el-button
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="120px"
                      label="是否赠送"
                    >
                      <template v-slot="scope">
                        {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="180px"
                      prop="description"
                      label="赠送原因"
                    />
                    <el-table-column
                      align="center"
                      prop="status"
                      width="122"
                      label="支付状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertPaymentStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="status"
                      label="订单状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopPaymentAuitStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div></el-col
              >
            </el-row>
            <el-row v-if="props.row.wechatOrderList.length">
              <el-col :span="17">
                <div class="wd-flex">
                  <div class="title-box">
                    <div class="table-title">微信</div>
                  </div>
                  <el-table
                    row-class-name="orderListTableRowClassName"
                    header-row-class-name="orderListTableHeadRowClassName"
                    ref="wechatOrderList"
                    :data="props.row.wechatOrderList"
                    row-key="wechatOrderList"
                    border
                  >
                    <el-table-column
                      prop="orderNumber"
                      width="191"
                      align="center"
                      label="订单号"
                    />
                    <el-table-column
                      align="center"
                      prop="shopId"
                      width="80px"
                      label="商家ID"
                    />
                    <el-table-column
                      align="center"
                      prop="shopName"
                      width="150"
                      label="经销商名称"
                    />
                    <el-table-column
                      prop="serviceType"
                      width="160"
                      align="center"
                      label="线索服务"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopServiceType[scope.row.serviceType]
                        }}</span>
                      </template>
                    </el-table-column>

                    <el-table-column
                      width="170px"
                      align="center"
                      prop="settingType"
                      label="配置类型"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertWechatSettingsType[scope.row.settingType]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="130"
                      prop="originalPrice"
                      label="应付价格"
                    />
                    <el-table-column
                      align="center"
                      prop="discount"
                      width="130"
                      label="价格折扣"
                    />
                    <el-table-column
                      align="center"
                      prop="price"
                      width="130"
                      label="支付金额"
                    />

                    <el-table-column
                      align="center"
                      width="160px"
                      label="配置开始时间"
                    >
                      <template v-slot="scope">
                        <span>{{
                          $filters.timeFullS(scope.row.beginTime)
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="160px"
                      label="配置结束时间"
                    >
                      <template v-slot="scope">
                        <span>{{
                          $filters.timeFullS(scope.row.deadlineTime)
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="关联主订单号"
                      align="center"
                      width="180px"
                    >
                      <template v-slot="scope">
                        <el-button
                          v-if="scope.row.relatedOrderNumber"
                          link
                          type="primary"
                          size="small"
                          @click="goToDetail(scope.row, true)"
                          >{{ scope.row.relatedOrderNumber }}</el-button
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="120px"
                      label="是否赠送"
                    >
                      <template v-slot="scope">
                        {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="180px"
                      prop="description"
                      label="赠送原因"
                    />
                    <el-table-column
                      align="center"
                      width="122"
                      prop="status"
                      label="支付状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertPaymentStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="status"
                      label="订单状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopPaymentAuitStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="settingType"
                      label="配置数量"
                    >
                      <template v-slot="scope">
                        <span>{{
                          scope.row.settingType === 1 ? scope.row.number : ''
                        }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="props.row.adOrderList.length">
              <el-col :span="17">
                <div class="wd-flex">
                  <div class="title-box">
                    <div class="table-title">广告</div>
                  </div>
                  <el-table
                    row-class-name="orderListTableRowClassName"
                    header-row-class-name="orderListTableHeadRowClassName"
                    ref="adOrderList"
                    :data="props.row.adOrderList"
                    row-key="adOrderList"
                    border
                  >
                    <el-table-column
                      prop="orderNumber"
                      width="191"
                      align="center"
                      label="订单号"
                    />
                    <el-table-column
                      align="center"
                      prop="shopId"
                      width="80px"
                      label="商家ID"
                    />
                    <el-table-column
                      align="center"
                      prop="shopName"
                      width="150"
                      label="经销商名称"
                    />
                    <el-table-column
                      align="center"
                      width="120"
                      label="广告服务"
                    >
                      <template #default>广告服务</template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="120"
                      label="投放时长"
                    >
                      <template #default="{ row }">
                        {{ setReleaseTime(row) }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="130"
                      prop="originalPrice"
                      label="应付价格"
                    />
                    <el-table-column
                      align="center"
                      prop="discount"
                      width="130"
                      label="价格折扣"
                    />
                    <el-table-column
                      align="center"
                      prop="price"
                      width="130"
                      label="支付金额"
                    />
                    <el-table-column
                      label="关联主订单号"
                      align="center"
                      width="180px"
                    >
                      <template v-slot="scope">
                        <el-button
                          v-if="scope.row.relatedOrderNumber"
                          link
                          type="primary"
                          size="small"
                          @click="goToDetail(scope.row, true)"
                          >{{ scope.row.relatedOrderNumber }}</el-button
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="120px"
                      label="是否赠送"
                    >
                      <template v-slot="scope">
                        {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="180px"
                      prop="description"
                      label="赠送原因"
                    />
                    <el-table-column
                      align="center"
                      width="122"
                      prop="status"
                      label="支付状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertPaymentStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="122"
                      prop="status"
                      label="订单状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopPaymentAuitStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="props.row.goldOrderList.length">
              <el-col :span="17">
                <div class="wd-flex">
                  <div class="title-box">
                    <div class="table-title">金币</div>
                  </div>
                  <el-table
                    row-class-name="orderListTableRowClassName"
                    header-row-class-name="orderListTableHeadRowClassName"
                    ref="goldOrderList"
                    :data="props.row.goldOrderList"
                    row-key="goldOrderList"
                    border
                  >
                    <el-table-column
                      prop="orderNumber"
                      width="191"
                      align="center"
                      label="订单号"
                    />
                    <el-table-column
                      align="center"
                      prop="shopId"
                      width="80px"
                      label="商家ID"
                    />
                    <el-table-column
                      align="center"
                      prop="shopName"
                      width="150"
                      label="经销商名称"
                    />
                    <el-table-column
                      align="center"
                      width="120"
                      label="金币服务"
                    >
                      <template #default>金币服务</template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="120"
                      prop="number"
                      label="金币数量"
                    />
                    <el-table-column
                      align="center"
                      width="130"
                      prop="originalPrice"
                      label="应付价格"
                    />
                    <el-table-column
                      align="center"
                      prop="discount"
                      width="130"
                      label="价格折扣"
                    />
                    <el-table-column
                      align="center"
                      prop="price"
                      width="130"
                      label="支付金额"
                    />
                    <el-table-column
                      label="关联主订单号"
                      align="center"
                      width="180px"
                    >
                      <template v-slot="scope">
                        <el-button
                          v-if="scope.row.relatedOrderNumber"
                          link
                          type="primary"
                          size="small"
                          @click="goToDetail(scope.row, true)"
                          >{{ scope.row.relatedOrderNumber }}</el-button
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="120px"
                      label="是否赠送"
                    >
                      <template v-slot="scope">
                        {{ scope.row.serviceExtra?.isGive ? '赠送' : '付费' }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="180px"
                      prop="description"
                      label="赠送原因"
                    />
                    <el-table-column
                      align="center"
                      width="122"
                      prop="status"
                      label="支付状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertPaymentStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      width="122"
                      prop="status"
                      label="订单状态"
                    >
                      <template v-slot="scope">
                        <span>{{
                          convertShopPaymentAuitStatus[scope.row.status]
                        }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column
          prop="orderNumber"
          label="订单号"
          align="center"
          width="190px"
        />
        <el-table-column
          prop="shopId"
          label="商家ID"
          align="center"
          width="80px"
        />
        <el-table-column
          prop="shopName"
          label="经销商名称"
          align="center"
          width="150px"
        >
          <template v-slot="scope">
            <div
              style="color: blue; cursor: pointer"
              @click="JumpDetails(scope.row)"
            >
              {{ scope.row.shopName }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="shopMemberType"
          label="店铺类别"
          align="center"
          width="100px"
        >
          <template v-slot="scope">
            <span>{{ convertStoreCategory[scope.row.shopMemberType] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="memberLevel"
          label="会员等级"
          align="center"
          width="170px"
        >
          <template v-slot="scope">
            <span>{{ convertMemberLevelEnum[scope.row.memberLevel] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="source"
          label="订单来源"
          align="center"
          width="90px"
        >
          <template v-slot="scope">
            <span>{{ shopMainOrderSource[scope.row.source] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单生成时间" align="center" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="price"
          label="支付金额"
          align="center"
          width="90px"
        />
        <el-table-column label="付款时间" align="center" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.payTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="付款人" align="center" width="160px">
          <template v-slot="scope">
            <div>
              <span v-if="scope.row.uid">{{ scope.row.uid }}</span>
              <el-input
                v-model="scope.row.payUser"
                placeholder="请输入付款人姓名"
                clearable
                @change="modifyPayUser(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="payNumber"
          label="付款账号"
          align="center"
          width="150px"
        />

        <el-table-column
          prop="payPlatform"
          align="center"
          label="付费方式"
          width="140px"
        >
          <template v-slot="scope">
            <span>{{ convertPayCategory[scope.row.payPlatform] }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="invoiceStatus"
          align="center"
          label="发票状态"
          width="90"
        >
          <template v-slot="scope">
            <span>{{ invoiceType[scope.row.invoiceStatus] }}</span>
            <el-button
              v-if="parseInt(scope.row.invoiceStatus) === 1"
              type="primary"
              link
              @click="skipInvoiceDetail(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="allowInvoicePrice"
          label="可开票金额"
          align="center"
          width="150px"
        />
        <el-table-column prop label="开票时间" align="center" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.invoiceTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          align="center"
          label="支付状态"
          width="90px"
        >
          <template v-slot="scope">
            <span>{{ convertPaymentStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          align="center"
          label="订单状态"
          width="90px"
        >
          <template v-slot="scope">
            <span>{{ convertShopPaymentAuitStatus[scope.row.status] }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="transactionNumber"
          label="订单流水编号"
          align="center"
          width="230px"
        />

        <el-table-column
          label="退款金额"
          prop="refundAmount"
          align="center"
          width="90px"
        />
        <el-table-column prop label="退款时间" align="center" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.refundTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          prop="remark"
          align="center"
          width="90px"
        />
        <!-- <el-table-column
          prop="description"
          label="订单描述"
          align="center"
          width="160px"
        /> -->
        <el-table-column prop="" label="代付凭证" align="center" width="150px">
          <template v-slot="scope">
            <div class="orderDatas">
              <el-button
                v-if="scope.row.payOtherVoucher"
                @click="onPreview([scope.row.payOtherVoucher])"
                type="primary"
                link
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="转账回执" align="center" width="150px">
          <template v-slot="scope">
            <div class="orderDatas">
              <span>{{ scope.row.transferReceiptMsg || '' }}</span>
              <el-button
                v-if="scope.row.transferReceipt"
                @click="onPreview([scope.row.transferReceipt])"
                type="primary"
                link
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="ossUserName"
          label="操作人"
          align="center"
          width="160px"
        />
        <el-table-column label="操作" width="260px">
          <template v-slot="scope">
            <el-button
              type="primary"
              text
              size="small"
              @click="goToDetail(scope.row)"
              >订单详情</el-button
            >
            <el-button
              type="primary"
              text
              v-if="[3, 20].includes(scope.row.status) && scope.row.price"
              @click="craetFreeOrder(scope.row)"
              >创建0元单</el-button
            >
            <el-button
              v-if="[1].includes(scope.row.status)"
              type="primary"
              text
              size="small"
              @click="cancelOrder(scope.row)"
              >取消</el-button
            >
            <el-button
              v-if="
                [0, 2].includes(scope.row.source) && scope.row.status === 18
              "
              type="primary"
              text
              size="small"
              @click="creatPay(scope.row)"
              >生成支付</el-button
            >
            <el-button
              v-if="[1].includes(scope.row.status)"
              type="primary"
              text
              size="small"
              @click="creatPayCode(scope.row)"
              >生成支付码</el-button
            >
            <el-button
              type="primary"
              v-if="scope.row.payOtherVoucherFlag"
              link
              size="small"
              @click="updataImg(scope.row)"
              >上传代付凭证</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="ruleForm.page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="justify-content: center; margin-top: 10px"
        @size-change="currentChange"
        @current-change="currentChange"
      />
    </div>
    <el-dialog
      v-model="qrCodeImgShow"
      title="支付二维码"
      width="330px"
      append-to-body
    >
      <div>
        <img :src="qrCodeImg" alt style="width: 300px; height: 300px" />
      </div>
    </el-dialog>
    <TransferAccountsPayForm
      ref="transferAccountsRefForm"
      @updateData="updateData"
    />
    <ShopinvoiceMore
      ref="shopinvoiceRef"
      @gotoInvoiceDatail="gotoInvoiceDatail"
    ></ShopinvoiceMore>
    <el-upload
      :show-file-list="false"
      :http-request="httpRequest"
      :on-success="updateImg"
      name="upfile"
      multiple
      style="display: inline-block"
      class="avatar-uploader"
      id="el-upload__input_common"
      action
    />
  </div>
</template>
<script setup lang="ts">
import TransferAccountsPayForm from './components/transferAccountsPayForm.vue'
import ShopinvoiceMore from './components/shopinvoiceMore.vue'
import { ElMessageBox, ElLoading, ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import {
  storeCategory,
  memberLevelEnum,
  shopPaymentStatus,
  shopPaymentAuitStatus,
  invoiceTypeAllEnum,
  shopInvoiceType,
  shopPayGoldType,
  shopMainOrderSource,
  shopServiceType,
  wechatSettingsType
} from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import {
  getServiceOrderList,
  editServiceOrderPayUser,
  serviceCancel,
  serviceOrderActualPrice,
  orderInvoiceRecordlist,
  serviceOrderGenPayQrCode,
  uploadT,
  exportServiceOrderList
} from '@/api/garage'
import { forwardPickerOptions } from '@/utils/configData'

const convertShopServiceType = convertKeyValueEnum(shopServiceType)
const convertWechatSettingsType = convertKeyValueEnum(wechatSettingsType)
interface IRuleForm {
  orderNumber: string
  shopId: number | string
  subShopId: number | string
  status: number | string
  payPlatform: number | string
  price: number | string
  page: number
  limit: number
  invoiceStatus: number | string
  createStartTime: string
  createEndTime: string
  payStartTime: string
  payEndTime: string
  invoiceStartTime: string
  invoiceEndTime: string
  refundTimeBegin: string
  refundTimeEnd: string
}

interface IItem {
  orderNumber: string
  shopId: number | string
  subShopId: number | string
  shopName: string
  ossUserName: string
  shopMemberType: number
  memberLevel: number | string
  payPlatform: number | string
  price: number | string
  payTime: number | string
  uid: number | string
  payUser: string
  payNumber: number | string
  createTime: number | string
  refundTime: number | string
  invoiceStatus: number | string
  invoiceTime: number | string
  status: number | string
  transactionNumber: number | string
  contractNumber: number | string
  [propsName: number | string]: number | string
}
interface IdataType {
  [propsName: number | string]: number | string
}
// 路由
const router = useRouter()
// 参数
// const route = useRoute()

// 订单生成时间
const createDateRange = computed({
  get() {
    if (ruleForm.createStartTime && ruleForm.createEndTime) {
      return [ruleForm.createStartTime, ruleForm.createEndTime]
    }
    return []
  },
  set(value) {
    if (value) {
      ruleForm.createStartTime = value[0]
      ruleForm.createEndTime = value[1]
    } else {
      ruleForm.createStartTime = ''
      ruleForm.createEndTime = ''
    }
  }
})

// 付款时间
const payTimeDateRange = computed({
  get() {
    if (ruleForm.payStartTime && ruleForm.payEndTime) {
      return [ruleForm.payStartTime, ruleForm.payEndTime]
    }
    return []
  },
  set(value) {
    if (value) {
      ruleForm.payStartTime = value[0]
      ruleForm.payEndTime = value[1]
    } else {
      ruleForm.payStartTime = ''
      ruleForm.payEndTime = ''
    }
  }
})

// 开票时间
const invoiceTimeDateRange = computed({
  get() {
    if (ruleForm.invoiceStartTime && ruleForm.invoiceEndTime) {
      return [ruleForm.invoiceStartTime, ruleForm.invoiceEndTime]
    }
    return []
  },
  set(value) {
    if (value) {
      ruleForm.invoiceStartTime = value[0]
      ruleForm.invoiceEndTime = value[1]
    } else {
      ruleForm.invoiceStartTime = ''
      ruleForm.invoiceEndTime = ''
    }
  }
})

// 退款时间
const refundTimeDateRange = computed({
  get() {
    if (ruleForm.refundTimeBegin && ruleForm.refundTimeEnd) {
      return [ruleForm.refundTimeBegin, ruleForm.refundTimeEnd]
    }
    return []
  },
  set(value) {
    if (value) {
      ruleForm.refundTimeBegin = value[0]
      ruleForm.refundTimeEnd = value[1]
    } else {
      ruleForm.refundTimeBegin = ''
      ruleForm.refundTimeEnd = ''
    }
  }
})
// 时间快捷
const pickerOptions = reactive<any>(forwardPickerOptions)
// 店铺类别
// const storeCategory = reactive<any>(storeCategory)
const convertStoreCategory = reactive<IdataType>(
  convertKeyValueEnum(storeCategory)
)

// 会员等级
const convertMemberLevelEnum = reactive<IdataType>(
  convertKeyValueEnum(memberLevelEnum)
)

// 付费业务
// const memberTypeList = reactive<any>(memberTypeList)
// const convertMemberTypeList = reactive<any>(convertKeyValueEnum(memberTypeList))

// 支付状态
const convertPaymentStatus = reactive<IdataType>(
  convertKeyValueEnum(shopPaymentStatus)
)
// 支付状态-审核
const convertShopPaymentAuitStatus = reactive<IdataType>(
  convertKeyValueEnum(shopPaymentAuitStatus)
)

// 付费方式
// const payCategory = reactive<any>(payCategory)
const shopPayGoldTypeEnmu = { ...{ 全部: '', ...shopPayGoldType } }
const convertPayCategory = reactive<IdataType>(
  convertKeyValueEnum(shopPayGoldType)
)
// 发票状态
const invoiceTypeEnum = reactive<IdataType>(invoiceTypeAllEnum)
const invoiceType = reactive<IdataType>(shopInvoiceType)

let ruleForm = reactive<IRuleForm>({
  orderNumber: '',
  shopId: '',
  subShopId: '',
  status: '',
  payPlatform: '',
  price: '',
  limit: 20,
  page: 1,
  invoiceStatus: '',
  createStartTime: '',
  createEndTime: '',
  payStartTime: '',
  payEndTime: '',
  invoiceStartTime: '',
  invoiceEndTime: '',
  refundTimeBegin: '',
  refundTimeEnd: ''
})

let orderList = reactive<IItem[]>([])
const loading = ref(<boolean>false)
const total = ref(<number>0)
const transferAccountsRefForm = ref()
const shopinvoiceRef = ref()

// 支付状态和订单状态
const payStatus = ref()
const orderStatus = ref()

const qrCodeImgShow = ref(false)
const qrCodeImg = ref()

onMounted(() => {
  getOrderList({ page: 1 })
})

const creatPayCode = (item: IItem) => {
  qrCodeImg.value = ''
  qrCodeImgShow.value = false
  serviceOrderGenPayQrCode({
    orderNum: item.orderNumber,
    payType: 66 // 66 固定定义 代表线上支付
  }).then((res) => {
    const data = res.data
    if (data.code === 0) {
      qrCodeImg.value = data.data.qrCodeImg
      qrCodeImgShow.value = true
    }
    console.log(res, '==========')
  })
}

const changePayStatus = (value: string | number) => {
  orderStatus.value = ''
  ruleForm.status = value
}
const changeOrderStatus = (value: string | number) => {
  payStatus.value = ''
  ruleForm.status = value
}
const getOrderList = (paramsObj: { page: number }) => {
  const requestParams: IRuleForm = {
    ...ruleForm,
    ...paramsObj
  }
  loading.value = true
  getServiceOrderList(requestParams)
    .then((response) => {
      if (response.data.code === 0) {
        const data = response.data.data
        orderList = data.listData || []
        orderList.forEach((iten: any) => {
          iten.vipOrderList = []
          iten.clueOrderList = []
          iten.wechatOrderList = []
          iten.adOrderList = []
          iten.goldOrderList = []
          iten.orderSubIndexList.length &&
            iten.orderSubIndexList.map((item: any) => {
              // 会员系列
              if ([1, 2].includes(item.serviceType)) {
                iten.vipOrderList.push(item)
              }
              // 线索系列
              if ([3, 4, 5, 6, 8, 9, 11].includes(item.serviceType)) {
                iten.clueOrderList.push(item)
              }
              // 微信线索
              if ([7].includes(item.serviceType)) {
                iten.wechatOrderList.push(item)
              }
              // 广告服务
              if ([10].includes(item.serviceType)) {
                iten.adOrderList.push(item)
              }
              // 金币服务
              if ([12].includes(item.serviceType)) {
                iten.goldOrderList.push(item)
              }
            })
        })
        total.value = data.total || 0
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 生成支付订单
const creatPay = (item: IItem) => {
  serviceOrderActualPrice({
    orderNumber: item.orderNumber
  }).then((res) => {
    const data = res.data
    if (data.code === 0) {
      transferAccountsRefForm.value.initData({
        ...item,
        ...{ price: data.data }
      })
    }
  })
}

const updateData = () => {
  getOrderList({ page: ruleForm.page })
}

// 创建0元单
const craetFreeOrder = (item: IItem) => {
  router.push({
    name: 'OrderService',
    query: {
      shopId: item.shopId,
      shopName: item.shopName,
      orderNumber: item.orderNumber,
      type: 0
    }
  })
}

// 新增订单
const addOrder = () => {
  router.push({
    name: 'OrderService'
  })
}

// 订单详情
const goToDetail = (item: IItem, isMain?: boolean) => {
  router.push({
    name: 'ShopOrderDetail',
    query: {
      orderNumber: isMain ? item.relatedOrderNumber : item.orderNumber
    }
  })
}

// 双击进入详情
const rowDoubleClick = (item: IItem) => {
  router.push({
    name: 'ShopOrderDetail',
    query: {
      orderNumber: item.orderNumber
    }
  })
}

// 查看发票详情
const skipInvoiceDetail = (item: IItem) => {
  orderInvoiceRecordlist({
    orderNum: item.orderNumber
  }).then((res) => {
    const data = res.data
    if (data.code === 0) {
      if (data.data.length === 1) {
        gotoInvoiceDatail(data.data[0])
      } else {
        shopinvoiceRef.value.initData({ list: data.data })
      }
    }
  })
}
// 跳转发票详情
const gotoInvoiceDatail = (orderNum: string | number) => {
  router.push({
    path: 'invoiceDetail',
    query: {
      invoiceId: orderNum
    }
  })
}

// 跳转经销商详情
const JumpDetails = (item: IItem) => {
  router.push({
    name: 'DistributorDetails',
    query: {
      id: item.shopId
    }
  })
}

// 查询
const search = () => {
  ruleForm.page = 1
  orderList = []
  getOrderList({ page: 1 })
}

// 更新页码
const currentChange = (page: number) => {
  ruleForm.page = page
  orderList = []
  getOrderList({ page })
}

// 重置
const initGetList = () => {
  ruleForm.page = 1
  orderList = []
  resetData()
  getOrderList({ page: 1 })
}

// 设置发送数据
const resetData = () => {
  ruleForm = Object.assign(ruleForm, {
    orderNumber: '',
    shopId: '',
    subShopId: '',
    shopName: '',
    status: '',
    payPlatform: '',
    price: '',
    page: 1,
    limit: 20,
    invoiceStatus: '', // 开票状态 1：已开 0：未开
    createStartTime: '', // 订单生成的开始日期
    createEndTime: '', // 订单生成的结束日期
    payStartTime: '', // 付款时间开始日期
    payEndTime: '', // 付款时间结束日期
    invoiceStartTime: '',
    invoiceEndTime: '',
    refundTimeBegin: '',
    refundTimeEnd: ''
  })
  orderStatus.value = ''
  payStatus.value = ''
}

// 取消
const cancelOrder = (item: IItem) => {
  serviceCancel({
    pOrderNumber: item.orderNumber
  }).then((response) => {
    console.log(response.data.code)
    if (response.data.code === 0) {
      ElMessage.success('订单取消成功')
      getOrderList({ page: ruleForm.page })
    } else {
      ElMessage.error('订单取消失败')
    }
  })
}

// 修改付款人姓名
const modifyPayUser = (item: IItem) => {
  editServiceOrderPayUser({
    orderNumber: item.orderNumber,
    payUser: item.payUser
  }).then((response) => {
    if (response.data.code === 0) {
      ElMessage.success('修改成功')
      getOrderList({ page: ruleForm.page })
    }
  })
}
// 导出Excel

const exportExcel = () => {
  ElMessageBox.confirm('你确认导出到Excel么', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在导出，请稍等......',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    exportServiceOrderList({
      ...ruleForm,
      page: 1,
      limit: 9999
    }).then(async (res) => {
      const data = res.data.data
      const listData = data.listData || []
      if (!listData || listData.length === 0) {
        loading.close()
        ElMessage.error('暂无数据可以导出')
        return
      }
      const { export_json_to_excel } = await import('@/vendor/Export2Excel')
      // 导出的表头
      const tHeader = [
        '订单号',
        '经销商id',
        '经销商名称',
        '店铺类别',

        '会员等级',
        '支付金额',
        '付款时间',
        '付款人',
        '付款账号',

        '付费方式',
        '订单生成时间',
        '发票状态',
        '可开票金额',
        '开票时间',
        '状态',

        '订单流水编号',
        '退款金额',
        '退款时间',
        '备注',
        '操作人'
      ]
      // 导出表头要对应的数据
      const filterVal = [
        'orderNumber',
        'shopId',
        'shopName',
        'shopMemberType',

        'memberLevel',
        'price',
        'payTime',
        'payUser',
        'payNumber',

        'payPlatform',
        'createTime',
        'invoiceStatus',
        'allowInvoicePrice',
        'invoiceTime',
        'status',

        'transactionNumber',
        'refundAmount',
        'refundTime',
        'remark',
        'ossUserName'
      ]
      const exportData = formatJsonUser(filterVal, listData)
      export_json_to_excel(tHeader, exportData, '商家订单列表')
      ElMessage.success('导出成功')
      loading.close()
    })
  })
}

const formatJsonUser = (filterVal: string[], jsonData: any[]) => {
  return jsonData.map((v) =>
    filterVal.map((j) => {
      // 时间处理
      if (
        ['payTime', 'createTime', 'invoiceTime', 'refundTime'].includes(j) &&
        v[j]
      ) {
        return dayjs(v[j]).format('YYYY-MM-DD HH:mm:ss')
      }

      // 店铺类别
      if (j === 'shopMemberType' && v[j]) {
        return convertStoreCategory[v[j]]
      }
      // 会员等级
      if (j === 'memberLevel' && v[j]) {
        return convertMemberLevelEnum[v[j]]
      }
      if (j === 'allowInvoicePrice') {
        return v[j]
      }
      // 付款人
      if (j === 'payUser') {
        const str = v['uid'] || '' + ' ' + !!v[j] ? v[j] : ''
        return str
      }

      // 付费方式
      if (j === 'payPlatform' && v[j]) {
        return convertPayCategory[v[j]]
      }

      // 发票状态
      if (j === 'invoiceStatus' && (v[j] || v[j] === 0)) {
        return invoiceType[v[j]]
      }

      // 支付状态
      if (j === 'status' && (v[j] || v[j] === 0)) {
        if ([17, 18, 19, 20].includes(v[j])) {
          return convertShopPaymentAuitStatus[v[j]]
        } else {
          return convertPaymentStatus[v[j]]
        }
      }

      return v[j] || ''
    })
  )
}

const { proxy } = getCurrentInstance() as any

const httpRequest = async (option: any) => {
  proxy.$oss.ossUploadImage(option)
}
let currentRow: any = {}
const updataImg = (row) => {
  ;(
    document.getElementById('el-upload__input_common')!.children[0]
      .children[0] as HTMLElement
  ).click()
  currentRow = row
  // document.querySelector('.el-upload__input').click()
}
const updateImg = (res: any) => {
  if (!res) return
  console.log(res)
  if (res.name) {
    uploadT({
      orderNumber: currentRow.orderNumber,
      payOtherVoucher: res.imgUrl
    }).then(() => {
      getOrderList({ page: ruleForm.page })
    })

    return
  } else {
    window.$message.error('上传错误')
  }
}
const onPreview = (imgList) => {
  proxy.$viewerApi({
    images: imgList,
    options: {
      initialViewIndex: 0
    }
  })
}

const setReleaseTime = (data: any) => {
  if (!data.createTime || !data.deadlineTime) return ''
  const d1 = new Date(data.createTime)
  const d2 = new Date(data.deadlineTime)
  const timeDiff = Math.abs(d2.getTime() - d1.getTime())
  const days = Math.ceil(timeDiff / (1000 * 3600 * 24))
  return days + '天'
}
</script>
<style lang="scss">
.orderListTableRowClassName,
.orderListTableHeadRowClassName {
  // --el-table-tr-bg-color: #eaf5fe;
  // --el-table-header-bg-color: #eaf5fe;
  // th {
  //   font-weight: normal;
  // }
}
.listTableRowClassName {
  background-color: #95d475 !important;
}
</style>
<style lang="scss" scoped>
.shop-order-list {
  padding: 20px 20px 0 20px;
}

:deep(.el-table__expanded-cell) {
  padding: 0;
  background: #eaf5fe;
}

.title-box {
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px solid #fff;
  border-right: 0;
}
.table-title {
  color: rgb(161, 165, 80);
  display: flex;
  align-items: center;
  justify-content: center;
  /* border: 1px solid #ebeef5; */
}
</style>
