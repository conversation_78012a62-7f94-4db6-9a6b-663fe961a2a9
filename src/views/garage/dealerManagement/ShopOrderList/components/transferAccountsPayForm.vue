<template>
  <el-dialog
    v-model="showStatus"
    title="完善支付信息"
    width="900px"
    append-to-body
    @close="close"
  >
    <el-form
      inline
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="130px"
    >
      <el-form-item prop="cash" label="支付金额：">
        <el-input disabled v-model="form.cash" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="付费方式：" prop="payWay">
        <el-select
          v-model="form.payWay"
          value-key="id"
          clearable
          placeholder="请选择"
          @change="selectPayWay"
          :disabled="!!qrCodeImg"
        >
          <el-option
            v-for="(value, index) in payWays"
            :key="index"
            :label="value.label"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <div v-if="form.payWay === 3">
        <el-form-item prop="date" label="付款时间：">
          <div>
            <el-date-picker
              v-model="form.date"
              type="date"
              :default-value="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
              placeholder="开始日期"
              value-format="x"
            />
          </div>
        </el-form-item>
        <el-form-item prop="account" label="付款账号：">
          <el-input
            v-model="form.account"
            placeholder="请输入付款账号"
            clearable
          />
        </el-form-item>
        <el-form-item prop="transactionNumber" label="流水编号：">
          <el-input
            v-model="form.transactionNumber"
            placeholder="请输入流水编号"
            clearable
          />
        </el-form-item>
        <el-form-item prop="payer" label="付款人：">
          <el-input v-model="form.payer" placeholder="请输入付款人" clearable />
        </el-form-item>
        <el-form-item label="支付状态：">
          <el-input
            v-model="form.status"
            disabled
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="付款方：" prop="payPerson">
          <el-select
            v-model="form.payPerson"
            value-key="id"
            clearable
            placeholder="个人/企业"
          >
            <el-option
              v-for="(value, index) in payPersnTypes"
              :key="index"
              :label="value.name"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <div style="margin-bottom: 60px">
          <el-form-item label="代付凭证">
            <el-button type="primary" @click="updataImg(0)">上传</el-button>
            <el-image
              v-if="paymentVoucher"
              :src="paymentVoucher"
              class="img-content"
              @click="seeBigImg(0)"
            >
              <template v-slot:placeholder>
                <div class="image-slot">
                  加载中
                  <span class="dot">...</span>
                </div>
              </template>
            </el-image>
          </el-form-item>
          <el-form-item
            label="转账回执信息"
            prop="transferReceipt"
            style="margin-left: 50px"
          >
            <div class="flex">
              <el-select v-model="transferType" placeholder="请选择" clearable>
                <el-option label="回执图片" :value="1" />
                <el-option label="回执信息" :value="2" />
              </el-select>
              <div class="ml10">
                <el-input
                  v-if="transferType === 2"
                  v-model="form.transferReceiptMsg"
                  type="textarea"
                  :rows="3"
                  style="width: 300px"
                  placeholder="请输入转账回执"
                  clearable
                />
              </div>

              <template v-if="transferType === 1">
                <el-button
                  class="ml10 mr10"
                  type="primary"
                  @click="updataImg(1)"
                  >上传</el-button
                >
                <el-image
                  v-if="form.transferReceipt"
                  :src="form.transferReceipt"
                  class="img-content"
                  @click="seeBigImg(1)"
                >
                  <template v-slot:placeholder>
                    <div class="image-slot">
                      加载中
                      <span class="dot">...</span>
                    </div>
                  </template>
                </el-image>
              </template>
            </div>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <!-- <el-button type="primary" :disabled="!!qrCodeImg" @click="submit">{{
      form.payWay === 3 ? '提交订单' : '生成二维码'
    }}</el-button> -->
    <el-button v-if="form.payWay === 3" type="primary" @click="submit"
      >提交订单</el-button
    >
    <el-button v-else type="primary" :disabled="!!qrCodeImg" @click="submit"
      >生成二维码</el-button
    >
    <img
      v-if="qrCodeImg && [61, 62, 66].includes(form.payWay)"
      class="wd-w-200px wd-h-200px wd-mb-20 wd-mt-20 wd-ml-50"
      :src="qrCodeImg"
    />
    <el-upload
      :show-file-list="false"
      :http-request="httpRequest"
      :on-success="updateImg"
      name="upfile"
      multiple
      style="display: inline-block"
      class="avatar-uploader"
      id="el-upload__input_common2"
      action
    />
    <choose-show-image ref="showImage" />
  </el-dialog>
</template>

<script setup lang="ts">
import { FormInstance, ElMessage } from 'element-plus'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'

import {
  serviceOrderComplete,
  serviceOrderCompleteGenQrCode
} from '@/api/garage'

interface IForm {
  payWay: any
  cash: string
  date: string
  account: string
  transactionNumber: string
  payer: string
  status: string
  transferReceiptMsg: string
  transferReceipt: string
  payPerson: any
}
let form = reactive<IForm>({
  payWay: '',
  cash: '',
  date: '',
  account: '',
  transactionNumber: '',
  payer: '',
  status: '已支付',
  transferReceipt: '',
  payPerson: '',
  transferReceiptMsg: ''
})
const rules = reactive({
  payWay: [{ required: true, message: '请选择付费方式', trigger: 'change' }],
  cash: [{ required: true, message: '请输入支付金额', trigger: 'blur' }],
  date: [{ required: true, message: '请选择付款时间', trigger: 'change' }],
  account: [{ required: true, message: '请输入付款账号', trigger: 'blur' }],
  transactionNumber: [
    { required: true, message: '请输入流水编号', trigger: 'blur' }
  ],
  payer: [{ required: true, message: '请输入付款人', trigger: 'blur' }],
  payPerson: [{ required: true, message: '请选择付款方', trigger: 'change' }]
  // transferReceipt: [
  //   { required: true, message: '请选择转账回执', trigger: 'blur' }
  // ]
})
const payPersnTypes = [
  {
    id: 1,
    name: '个人'
  },
  {
    id: 2,
    name: '企业'
  }
]

const transferType = ref<number>(1)
watchPostEffect(() => {
  if (transferType.value === 2) {
    form.transferReceipt = ''
  }
  if (transferType.value === 1) {
    form.transferReceiptMsg = ''
  }
  if (!transferType.value) {
    form.transferReceiptMsg = ''
    form.transferReceipt = ''
  }
})

const formRef = ref<FormInstance>()
const payWays = [
  {
    id: 66,
    label: '线上支付'
  },
  // {
  //   id: 1,
  //   label: '微信'
  // },
  // // {
  // //   id: 2,
  // //   label: '支付宝'
  // // },
  // {
  //   id: 62,
  //   label: '支付宝（收钱吧）'
  // },
  {
    id: 3,
    label: '转账'
  }
]
const showStatus = ref<boolean>(false)
const qrCodeImg = ref<string>('')
const infoData = ref<any>({})

const emits = defineEmits(['updateData'])

// 初始化
const initData = (info: { price: string }) => {
  infoData.value = info
  form.cash = info.price
  form.payWay = ''
  form.transactionNumber = ''
  showStatus.value = true
  form.transferReceipt = ''
  form.transferReceiptMsg = ''
  form.payPerson = ''
  paymentVoucher.value = ''
  qrCodeImg.value = ''
}
const submit = () => {
  formRef.value!.validate((valid) => {
    if (valid) {
      if (form.payWay === 3) {
        if (!form.transferReceipt && !form.transferReceiptMsg) {
          return ElMessage.error('请上传转账回执图片或填写转账回执信息')
        }
        serviceOrder()
      } else {
        serviceOrderGenQrCode()
      }
    }
  })
}
const selectPayWay = (item: number) => {
  if ([1, 2, 62, 66].includes(item)) {
    form = Object.assign(form, {
      date: '',
      account: '',
      transactionNumber: '',
      payer: '',
      status: '已支付'
    })
  }
  qrCodeImg.value = ''
}

// 支付-转账
const serviceOrder = () => {
  serviceOrderComplete({
    orderNumber: infoData.value.orderNumber,
    payType: form.payWay,
    payTime: form.date,
    payNumber: form.account,
    transactionNumber: form.transactionNumber,
    payUser: form.payer,
    paySource: form.payPerson?.id || '',
    payOtherVoucher: paymentVoucher.value,
    transferReceipt: form.transferReceipt,
    transferReceiptMsg: form.transferReceiptMsg
  }).then((res) => {
    if (res.data.code === 0) {
      window.$message.success('支付方式完善成功')
      showStatus.value = false
      emits('updateData')
    } else {
      window.$message.error('支付方式完善失败')
    }
  })
}
// 支付-二维码
const serviceOrderGenQrCode = () => {
  serviceOrderCompleteGenQrCode({
    orderNumber: infoData.value.orderNumber,
    payType: form.payWay
  }).then((res) => {
    if (res.data.code === 0) {
      qrCodeImg.value = res.data.data.qrCodeImg
      window.$message.success('支付方式完善成功')
      emits('updateData')
    } else {
      window.$message.error('支付方式完善失败')
    }
  })
}

const close = () => {
  formRef.value!.resetFields()
}
const { proxy } = getCurrentInstance() as any
const paymentVoucher = ref('')

const showImage = ref()
let type = 0
// 更新图片
const updataImg = (val: number) => {
  ;(
    document.getElementById('el-upload__input_common2')!.children[0]
      .children[0] as HTMLElement
  ).click()
  type = val
  // document.querySelector('.el-upload__input').click()
}
const updateImg = (res: any) => {
  if (!res) return
  console.log(res)
  if (res.name) {
    const imgOrgUrl = res.imgOrgUrl
    if (type === 0) {
      paymentVoucher.value = imgOrgUrl
    } else {
      form.transferReceipt = imgOrgUrl
      formRef.value?.clearValidate(['transferReceipt'])
    }

    return
  } else {
    window.$message.error('上传错误')
  }
}
const seeBigImg = (type) => {
  showImage.value.init(type === 0 ? paymentVoucher.value : form.transferReceipt)
}
const httpRequest = async (option: any) => {
  proxy.$oss.ossUploadImage(option)
}
defineExpose({
  initData
})
</script>

<style scoped>
.img-content {
  width: 100px;
  /* height: 100px; */
  object-fit: cover;
  cursor: pointer;
}
</style>
