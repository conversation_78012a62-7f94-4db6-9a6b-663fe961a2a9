<template>
  <el-dialog v-model="dialogVisible" title="订单信息">
    <div>
      <div
        v-if="data.shopId"
        class="wd-flex wd-justify-center wd-font-bold wd-text-base"
      >
        <div>经销商ID：{{ data.shopId }}</div>
        <div class="wd-ml-50px">经销商名称：{{ data.shopName }}</div>
      </div>
      <template v-if="props.isTransfer">
        <div class="wd-font-bold wd-text-base wd-mb-20px wd-mt-20px">
          转移服务：
        </div>
        <CarMemberPreview
          :isTransfer="true"
          :data="transferDetailData"
          v-if="[1, 2].includes(Number(props.transferDetail.serviceType))"
        />
        <WeChatPreview
          :isTransfer="true"
          :data="transferDetailData"
          v-else-if="Number(props.transferDetail.serviceType) === 7"
        />
        <AdPreview
          :isTransfer="true"
          :data="transferDetailData"
          v-else-if="Number(props.transferDetail.serviceType) === 10"
        />
        <CluePreview :isTransfer="true" v-else :data="transferDetailData" />
        <div class="wd-h-1px wd-bg-gray-200 wd-my-20px"></div>
      </template>

      <div class="wd-font-bold wd-text-base wd-mb-20px wd-mt-20px">
        订单服务明细：
      </div>
      <template v-for="(item, index) in data.serviceCfgs" :key="index">
        <CarMemberPreview
          :data="item"
          v-if="MemberLabels[item.serviceType as MemberType]"
        />
        <WeChatPreview :data="item" v-else-if="item.serviceType === 7" />
        <AdPreview :data="item" v-else-if="item.serviceType === 10" />
        <GoldPreview :data="item" v-else-if="item.serviceType === 12" />
        <CluePreview :data="item" v-else />
      </template>
      <div v-if="showPrice > 0 && data.payTypeName">
        <div class="wd-h-1px wd-bg-gray-200 wd-my-20px"></div>
        <div class="wd-mb-10px">
          <span>付费方式：</span>
          <span>{{ data.payTypeName }}</span>
        </div>
        <template v-if="data.payType === '3'">
          <div class="wd-mb-10px">
            <span>付费时间：</span>
            <span>{{ dayjs(data.payTime).format('YYYY-MM-DD') }}</span>
          </div>
          <div class="wd-mb-10px">
            <span>付款账号：</span>
            <span>{{ data.payNumber }}</span>
          </div>
          <div class="wd-mb-10px">
            <span>流水编号：</span>
            <span>{{ data.transactionNumber }}</span>
          </div>
          <div class="wd-mb-10px">
            <span>付款人：</span>
            <span>{{ data.payUser }}</span>
          </div>
          <div class="wd-mb-10px">
            <span>支付状态：</span>
            <span>已支付</span>
          </div>
          <div class="wd-mb-10px">
            <span>付款方：</span>
            <span>{{ data.payPersonName }}</span>
          </div>
          <div class="wd-mb-10px wd-flex">
            <span>代付凭证：</span>
            <el-image
              v-if="data.payOtherVoucher"
              :src="data.payOtherVoucher"
              class="img-content"
              @click="seeBigImg(0)"
            >
              <template v-slot:placeholder>
                <div class="image-slot">
                  加载中
                  <span class="dot">...</span>
                </div>
              </template>
            </el-image>
          </div>
          <div class="wd-mb-10px wd-flex">
            <span>转账回执：</span>
            <span>{{ data.transferReceiptMsg }}</span>
            <el-image
              v-if="data.transferReceipt"
              :src="data.transferReceipt"
              class="img-content"
              @click="seeBigImg(1)"
            >
              <template v-slot:placeholder>
                <div class="image-slot">
                  加载中
                  <span class="dot">...</span>
                </div>
              </template>
            </el-image>
          </div>
        </template>
      </div>
      <div class="wd-mb-10px">
        <span>备注：</span>
        <span>{{ data.remark }}</span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :disabled="time > 0" @click="submit">
          {{ time > 0 ? time + 's' : '提交订单' }}
        </el-button>
      </span>
    </template>
    <choose-show-image ref="showImage" />
  </el-dialog>
</template>

<script setup lang="ts">
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import CarMemberPreview from './carMemberPreview.vue'
import CluePreview from './cluePreview.vue'
import WeChatPreview from './weChatPreview.vue'
import { MemberLabels, MemberType } from '@/components/orderService/config'
import { useCountDown } from '@/hooks/count'
import AdPreview from './adPreview.vue'
import GoldPreview from './goldPreview.vue'

import dayjs from 'dayjs'
const props = defineProps([
  'modelValue',
  'data',
  'isTransfer',
  'showPrice',
  'transferDetail',
  'isUpgrade',
  'isMakeUpDifference'
])
const emits = defineEmits(['update:modelValue', 'submit'])
const { time, start } = useCountDown()
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emits('update:modelValue', val)
  }
})
watch(
  () => dialogVisible.value,
  (val) => {
    if (val) {
      start(8)
    }
  }
)
const showImage = ref()
const seeBigImg = (type: number) => {
  showImage.value.init(
    type === 0 ? data.value.payOtherVoucher : data.value.transferReceipt
  )
}
const data = computed(() => {
  const val = { ...props.data }
  val.serviceCfgs = JSON.parse(val.serviceCfgs)
  if (props.isTransfer) {
    val.shopId = props.transferDetail.shopId
    val.shopName = props.transferDetail.shopName
  }
  if (props.isUpgrade) {
    val.shopId = val.serviceCfgs[0].shopId
    val.shopName = val.serviceCfgs[0].shopName
  }
  console.log(val)
  return val
})
const transferDetailData = computed(() => {
  if ([1, 2].includes(Number(props.transferDetail.serviceType))) {
    const previewData = JSON.parse(localStorage.getItem('previewData')!)
    return {
      serviceType: props.transferDetail.serviceType,
      shopId: props.transferDetail.shopId,
      shopName: props.transferDetail.shopName,
      levelName: props.transferDetail.serviceName,
      orgPrice: previewData.originalPrice,
      discount: previewData.discount || 0,
      price: props.transferDetail.price,
      beginTime: dayjs(previewData.beginTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(previewData.deadlineTime).format('YYYY-MM-DD HH:mm:ss'),
      usedNumber: props.transferDetail.usedNumber,
      restNumber: props.transferDetail.restNumber,
      refundPrice: props.transferDetail.refundPrice
    }
  } else if ([7, 10].includes(Number(props.transferDetail.serviceType))) {
    return {
      ...props.transferDetail
    }
  } else {
    const previewData = JSON.parse(localStorage.getItem('previewData')!)
    return {
      ...props.transferDetail,
      discount: previewData.discount || 0,
      number: props.transferDetail.allNumber,
      orgPrice: previewData.originalPrice,
      serviceName: props.transferDetail.serviceName,
      usedNumber: props.transferDetail.usedNumber,
      restNumber: props.transferDetail.restNumber,
      refundPrice: props.transferDetail.refundPrice,
      itemName:
        props.transferDetail.serviceType === 8
          ? '包年线索包'
          : props.transferDetail.allNumber
          ? `${props.transferDetail.allNumber}条线索流量包`
          : ''
    }
  }
})
const submit = () => {
  emits('submit')
}
</script>

<style scoped>
.img-content {
  display: block;
  top: 0px;
  width: 100px;
  height: 100px;
  object-fit: cover;
  cursor: pointer;
}
</style>
