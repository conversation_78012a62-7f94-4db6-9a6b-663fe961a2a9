<template>
  <el-form
    inline
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="130px"
  >
    <el-form-item label="付费方式：" prop="payWay">
      <el-select
        v-model="form.payWay"
        @change="handleChange"
        value-key="id"
        clearable
        placeholder="请选择"
      >
        <el-option
          v-for="(value, index) in payWays"
          :key="index"
          :disabled="value.disabled"
          :label="value.label"
          :value="value"
        />
      </el-select>
    </el-form-item>
    <template v-if="(form.payWay as PayWay)?.id === '3'">
      <!-- <el-form-item prop="cash" label="支付金额：">
        <el-input v-model="form.cash" placeholder="请输入" clearable />
      </el-form-item> -->
      <el-form-item prop="date" label="付款时间：">
        <div>
          <el-date-picker
            v-model="form.date"
            type="date"
            :default-value="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
            placeholder="开始日期"
            value-format="x"
          />
        </div>
      </el-form-item>
      <el-form-item prop="account" label="付款账号：">
        <el-input
          v-model="form.account"
          placeholder="请输入付款账号"
          clearable
        />
      </el-form-item>
      <el-form-item prop="number" label="流水编号：">
        <el-input
          v-model="form.number"
          placeholder="请输入流水编号"
          clearable
        />
      </el-form-item>
      <el-form-item prop="payer" label="付款人：">
        <el-input v-model="form.payer" placeholder="请输入付款人" clearable />
      </el-form-item>
      <el-form-item label="支付状态：">
        <el-input
          v-model="form.status"
          disabled
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="付款方：" prop="payPerson">
        <el-select
          v-model="form.payPerson"
          value-key="id"
          clearable
          placeholder="个人/企业"
        >
          <el-option
            v-for="(value, index) in payPersnTypes"
            :key="index"
            :label="value.name"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <br />
      <el-form-item label="代付凭证">
        <el-button type="primary" @click="updataImg(0)">上传</el-button>
        <el-image
          v-if="paymentVoucher"
          :src="paymentVoucher"
          class="img-content"
          @click="seeBigImg(0)"
        >
          <template v-slot:placeholder>
            <div class="image-slot">
              加载中
              <span class="dot">...</span>
            </div>
          </template>
        </el-image>
      </el-form-item>
      <el-form-item label="转账回执信息" style="margin-left: 50px" required>
        <div class="flex">
          <el-select v-model="transferType" placeholder="请选择" clearable>
            <el-option label="回执图片" :value="1" />
            <el-option label="回执信息" :value="2" />
          </el-select>
          <div class="ml10">
            <el-input
              v-if="transferType === 2"
              v-model="form.transferReceiptMsg"
              type="textarea"
              :rows="3"
              style="width: 300px"
              placeholder="请输入转账回执"
              clearable
            />
          </div>

          <template v-if="transferType === 1">
            <el-button class="ml10 mr10" type="primary" @click="updataImg(1)"
              >上传</el-button
            >
            <el-image
              v-if="form.transferReceipt"
              :src="form.transferReceipt"
              class="img-content"
              @click="seeBigImg(1)"
            >
              <template v-slot:placeholder>
                <div class="image-slot">
                  加载中
                  <span class="dot">...</span>
                </div>
              </template>
            </el-image>
          </template>
        </div>
      </el-form-item>
    </template>
    <!-- <el-form-item label="备注：" class="wd-ml-10">
      <el-input v-model="form.remark" placeholder="请输入订单备注" clearable />
    </el-form-item> -->
    <choose-show-image ref="showImage" />
    <el-upload
      :show-file-list="false"
      :http-request="httpRequest"
      :on-success="updateImg"
      name="upfile"
      multiple
      style="display: inline-block"
      class="avatar-uploader"
      id="el-upload__input_common"
      action
    />
  </el-form>
</template>

<script setup lang="ts">
import { FormInstance, ElMessage } from 'element-plus'
import { OrderType } from '@/components/orderService/config'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'

import { IOrderParams } from '@/api/order'
import type { IForm, PayWay } from './type'

const props = defineProps(['coverSpreadOrder'])
const emit = defineEmits<{
  (e: 'handleChange', val: PayWay): void
}>()
const handleChange = (val: PayWay) => {
  emit('handleChange', val)
}

const transferType = ref(1)

const form = reactive<IForm>({
  payWay: '',
  cash: '',
  date: '',
  account: '',
  number: '',
  payer: '',
  status: '已支付',
  payPerson: '',
  transferReceipt: '',
  transferReceiptMsg: ''
  // remark: ''
})
const rules = reactive({
  payWay: [{ required: true, message: '请选择付费方式', trigger: 'change' }],
  // cash: [{ required: true, message: '请输入支付金额', trigger: 'blur' }],
  date: [{ required: true, message: '请选择付款时间', trigger: 'change' }],
  account: [{ required: true, message: '请输入付款账号', trigger: 'blur' }],
  number: [{ required: true, message: '请输入流水编号', trigger: 'blur' }],
  payer: [{ required: true, message: '请输入付款人', trigger: 'blur' }],
  payPerson: [{ required: true, message: '请选择付款方', trigger: 'change' }]
  // transferReceipt: [
  //   { required: true, message: '请选择转账回执', trigger: 'blur' }
  // ]
})
const formRef = ref<FormInstance>()
const route = useRoute()
const allWays = computed(() => {
  return [
    {
      id: '66',
      label: '线上支付',
      disabled:
        Number(route.query.type) === OrderType.Transfer &&
        !props.coverSpreadOrder
    },
    // {
    //   id: '1',
    //   label: '微信',
    //   disabled:
    //     Number(route.query.type) === OrderType.Transfer &&
    //     !props.coverSpreadOrder
    // },
    // {
    //   id: '62',
    //   label: '支付宝（收钱吧）',
    //   disabled:
    //     Number(route.query.type) === OrderType.Transfer &&
    //     !props.coverSpreadOrder
    // },
    {
      id: '3',
      label: '转账',
      disabled:
        Number(route.query.type) === OrderType.Transfer &&
        !props.coverSpreadOrder
    },
    { id: '64', label: '账户余额', disabled: props.coverSpreadOrder }
  ]
})
const payPersnTypes = [
  {
    id: 1,
    name: '个人'
  },
  {
    id: 2,
    name: '企业'
  }
]

watchPostEffect(() => {
  if (transferType.value === 2) {
    form.transferReceipt = ''
  }
  if (transferType.value === 1) {
    form.transferReceiptMsg = ''
  }
  if (!transferType.value) {
    form.transferReceiptMsg = ''
    form.transferReceipt = ''
  }
})

const payWays = computed(() => {
  if (Number(route.query.type) !== OrderType.Transfer) {
    return allWays.value.filter((item) => item.id !== '64')
  } else {
    return allWays.value
  }
})
const validate = () => {
  return new Promise<Omit<IOrderParams, 'serviceCfgs'>>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        if (
          !form.transferReceipt &&
          !form.transferReceiptMsg &&
          (form.payWay as PayWay)?.id == '3'
        ) {
          ElMessage.error('请上传转账回执图片或填写转账回执信息')
          return reject()
        }
        return resolve({
          payType: (form.payWay as PayWay).id,
          payTypeName: (form.payWay as PayWay).label,
          price: form.cash,
          payTime: form.date,
          payNumber: form.account,
          transactionNumber: form.number,
          payUser: form.payer,
          paySource: form.payPerson?.id || '',
          payPersonName: form.payPerson?.name || '',
          payOtherVoucher: paymentVoucher.value,
          transferReceipt: form.transferReceipt,
          transferReceiptMsg: form.transferReceiptMsg
          // remark: form.remark
        })
      } else {
        return reject()
      }
    })
  })
}

const { proxy } = getCurrentInstance() as any
const paymentVoucher = ref('')

const showImage = ref()
let type = 0
// 更新图片
const updataImg = (val: number) => {
  ;(
    document.getElementById('el-upload__input_common')!.children[0]
      .children[0] as HTMLElement
  ).click()
  type = val
  // document.querySelector('.el-upload__input').click()
}
const updateImg = (res: any) => {
  if (!res) return
  console.log(res)
  if (res.name) {
    const imgOrgUrl = res.imgOrgUrl
    if (type === 0) {
      paymentVoucher.value = imgOrgUrl
    } else {
      form.transferReceipt = imgOrgUrl
      formRef.value?.clearValidate(['transferReceipt'])
    }

    return
  } else {
    window.$message.error('上传错误')
  }
}
const seeBigImg = (type: number) => {
  showImage.value.init(type === 0 ? paymentVoucher.value : form.transferReceipt)
}
const httpRequest = async (option: any) => {
  proxy.$oss.ossUploadImage(option)
}

defineExpose({
  validate
})
</script>

<style scoped>
.img-content {
  /* display: block; */
  /* position: absolute;
  top: 0px;
  left: 70px; */
  width: 100px;
  /* height: 100px; */
  object-fit: cover;
  cursor: pointer;
}
</style>
