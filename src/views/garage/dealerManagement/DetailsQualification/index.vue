<template>
  <div>
    <div class="head">
      <el-button
        v-if="isAdd"
        type="primary"
        style="margin-left: 20px"
        @click="confirm"
        >保存</el-button
      >
      <el-button type="primary" style="margin-left: 20px" @click="seeNearbyShop"
        >附近经销商</el-button
      >
      <el-button
        type="primary"
        style="margin-left: 20px"
        @click="seeBusinessShop"
        >同营业执照经销商</el-button
      >
      <el-button type="primary" style="margin-left: 20px" @click="seeIdCardShop"
        >同身份证经销商</el-button
      >
    </div>
    <el-row type="flex" style="padding: 0 20px">
      <el-col :span="showRigth ? 12 : 24" class="left">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="130px"
          class="detail-entry"
        >
          <div class="content" style="margin-top: 40px">
            <div class="content-title">
              <span v-if="!isAdd"
                >业务资质（{{
                  ruleForm.type == 2 ? '个体工商户' : '公司'
                }}）</span
              >
              <span v-else
                >业务资质（{{
                  $route.query.type == 2 ? '新增个体工商户' : '新增公司资质'
                }}）</span
              >
              <el-button
                v-if="ruleForm.applyStatus === 1"
                type="primary"
                link
                disabled
                >已审核通过</el-button
              >
              <el-button
                v-if="ruleForm.applyStatus === 2"
                type="primary"
                link
                disabled
                >审核不通过</el-button
              >
            </div>
            <div class="content-content">
              <el-form-item v-if="!isAdd" label="变更理由">
                <el-input disabled v-model="reasonStr" type="text" />
              </el-form-item>
              <el-form-item v-if="isAdd" prop="shopId" label="经销商ID">
                <el-input
                  v-model="ruleForm.shopId"
                  @blur="handleBlur"
                  :maxlength="50"
                />
              </el-form-item>
              <el-form-item
                prop="registrationName"
                :class="{
                  red:
                    showRigth &&
                    ruleForm.registrationName !== original.registrationName
                }"
                label="营业执照名称"
              >
                <el-input
                  v-model="ruleForm.registrationName"
                  :maxlength="50"
                  type="text"
                />
              </el-form-item>
              <el-form-item v-if="!isAdd" label="公司类型">
                <span>{{ ruleForm.type == 2 ? '个体工商户' : '公司' }}</span>
              </el-form-item>
              <el-form-item
                prop="registrationNumber"
                :class="{
                  red:
                    showRigth &&
                    ruleForm.registrationNumber !== original.registrationNumber
                }"
                label="统一社会信用代码"
              >
                <el-input
                  v-model="ruleForm.registrationNumber"
                  :maxlength="50"
                  type="text"
                />
              </el-form-item>
              <el-form-item
                prop="identificationName"
                :class="{
                  red:
                    showRigth &&
                    ruleForm.identificationName !== original.identificationName
                }"
                label="法人名称"
              >
                <el-input
                  v-model="ruleForm.identificationName"
                  :maxlength="50"
                />
                <div
                  style="color: red"
                  v-if="
                    !isAdd &&
                    ruleForm.defaultIdentificationName &&
                    ruleForm.defaultIdentificationName !==
                      ruleForm.identificationName
                  "
                >
                  法人名称与{{
                    ruleForm.type == 2 ? '公司' : '个体工商户'
                  }}不一致
                </div>
              </el-form-item>
              <el-form-item
                prop="identificationNumber"
                :class="{
                  red:
                    showRigth &&
                    ruleForm.identificationNumber !==
                      original.identificationNumber
                }"
                label="法人身份证"
              >
                <el-input
                  v-model="ruleForm.identificationNumber"
                  :maxlength="50"
                  style="width: calc(100% - 120px)"
                />
                <el-button
                  v-if="ruleForm.identificationNumber"
                  type="primary"
                  size="small"
                  @click="seeMobile(ruleForm.identificationNumber)"
                  >查看身份证号</el-button
                >
                <div
                  style="color: red"
                  v-if="
                    !isAdd &&
                    ruleForm.defaultIdentificationNumber &&
                    ruleForm.defaultIdentificationNumber !==
                      ruleForm.identificationNumber
                  "
                >
                  法人身份证与{{
                    ruleForm.type == 2 ? '公司' : '个体工商户'
                  }}不一致
                </div>
              </el-form-item>
              <el-form-item label="营业执照图片">
                <div>
                  <el-upload
                    :show-file-list="false"
                    :http-request="httpRequest"
                    :on-success="updateRegistrationImg"
                    accept=".jpg, .png"
                    style="display: inline-block"
                    class="avatar-uploader"
                    action
                  >
                    <el-button class="wd-mr-20px" type="primary">
                      {{ isAdd ? '新增' : '修改' }}</el-button
                    >
                  </el-upload>
                  <div v-if="ruleForm.registrationImage">
                    <el-image
                      :lazy="true"
                      :src="ruleForm.registrationImage"
                      fit="cover"
                      class="img-content"
                      @click="seeBigImg(ruleForm.registrationImage)"
                    >
                      <template v-slot:placeholder>
                        <div class="image-slot">
                          加载中
                          <span class="dot">...</span>
                        </div>
                      </template>
                    </el-image>
                  </div>
                </div>
              </el-form-item>
              <!-- uid=36（伏星羽） -->
              <el-form-item label="身份证图片">
                <div>
                  <el-upload
                    :show-file-list="false"
                    :http-request="httpRequestCard"
                    :on-success="updateIdentificationImg"
                    accept=".jpg, .png"
                    style="display: inline-block"
                    class="avatar-uploader"
                    action
                  >
                    <el-button class="wd-mr-20px" type="primary">{{
                      isAdd ? '新增' : '修改'
                    }}</el-button>
                  </el-upload>
                  <div v-if="identificationImage">
                    <el-image
                      v-for="(imageUrl, index) in identificationImage
                        .split(',')
                        .filter((imageUrl) => imageUrl)"
                      :key="index"
                      :lazy="true"
                      :src="$filters.replaceImgUrl(imageUrl)"
                      fit="cover"
                      class="img-content"
                      @click="seeBigImg(imageUrl, true)"
                    >
                      <template v-slot:placeholder>
                        <div class="image-slot">
                          加载中
                          <span class="dot">...</span>
                        </div>
                      </template>
                    </el-image>
                  </div>
                </div>
              </el-form-item>
              <div
                class="wd-pl-20px wd-pt-20px wd-flex"
                style="border-top: 1px solid gainsboro"
                :style="showRigth ? { width: '200%' } : ''"
              >
                <div class="wd-flex-1">
                  <span>申请角色</span>
                  <el-checkbox-group
                    v-if="!isAdd"
                    :disabled="
                      ruleForm.applyStatus === 1 &&
                      ![36, 381, 170].includes(uid)
                    "
                    class="wd-flex wd-flex-col wd-mt-20px wd-pl-10px"
                    v-model="checkedRoles"
                  >
                    <div
                      v-for="(item, index) in applyServiceList"
                      :key="index"
                      class="wd-flex wd-items-center"
                    >
                      <el-checkbox :label="item" @change="checkChange(item)">
                        <div class="wd-w-70px">{{ item.name }}</div>
                      </el-checkbox>
                      <el-radio-group
                        v-model="item.status"
                        size="small"
                        @change="radioChange(item)"
                        :class="{
                          'point-none':
                            ruleForm.applyStatus === 1 &&
                            ![36, 381, 170].includes(uid)
                        }"
                      >
                        <el-radio-button label="0">通过</el-radio-button>
                        <el-radio-button label="1">不通过</el-radio-button>
                      </el-radio-group>
                    </div>
                  </el-checkbox-group>
                  <el-checkbox-group
                    v-else
                    class="wd-flex wd-flex-col wd-mt-20px wd-pl-10px"
                    v-model="checkedRoles"
                  >
                    <el-checkbox
                      v-for="(item, index) in applyServiceList"
                      :key="index"
                      :disabled="item.disabled"
                      :label="item"
                    >
                      {{ item.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                <div v-if="opendRoles.length" class="wd-flex-1">
                  <span>已开通角色</span>
                  <div
                    class="wd-flex wd-flex-col wd-mt-10px wd-pl-10px"
                    v-for="(item, index) in opendRoles"
                    :key="index"
                  >
                    <div class="wd-flex wd-items-center">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
              </div>

              <div
                v-if="ruleForm.applyStatus === 0 || $route.query.isEdit"
                style="margin: 20px"
              >
                <el-button
                  v-if="checkedRoles && checkedRoles.length"
                  type="success"
                  @click="() => confirm()"
                  >提交</el-button
                >
                <template v-else>
                  <el-button type="primary" @click="() => confirm(1)"
                    >通过</el-button
                  >
                  <el-button type="danger" @click="() => confirm(2)"
                    >不通过</el-button
                  >
                </template>
              </div>
            </div>
          </div>
        </el-form>
      </el-col>
      <el-col v-show="showRigth" :span="12">
        <el-form
          ref="original"
          :model="original"
          label-width="130px"
          class="detail-entry"
        >
          <div class="content" style="margin-top: 40px">
            <p class="content-title">原业务资质</p>
            <div class="content-content">
              <el-form-item label="营业执照名称">
                <el-input
                  v-model="original.registrationName"
                  type="text"
                  readonly
                />
              </el-form-item>
              <el-form-item label="统一社会信用代码">
                <el-input
                  v-model="original.registrationNumber"
                  type="text"
                  readonly
                />
              </el-form-item>
              <el-form-item label="法人名称">
                <el-input v-model="original.identificationName" readonly />
              </el-form-item>
              <el-form-item label="法人身份证">
                <el-input
                  v-model="original.identificationNumber"
                  readonly
                  style="width: calc(100% - 120px)"
                />
                <el-button
                  v-if="original.identificationNumber"
                  type="primary"
                  size="small"
                  @click="seeMobile(original.identificationNumber)"
                  >查看身份证号</el-button
                >
              </el-form-item>
              <el-form-item label="营业执照图片">
                <el-image
                  v-if="original.registrationImage"
                  :lazy="true"
                  :src="original.registrationImage"
                  fit="cover"
                  class="img-content"
                  @click="seeBigImg(original.registrationImage)"
                >
                  <template v-slot:placeholder>
                    <div class="image-slot">
                      加载中
                      <span class="dot">...</span>
                    </div>
                  </template>
                </el-image>
                <div v-else>无图片</div>
              </el-form-item>
              <!-- uid=36（伏星羽） -->
              <el-form-item v-if="[36].includes(uid)" label="身份证图片">
                <template v-if="original.identificationImage">
                  <el-image
                    v-for="(imageUrl, index) in original.identificationImage
                      .split(',')
                      .filter((imageUrl) => imageUrl)"
                    :key="index"
                    :lazy="true"
                    :src="$filters.replaceImgUrl(imageUrl)"
                    fit="cover"
                    class="img-content"
                    @click="seeBigImg(imageUrl, true)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                </template>
                <div v-else>无图片</div>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <choose-show-image ref="showImage" />
    <reject-notice ref="rejectNotice" @confirm="confirmRejection" />
    <nearby-shop
      ref="nearbyShop"
      :latitude="ruleForm.latitude"
      :longitude="ruleForm.longitude"
      :shop-id="ruleForm.shopId"
    />
    <business-shop
      ref="businessShop"
      :shop-id="ruleForm.shopId"
      :id="ruleForm.id"
      :isUseV2="true"
      :isQualification="true"
    />
    <idCard-shop
      ref="idCardShop"
      :shop-id="ruleForm.shopId"
      :id="ruleForm.id"
      :isUseV2="true"
      :isQualification="true"
    />
    <ChooseSeePhoneNew ref="seePhone" />
  </div>
</template>

<script>
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import {
  ShopQualificationDetail,
  ShopQualificationApprove,
  addQualification,
  ShopQualificatioRefuse,
  getQualificationDetail
} from '@/api/garage'
import { mapGetters } from 'vuex'
import RejectNotice from './reject-notice.vue'
import NearbyShop from '../components/nearbyShop.vue'
import IdCardShop from '../components/idCardShop.vue'
import BusinessShop from '../components/businessShop.vue'
import { replaceImgUrl } from '@/filters'
import ChooseSeePhoneNew from '@/components/Dialog/ChooseSeePhoneNew.vue'

const services = [
  {
    id: 2,
    name: '售新车',
    checked: false,
    disabled: false
  },
  {
    id: 14,
    name: '二手车',
    checked: false,
    disabled: false
  },
  {
    id: 18,
    name: '驾考',
    checked: false,
    disabled: false
  },
  {
    id: 17,
    name: '租车',
    checked: true,
    disabled: false
  }
]
export default {
  name: 'DetailsQualification',
  components: {
    ChooseShowImage,
    RejectNotice,
    NearbyShop,
    IdCardShop,
    BusinessShop,
    ChooseSeePhoneNew
  },
  data() {
    return {
      opendRoles: [],
      applyServiceList: [],
      checkedRoles: [],
      applyedRoles: [],
      serviceList: JSON.parse(JSON.stringify(services)),
      showRigth: '', // 是否显示右侧信息
      ruleForm: {
        shopId: '',
        registrationName: '',
        registrationNumber: '',
        identificationName: '',
        identificationNumber: ''
      }, // 申请信息
      original: {}, // 原申请信息
      rules: {
        shopId: [
          { required: true, message: '请输入经销商ID', trigger: 'blur' }
        ],
        registrationName: [
          { required: true, message: '请输入营业执照名称', trigger: 'blur' }
        ],
        registrationNumber: [
          {
            required: true,
            message: '请输入统一社会信用代码',
            trigger: 'blur'
          }
        ],
        identificationName: [
          { required: true, message: '请输入法人名称', trigger: 'blur' }
        ],
        identificationNumber: [
          { required: true, message: '请输入法人身份证', trigger: 'blur' }
        ]
      },
      identificationImage: '',
      reasonList: {
        1: '业务资质信息填写有误',
        2: '营业执照更新',
        3: '业务资质提交有误',
        4: '其它'
      },
      pass: '',
      reasonStr: '' // 变更理由
    }
  },
  computed: {
    ...mapGetters(['uid']),
    isAdd() {
      return this.$route.query.type
    },
    isCanceledRole() {
      const checkedRolesMap = this.checkedRoles.reduce((acc, cur) => {
        acc[cur.id] = true
        return acc
      }, {})
      for (const item of this.applyedRoles) {
        if (!checkedRolesMap[item.id]) {
          return true
        }
      }
      return false
    }
  },
  activated() {
    this.tip = ''
    if (!this.isAdd) {
      this.ruleForm = {}
    } else {
      this.ruleForm = {
        shopId: '',
        registrationName: '',
        registrationNumber: '',
        identificationName: '',
        identificationNumber: ''
      }
      this.ruleForm.registrationImage = ''
      this.identificationImage = ''
      this.checkedRoles = []
    }
    this.original = {}
    this.getData()
  },
  methods: {
    async handleBlur() {
      if (!this.ruleForm.shopId) return
      try {
        const res = await getQualificationDetail({
          shopId: this.ruleForm.shopId,
          type: this.$route.query.type
        })
        this.serviceList = JSON.parse(JSON.stringify(services))
        let data = res.data.data
        Object.keys(data).map((key) => {
          this.ruleForm[key] = data[key]
          this.identificationImage = this.ruleForm.identificationImage || ''
        })
        const arr = data.shopAttr ? data.shopAttr.split(',') : []
        this.applyedRoles = arr.map((item) => {
          return {
            id: item
          }
        })
        this.checkedRoles = this.serviceList.filter((item) =>
          arr.includes(item.id.toString())
        )
        const checkedIds = this.checkedRoles.map((item) => item.id)
        for (let item of this.serviceList) {
          if (checkedIds.includes(item.id)) {
            item.disabled = true
          }
        }
        const unOpendRoles = this.serviceList.filter(
          (item) => !checkedIds.includes(item.id)
        )
        this.unCheckedIds = unOpendRoles.map((item) => item.id)
        if (this.checkedRoles.length || unOpendRoles.length) {
          this.applyServiceList = JSON.parse(
            JSON.stringify(this.checkedRoles)
          ).concat(unOpendRoles)
          console.log(
            '🚀 ~ handleBlur ~ me.applyServiceList:',
            this.applyServiceList
          )
        }
        this.reasonStr =
          data.otherReason || this.reasonList[data.changeReason] || ''
      } catch (e) {
        console.log(e)
      }
    },
    handleRejectCancel() {
      this.isPass = false
    },

    async httpRequest(option) {
      this.$oss.ossUploadImage(option)
    },
    async httpRequestCard(option) {
      option.idCard = true
      this.$oss.ossUploadImage(option)
    },
    getData() {
      const me = this
      if (!me.$route.query.id) {
        me.applyServiceList = [
          { id: 2, name: '售新车', checked: false, disabled: false },
          { id: 14, name: '二手车', checked: false, disabled: false },
          { id: 18, name: '驾考', checked: false, disabled: false },
          { id: 17, name: '租车', checked: false, disabled: false }
        ]
        return
      }
      ShopQualificationDetail({
        id: me.$route.query.id
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data
          // 初始化申请信息组件
          if (data.originalDetail && data.originalDetail.identificationName) {
            Object.keys(data.originalDetail).map((key) => {
              // 原申请信息
              me.original[key] =
                data.originalDetail[key] === null
                  ? ''
                  : data.originalDetail[key]
            })

            me.showRigth = true
          }
          if (data.originalDetail && data.originalDetail.shopAttr) {
            const arr = data.originalDetail.shopAttr
              ? data.originalDetail.shopAttr.split(',')
              : []
            me.opendRoles = JSON.parse(
              JSON.stringify(
                me.serviceList.filter((item) =>
                  arr.includes(item.id.toString())
                )
              )
            )
          }
          // 申请信息
          if (data.applyDetail) {
            Object.keys(data.applyDetail).map((key) => {
              me.ruleForm[key] = data.applyDetail[key]
              me.identificationImage = me.ruleForm.identificationImage || ''
            })
            const arr = data.applyDetail.shopAttr
              ? data.applyDetail.shopAttr.split(',')
              : []
            me.applyedRoles = arr.map((item) => {
              return {
                id: item
              }
            })

            me.checkedRoles = me.serviceList.filter((item) => {
              return arr.includes(item.id.toString())
            })

            const checkRoleJson = JSON.parse(
              data.applyDetail.shopAttrJson || '[]'
            )
            if (checkRoleJson.length) {
              const roleIds = checkRoleJson.map((item) => item.id)
              me.checkedRoles = me.serviceList.filter((item) =>
                roleIds.includes(item.id)
              )
              me.checkedRoles = me.checkedRoles.map((item) => {
                const jsonRole = checkRoleJson.find(
                  (check) => check.id === item.id
                )
                return {
                  ...item,
                  status: jsonRole.status
                }
              })
            }
            const ids = me.opendRoles.map((item) => item.id)
            const checkedIds = me.checkedRoles.map((item) => item.id)
            const unOpendRoles = me.serviceList.filter(
              (item) => !ids.includes(item.id) && !checkedIds.includes(item.id)
            )
            if (me.checkedRoles.length || unOpendRoles.length) {
              me.applyServiceList = JSON.parse(
                JSON.stringify(me.checkedRoles)
              ).concat(unOpendRoles)
            }
            me.reasonStr =
              data.applyDetail.otherReason ||
              me.reasonList[data.applyDetail.changeReason] ||
              ''
          }
        }
      })
    },
    // 大图查看图片
    seeBigImg(link, flag) {
      this.$refs.showImage.init(flag ? replaceImgUrl(link) : link)
    },
    updateRegistrationImg(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        const imgOrgUrl = res.imgOrgUrl
        this.ruleForm.registrationImage = imgOrgUrl
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },

    updateIdentificationImg(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        const imgOrgUrl = res.imgOrgUrl
        this.ruleForm.identificationImage = res.imgUrl
        this.identificationImage = imgOrgUrl
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 通过
    confirm(pass) {
      const me = this
      me.pass = pass || ''
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          // if (!this.checkedRoles.length) {
          //   return this.$message.error('请选择申请角色')
          // }
          if (!this.ruleForm.registrationImage) {
            return this.$message.error('请选择营业执照图片')
          }
          if (!this.identificationImage) {
            return this.$message.error('请选择身份证图片')
          }
          // if (this.isCanceledRole) {
          //   this.isPass = true
          //   this.$refs.rejectNotice.init('shop')
          //   return
          // }

          if (this.isAdd) {
            this.addQualificationHandle()
          } else {
            const unChecked = this.checkedRoles.filter((item) => !item.status)
            if (unChecked && unChecked.length) {
              return this.$message.error(`请选择${unChecked[0].name}是否通过`)
            }

            const rejectArr = this.checkedRoles.filter(
              (role) => role.status == '1'
            )
            if (rejectArr && rejectArr.length) {
              this.$refs.rejectNotice.init(rejectArr)
              return
            }

            if (pass === 2) {
              return this.$refs.rejectNotice.init([])
            }

            this.handleQualificationApprove()
          }
        } else {
          window.scrollTo(0, 200)
        }
      })
    },
    async addQualificationHandle() {
      try {
        const res = await addQualification({
          ...this.ruleForm,
          type: this.$route.query.type,
          shopAttr: this.checkedRoles
            .filter((item) => this.unCheckedIds.includes(item.id))
            .map((item) => item.id)
            .join(',')
        })
        this.$router.push({
          name: 'qualificationAudit'
        })
      } catch (error) {
        console.log(error)
      }
    },
    handleQualificationApprove(mes) {
      const shopAttrJson = this.checkedRoles.map((item) => {
        return {
          ...item,
          shopAttr: item.id,
          attrStatus: item.status
        }
      })
      ShopQualificationApprove({
        id: this.$route.query.id,
        ...this.ruleForm,
        shopAttr: this.checkedRoles.map((item) => item.id).join(','),
        refuseReason: mes,
        shopAttrJson: JSON.stringify(shopAttrJson),
        pass: this.pass
      })
        .then((response) => {
          this.ruleForm.applyStatus = 1
          this.isEditStatus = true
          this.$refs.rejectNotice.close()
          this.$router.push({
            name: 'qualificationAudit'
          })
        })
        .catch(() => {})
    },
    // 确认拒绝
    confirmRejection(mes) {
      this.handleQualificationApprove(mes)
    },
    // 查看附近经销商
    seeNearbyShop() {
      const me = this
      if (!(me.ruleForm.latitude && me.ruleForm.longitude)) {
        return this.$message.error('校对定位信息缺失，无法查看附近经销商')
      }
      me.$refs.nearbyShop.setStatus()
    },
    // 查看同营业执照的经销商
    seeBusinessShop() {
      const me = this
      me.$refs.businessShop.setStatus()
    },
    // 查看同身份证的经销商
    seeIdCardShop() {
      const me = this
      me.$refs.idCardShop.setStatus()
    },
    // 查看手机号码
    seeMobile(mobile) {
      const data = {
        decryptContent: mobile,
        source: 'DetailsQualification',
        type: 3, // 3身份证解密
        logModule: 20, // 20资质审核详情
        shopStatus: '' // 1为用户，商家可以传空
      }
      this.$refs.seePhone && this.$refs.seePhone.init(data)
    },
    radioChange(item) {
      // 这里radio change的时候, 第一次变化会导致checkedRoles的值变得奇怪, 删除再加回来
      let index = this.checkedRoles.findIndex((i) => i.id === item.id)
      if (index > -1) {
        this.checkedRoles.splice(index, 1)
      }
      this.checkedRoles.push(item)
    },
    checkChange(item) {
      const roles = this.checkedRoles.find((i) => i.id === item.id)
      if (!roles) {
        item.status = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.head {
  padding: 20px 0 0 20px;
}
.content-title {
  margin-bottom: 20px;
  font-size: 18px;
}
.head-title {
  padding-left: 20px;
}
.img-content {
  height: 120px;
  width: 120px;
  margin-right: 5px;
}
.avatar-uploader {
  margin-bottom: 20px;
}
</style>
