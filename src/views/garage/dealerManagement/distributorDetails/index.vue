<template>
  <div style="">
    <div class="head">
      <el-button type="success" @click="readyData">保存</el-button>
      <el-button type="danger" @click="goBack">取消</el-button>
      <el-button type="primary" @click="seeNearbyShop">附近经销商</el-button>
      <el-button type="primary" @click="seeBusinessShop"
        >同营业执照经销商</el-button
      >
      <el-button type="primary" @click="seeIdCardShop"
        >同身份证经销商</el-button
      >
      <el-select v-model="ruleForm.shopMemberType">
        <el-option
          v-for="(value, index) in storeCategory"
          :key="index"
          :label="index"
          :value="value"
          disabled
          @click="ruleForm.shopMemberType = value"
        />
      </el-select>
      <el-button type="success" @click="showGoldClue">金币赠送记录</el-button>
      <el-button type="success" @click="deleteShopLog">店铺操作日志</el-button>
      <el-tag type="error" v-if="data.riskTransRegist" class="ml5"
        >疑似关联封禁店铺重开</el-tag
      >
      <!-- <el-button type="primary" @click="seeMemberConfig">付费会员配置</el-button>
                    <el-button type="primary" @click="seeCluePackageConfig">线索包配置</el-button>
                    <el-button type="primary" @click="$refs.SecondHandlerConfig.init()">优质二手车商配置</el-button>
                    <el-button type="success" @click="showGoldClue">金币赠送记录</el-button>
                    <el-button type="success" @click="showSendClue">线索赠送设置</el-button>
                    <el-button type="success" @click="showTestDrive">试驾线索赠送设置</el-button>
                    <el-button type="success" @click="showSendWechatClue">微信线索赠送设置</el-button>
                    <el-button type="primary" @click="seeDrivingTest">驾考报名设置</el-button>
                    <el-input v-if="queryDistanceAccess" v-model="queryDistance" placeholder="询价距离设置" type="text" class="price"
                      ><template slot="append">km</template></el-input
                    > -->
    </div>
    <div style="">
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="ruleForm"
        label-width="120px"
        class="detail-entry"
      >
        <div class="content">
          <p class="content-title">基本信息</p>
          <div class="content-content">
            <el-form-item label="经销商名称：" prop="shopName">
              <el-input
                v-focus
                v-model="ruleForm.shopName"
                type="text"
                maxlength="'30'"
                style="width: 200px"
                @blur="verificationShopName"
              />
            </el-form-item>
            <el-form-item label="店铺状态：">
              <el-select
                v-model="shopStatus"
                :class="{
                  'is-choice-delete': shopStatus === '3',
                  'is-choice-examine': shopStatus === '0' || shopStatus === '4'
                }"
                style="width: 200px"
                :disabled="shopStatusAccess"
              >
                <el-option
                  v-for="(value, index) in statusObjectList"
                  :key="index"
                  :label="index"
                  :value="value"
                  :disabled="!!(value === '3' && data.authBrandNum > 0)"
                  @click="
                    () => {
                      value === '3' &&
                        data.authBrandNum > 0 &&
                        $message.warning('有授权开启品牌无法关闭店铺')
                    }
                  "
                />
              </el-select>
              &ensp;&ensp;{{ deleteReasonContent }}
            </el-form-item>
            <el-form-item>
              <seleted-user
                ref="selectUser"
                :name="'申请人名称：'"
                :placeholder="'请输入并选择用户'"
                :label-width="'100px'"
                @sendData="setUid"
              />
            </el-form-item>
            <el-form-item label="店铺号码：">
              <el-input
                v-focus
                v-model="ruleForm.mobile"
                maxlength="'20'"
                style="width: 200px"
              />&ensp;<el-button
                v-if="ruleForm.mobile"
                type="primary"
                size="small"
                @click="seeMobile(ruleForm.mobile, 1)"
                >查看号码</el-button
              >
              <!-- <el-button type="primary" size="small" @click="viewingVirtualNumbers">查看虚拟号</el-button> -->
            </el-form-item>
            <el-form-item label="联系方式：">
              <!-- <el-input v-focus v-model="ruleForm.virtualMobile" maxlength="'20'" style="width: 200px" />&ensp -->
              <el-select v-model="ruleForm.virtualMobile" style="width: 200px">
                <el-option
                  v-for="(item, index) in contactsList"
                  :key="index"
                  :label="item.phone"
                  :value="item.phone"
                />
              </el-select>
              <el-button
                type="primary"
                size="small"
                @click="seeMobile(ruleForm.virtualMobile, 1)"
                >查看号码</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="viewingVirtualNumbers"
                >查看虚拟号</el-button
              >
            </el-form-item>
            <br />
            <el-form-item label="座机电话：">
              <el-input
                v-focus
                v-model="ruleForm.tel"
                style="width: 200px"
                maxlength="'20'"
              />
            </el-form-item>
            <el-form-item label="微信号：">
              <!-- <el-input v-focus v-model="ruleForm.merchantWechat" maxlength="'20'" style="width: 200px" /> -->
              <el-select v-model="ruleForm.merchantWechat" style="width: 200px">
                <el-option
                  v-for="(item, index) in contactsList"
                  :key="index"
                  :label="item.wechatNumber"
                  :value="item.wechatNumber"
                />
              </el-select>
              <el-button
                v-if="ruleForm.merchantWechat"
                type="primary"
                size="small"
                @click="seeMobile(ruleForm.merchantWechat, 2)"
                >查看微信号</el-button
              >
              <el-tooltip
                effect="dark"
                content="店铺微信号会直接展示给客户，微信号为电话号码，请保证微信可以通过电话搜索添加"
                placement="top-start"
              >
                <el-icon>
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="所属地区：" prop="region">
              <div @click="getRef">
                <el-input
                  v-focus
                  v-model="ruleForm.region"
                  type="text"
                  style="width: 200px"
                />
              </div>
            </el-form-item>
            <el-form-item label="详细地址：" prop="addr">
              <el-input
                v-focus
                v-model="ruleForm.addr"
                type="text"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="校对定位：">
              <div @click="showPostion()">
                <el-input
                  v-focus
                  v-model="ruleForm.locationAddress"
                  readonly
                  type="text"
                  style="width: 200px"
                />
              </div>
            </el-form-item>
            <el-form-item label="店铺面积：">
              <el-input
                v-focus
                v-model="ruleForm.shopArea"
                maxlength="'20'"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="开店时间：">
              <el-date-picker
                v-model="ruleForm.openTime"
                format="YYYY-MM-DD"
                type="datetime"
                value-format="x"
                placeholder="选择日期时间"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="销售范围：">
              <el-select v-model="ruleForm.saleMode">
                <el-option
                  v-for="(value, index) in salesScopeAll"
                  :key="index"
                  :label="value.name"
                  :value="value.value"
                  style="width: 200px"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="推荐商家：">
              <el-input
                v-model="ruleForm.recommendShop"
                disabled
                style="width: 200px"
              />
            </el-form-item>
            <br />
            <p style="margin-left: 30px; font-weight: bold; font-size: 16px">
              售新车经销商
            </p>
            <el-form-item label="店铺级别：">
              <el-select v-model="ruleForm.shopLevel">
                <el-option
                  v-for="(value, index) in shopLevelList"
                  :key="index"
                  :label="index"
                  :value="value"
                  @click="ruleForm.shopLevel = value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="店铺属性：">
              <el-select v-model="ruleForm.storeAttributes">
                <el-option
                  v-for="(value, index) in storeAttributes"
                  :key="index"
                  :label="index"
                  :value="value"
                  @click="ruleForm.storeAttributes = value"
                />
              </el-select>
            </el-form-item>
            <p class="line"></p>
            <div style="margin-bottom: 40px">
              <div style="margin-bottom: 10px">
                <span class="content-title">人员管理</span
                ><el-button
                  size="small"
                  type="primary"
                  style="margin-bottom: 10px"
                  @click="addMobile"
                  >新增人员</el-button
                >
              </div>
              <div style="display: flex">
                <el-table
                  max-height="739"
                  :data="contactsListData"
                  style="margin-right: 20px"
                  border
                >
                  <el-table-column
                    prop="phone"
                    label="联系电话"
                    align="center"
                    width="110"
                  >
                    <template v-slot="scope"
                      ><span>{{ scope.row.phone }}</span
                      >&ensp;<el-button
                        v-if="scope.row.phone"
                        type="primary"
                        size="small"
                        @click="seeMobile(scope.row.phone, 1)"
                        >查看号码</el-button
                      ></template
                    >
                  </el-table-column>
                  <el-table-column label="角色" align="center">
                    <template #default="scope">
                      {{
                        scope.row.roleType === 1
                          ? '管理员'
                          : scope.row.roleType === 2
                          ? '店长'
                          : '店员'
                      }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="username"
                    label="用户名"
                    align="center"
                  >
                    <template #default="{ row }">
                      <p>{{ row.username }}</p>
                      <p v-if="row.groupName" style="color: red">
                        <span style="margin-right: 2px"
                          >已{{ row.groupName }}</span
                        >
                        <span>{{ row.banTime }}</span>
                      </p>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="联系人" align="center" />
                  <el-table-column
                    prop="name"
                    label="是否接收线索"
                    align="center"
                  >
                    <template #default="{ row }">
                      <el-switch
                        v-model="row.canReceiveClue"
                        disabled
                        @change="changeSwitch(row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="接收微信线索"
                    align="center"
                  >
                    <template v-slot="scope">
                      <el-switch
                        v-model="scope.row.canReceiveWechatClue"
                        @change="changeChatSwitch(scope.row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="查看电话线索"
                    align="center"
                  >
                    <template v-slot="scope">
                      <el-switch
                        v-model="scope.row.canViewMobileClue"
                        @change="changeMObileSwitch(scope.row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="分配驾考线索"
                    align="center"
                  >
                    <template v-slot="scope">
                      <el-switch
                        v-model="scope.row.canReceiveDrivingSchoolClue"
                        @change="changeDrivingTestSwitch(scope.row)"
                      />
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop="name"
                    label="查看租车线索"
                    align="center"
                  >
                    <template v-slot="scope">
                      <el-switch
                        v-model="scope.row.canViewRentalClue"
                        @change="changeRentalClueSwitch(scope.row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="分配收车线索"
                    align="center"
                  >
                    <template v-slot="scope">
                      <el-switch
                        v-model="scope.row.canReceiveCollectionCarClue"
                        @change="changeCollectionClueSwitch(scope.row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="110px">
                    <template v-slot="scope">
                      <span
                        style="color: #409eff"
                        @click="updataMobile(scope.row)"
                        >编辑</span
                      >&ensp;
                      <span style="color: #409eff" @click="delMobile(scope.row)"
                        >删除</span
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <p class="line"></p>
            <div class="wd-flex">
              <div style="margin-bottom: 30px">
                <div style="margin-bottom: 10px">
                  <span class="content-title">会员信息</span>
                  <el-button type="primary" @click="creatOrder"
                    >创建订单</el-button
                  >
                </div>
                <div class="member-info">
                  <div class="member-info-type">新车会员</div>
                  <div
                    v-if="
                      vipNewCarInfo.shopMemberTimeList &&
                      vipNewCarInfo.shopMemberTimeList.length
                    "
                    class="member-info-item"
                  >
                    <div
                      v-for="(item, index) in vipNewCarInfo.shopMemberTimeList"
                      :key="index"
                    >
                      <span class="member-info-level"
                        >会员级别：{{ item.levelName }}</span
                      >
                      <span class="member-info-timer"
                        >会员有效期：{{
                          $date.format(item.beginTime, 'YYYY年MM月DD日')
                        }}
                        -
                        {{ $date.format(item.endTime, 'YYYY年MM月DD日') }}</span
                      >
                    </div>
                  </div>
                  <div v-else class="member-info-item">
                    <div>
                      <span class="member-info-level">会员级别：无</span>
                      <span class="member-info-timer">会员有效期：无</span>
                    </div>
                  </div>
                  <div>
                    <!-- <el-button type="primary" @click="seeMemberConfig(1)"
                    >新车会员缴费</el-button
                  > -->
                    <el-button
                      type="success"
                      style="display: inline-block"
                      @click="seePayRecords(1)"
                      >会员付费记录</el-button
                    >
                  </div>
                </div>

                <div class="member-info">
                  <div class="member-info-type">年包</div>
                  <div v-if="isPackYears" class="member-info-item">
                    <div
                      v-for="(item, index) in shopCluePackageInfoList"
                      :key="index"
                    >
                      <span class="member-info-level">
                        <!-- 会员级别：{{ item.levelName }} -->
                      </span>
                      <span class="member-info-timer"
                        >年包有效期：{{
                          $date.format(item.beginTime, 'YYYY年MM月DD日')
                        }}
                        -
                        {{ $date.format(item.endTime, 'YYYY年MM月DD日') }}</span
                      >
                    </div>
                  </div>
                  <div v-else class="member-info-item">
                    <div>
                      <span class="member-info-timer">年包有效期：无</span>
                    </div>
                  </div>
                </div>

                <div class="member-info">
                  <div class="member-info-type">二手车会员</div>
                  <div
                    v-if="
                      vipOldCarInfo.shopMemberTimeList &&
                      vipOldCarInfo.shopMemberTimeList.length
                    "
                    class="member-info-item"
                  >
                    <div
                      v-for="(item, index) in vipOldCarInfo.shopMemberTimeList"
                      :key="index"
                    >
                      <span class="member-info-level"
                        >会员级别：{{ item.levelName }}</span
                      >
                      <span class="member-info-timer"
                        >会员有效期：{{
                          $date.format(item.beginTime, 'YYYY年MM月DD日')
                        }}
                        -
                        {{ $date.format(item.endTime, 'YYYY年MM月DD日') }}</span
                      >
                    </div>
                  </div>
                  <div v-else class="member-info-item">
                    <div>
                      <span class="member-info-level">会员级别：无</span>
                      <span class="member-info-timer">会员有效期：无</span>
                    </div>
                  </div>
                  <div>
                    <!-- <el-button type="primary" @click="$refs.SecondHandlerConfig.init()">二手车商缴费</el-button> -->
                    <!-- <el-button type="primary" @click="seeMemberConfig(2)"
                    >二手车商缴费</el-button
                  > -->
                    <el-button
                      type="success"
                      style="display: inline-block"
                      @click="seePayRecords(2)"
                      >会员付费记录</el-button
                    >
                  </div>
                </div>
              </div>
              <div>
                <el-table :data="clueTableData">
                  <el-table-column align="center" label="新车">
                    <el-table-column label="询价">
                      <el-table-column
                        prop="baseRemaining1"
                        label="本月线索剩余基础"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="paidRemaining1"
                        label="本月线索剩余付费"
                      >
                      </el-table-column>
                      <el-table-column prop="baseConsume1" label="本月消耗基础">
                      </el-table-column>
                      <el-table-column prop="paidConsume1" label="本月消耗付费">
                      </el-table-column>
                    </el-table-column>
                    <el-table-column align="center" label="试驾">
                      <el-table-column prop="paidRemaining2" label="线索剩余" />
                      <el-table-column prop="paidConsume2" label="本月消耗" />
                    </el-table-column>
                    <el-table-column align="center" label="微信">
                      <el-table-column
                        label="本月线索消耗"
                        prop="paidConsume3"
                      />
                      <el-table-column
                        label="本月线索剩余"
                        prop="paidRemaining3"
                      />
                    </el-table-column>
                  </el-table-column>
                  <el-table-column align="center" label="二手车">
                    <el-table-column align="center" label="收车/估价">
                      <el-table-column label="剩余" prop="paidRemaining4" />
                      <el-table-column label="消耗" prop="paidConsume4" />
                    </el-table-column>
                    <el-table-column align="center" label="询价">
                      <el-table-column label="剩余" prop="paidRemaining7" />
                      <el-table-column label="消耗" prop="paidConsume7" />
                    </el-table-column>
                  </el-table-column>
                  <el-table-column align="center" label="驾考">
                    <el-table-column align="center" label="驾考">
                      <el-table-column
                        prop="paidRemaining5"
                        label="本月线索剩余"
                      />
                      <el-table-column prop="paidConsume5" label="本月消耗" />
                    </el-table-column>
                  </el-table-column>
                  <el-table-column align="center" label="租车">
                    <el-table-column align="center" label="租车">
                      <el-table-column
                        label="本月线索剩余"
                        prop="paidRemaining6"
                      />
                      <el-table-column label="本月消耗" prop="paidConsume6" />
                    </el-table-column>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
        <p class="line"></p>
        <div class="content">
          <p class="content-title">业务信息</p>
          <div class="content-content">
            <div class="type-operate">
              <el-button
                type="primary"
                @click="seeOperateLogRecords('second', 0)"
                >业务日志</el-button
              >
              <el-button
                type="primary"
                @click="seeOperateLogRecords('third', 1)"
                >处罚日志</el-button
              >
              <el-button type="primary" @click="handleContractManagement()"
                >合同管理</el-button
              >
            </div>
            <div class="bussness-data">
              <!-- 角色处理 -->
              <div>
                <span style="margin: 0 0 10px 0">角色</span>
                <div
                  v-for="(item, index) in labelTitle"
                  :key="index"
                  @click="changLableRole(item)"
                  style="border: 1px solid #eee; width: 180px; height: 40px"
                  :class="
                    roleCode === item.code ? 'roleCodeShow' : 'roleCodeHide'
                  "
                >
                  <div style="display: flex; align-items: center">
                    <span style="width: 120px; padding-left: 10px">{{
                      item.name
                    }}</span>
                    <el-switch
                      v-if="!['加油站', '公共业务'].includes(item.name)"
                      v-model="item.status"
                      @change="operateRole(item)"
                    />
                  </div>
                </div>
              </div>
              <!-- 业务处理 -->
              <div>
                <div>
                  <span>业务</span>
                  <span style="margin-left: 210px">操作</span>
                </div>
                <div
                  v-for="(item, index) in data.serviceRights"
                  :key="index"
                  style="display: flex"
                >
                  <div
                    class="bussness-data-bus"
                    v-if="roleBusinessCode.includes(item.businessType)"
                  >
                    <div class="span-sty-text">
                      <span>{{ item.businessName }}</span>
                      <el-switch
                        v-if="operationCode.includes(item.businessType)"
                        v-model="item.openStatus"
                        @change="
                          () => {
                            operate(item.businessType, item.openStatus, item)
                          }
                        "
                      />
                      <el-tooltip
                        v-if="usedCarBussinessCode.includes(item.businessType)"
                        class="item"
                        effect="dark"
                        placement="top-start"
                      >
                        <template v-slot:content>
                          <div>
                            <div v-if="userPunishInfo.punishInfo">
                              {{ userPunishInfo.punishInfo }}
                            </div>
                            处罚期至{{ item.penaltyDueTime }}
                          </div>
                        </template>
                        <el-icon
                          v-show="item.penaltyDueTime"
                          class="business-day"
                        >
                          <WarningFilled />
                        </el-icon>
                      </el-tooltip>
                    </div>

                    <!-- <p class="limit-date" v-if="item.penaltyDueTime">处罚期至{{ item.penaltyDueTime }}</p> -->
                  </div>
                  <div
                    style="border: 1px solid #eee; width: 800px; height: 40px"
                    v-if="roleBusinessCode.includes(item.businessType)"
                  >
                    <el-form-item>
                      <!-- 电话客户 -->
                      <div
                        v-if="
                          [27].includes(item.businessType) &&
                          data.usedCarMemberAge &&
                          item.openStatus
                        "
                      >
                        入驻 {{ data.usedCarMemberAge }} 年
                        <!-- <el-button
                          size="small"
                          type="success"
                          @click="showDateChoose"
                          >成立时间</el-button
                        > -->
                      </div>
                      <!-- 询价客户 -->
                      <div v-if="item.businessType === 1">
                        <!-- <el-button
                          type="success"
                          size="small"
                          @click="seeCluePackageConfig"
                          >线索包配置</el-button
                        >
                        <el-button
                          type="success"
                          size="small"
                          @click="showSendClue('1')"
                          >线索赠送设置</el-button
                        > -->
                        <el-button
                          type="success"
                          size="small"
                          @click="goToRecord('inquiryRecordList')"
                          >询价纪录</el-button
                        >
                        <el-input
                          :disabled="queryDistanceAccess"
                          style="width: 170px"
                          size="small"
                          v-model="queryDistance"
                          placeholder="询价距离设置"
                          type="text"
                          class="price mt5"
                          ><template v-slot:append>km</template></el-input
                        >
                        <el-button
                          type="success"
                          size="small"
                          @click="goToClue(1)"
                          >线索订单</el-button
                        >
                      </div>
                      <!-- 试驾客户 -->
                      <div v-if="item.businessType === 2">
                        <!-- <el-button
                          size="small"
                          type="success"
                          @click="showSendClue('1')"
                          >试驾线索赠送设置</el-button
                        > -->
                        <el-button
                          size="small"
                          type="success"
                          @click="goToRecord('driveRecord')"
                          >试驾纪录</el-button
                        >
                        <el-button
                          type="success"
                          size="small"
                          @click="goToClue(2)"
                          >线索订单</el-button
                        >
                      </div>
                      <!-- 电话客户 -->
                      <div v-if="[3, 18].includes(item.businessType)">
                        <el-button
                          size="small"
                          type="success"
                          @click="goToRecord('phoneClueRecord')"
                          >电话纪录</el-button
                        >
                      </div>
                      <!-- 直通车 -->
                      <div v-if="item.businessType === 4">
                        <el-button
                          size="small"
                          type="success"
                          @click="goToRecord('CouponOrderList')"
                          >优惠券订单</el-button
                        >
                      </div>
                      <!-- 一口价 -->
                      <div v-if="item.businessType === 5">
                        <el-button
                          size="small"
                          type="success"
                          @click="goToRecord('onePriceRecordList')"
                          >一口价记录</el-button
                        >
                      </div>
                      <!-- 二手车 -->
                      <div v-if="item.businessType === 6">
                        <!-- <el-button size="small" type="success" @click="quicklyQueryUsedCars()">快速查询二手车</el-button> -->
                        <el-button
                          size="small"
                          type="success"
                          @click="toUsedCarOrder()"
                          >二手车订单</el-button
                        >
                        <el-button
                          size="small"
                          type="success"
                          @click="toUsedCarSupply()"
                          >二手车车源</el-button
                        >
                        <el-button
                          size="small"
                          type="success"
                          @click="openMergeOptions()"
                          >合并车源</el-button
                        >
                        <div class="restricted-mode wd-ml-10px">
                          <el-button
                            size="small"
                            type="success"
                            @click="punish(item.businessType, false, item)"
                            >违规处罚</el-button
                          >
                        </div>
                        <div class="restricted-mode wd-ml-10px">
                          <el-button
                            size="small"
                            type="success"
                            @click="seePunishMessage()"
                            >处罚信息</el-button
                          >
                        </div>
                        <div class="restricted-mode wd-ml-10px">
                          <el-button
                            size="small"
                            type="success"
                            @click="seeRelevanceMessage()"
                            >关联信息</el-button
                          >
                        </div>
                        <div class="restricted-mode">
                          <span>受限模式</span
                          ><el-switch
                            v-model="limitation"
                            @change="changeLimitation"
                            :disabled="restrictedMode"
                          />
                        </div>
                        <div class="restricted-mode">
                          <span>个人模板</span
                          ><el-switch
                            v-model="secondTemplate"
                            @change="changeSecondTemplate"
                          />
                        </div>
                      </div>
                      <!-- 店铺活动 -->
                      <div v-if="[7, 20, 21].includes(item.businessType)">
                        <el-button
                          size="small"
                          type="success"
                          @click="chooseActive(item.businessType)"
                          >新建活动</el-button
                        >
                      </div>
                      <!-- 驾考报名 -->
                      <div v-if="item.businessType === 8">
                        <el-button
                          size="small"
                          type="success"
                          @click="seeDrivingTest"
                          >驾考报名设置</el-button
                        >
                        <!-- <el-button
                          type="success"
                          size="small"
                          @click="seeCluePackageConfig"
                          >线索包配置</el-button
                        >
                        <el-button
                          type="success"
                          size="small"
                          @click="showSendClue('1')"
                          >线索赠送设置</el-button
                        > -->
                      </div>
                      <!-- 租车服务 -->
                      <div v-if="item.businessType === 9">
                        <!-- <el-button
                          size="small"
                          type="success"
                          @click="goToRecord('RentCarOrder')"
                          >租车订单</el-button
                        > -->

                        <!-- <el-button
                          type="success"
                          size="small"
                          @click="seeCluePackageConfig"
                          >线索包配置</el-button
                        >
                        <el-button
                          type="success"
                          size="small"
                          @click="showSendClue('1')"
                          >线索赠送设置</el-button
                        > -->
                        <el-button
                          type="success"
                          size="small"
                          @click="setRentalCar"
                          >租车配置</el-button
                        >
                      </div>
                      <div v-if="item.businessType === 10">
                        <el-button
                          size="small"
                          type="success"
                          style="margin-bottom: 10px"
                          @click="maintenanceImg(item.businessType)"
                          >维修图片</el-button
                        >
                      </div>
                      <div v-if="item.businessType === 11">
                        <el-button
                          size="small"
                          type="success"
                          style="margin-bottom: 10px"
                          @click="rescueImg(item.businessType)"
                          >救援图片</el-button
                        >
                      </div>
                      <!-- 在售车型 -->
                      <div v-if="item.businessType === 13">
                        <el-button
                          size="small"
                          type="success"
                          style="margin-bottom: 10px"
                          @click="punish(item.businessType, false, item)"
                          >违规处罚</el-button
                        >
                      </div>
                      <!-- 微信客户 -->
                      <div v-if="[14, 19].includes(item.businessType)">
                        <!-- <el-button
                          size="small"
                          type="success"
                          @click="showSendClue('2')"
                          >微信线索赠送设置</el-button
                        > -->
                      </div>
                      <el-button
                        v-if="[14, 8, 9].includes(item.businessType)"
                        type="success"
                        :class="item.businessType !== 14 ? 'wd-ml-10px' : ''"
                        size="small"
                        @click="goToClue(item.businessType)"
                        >线索订单</el-button
                      >
                      <el-button
                        v-if="[8, 9].includes(item.businessType)"
                        type="success"
                        class="wd-ml-10px"
                        size="small"
                        @click="punish(item.businessType, false, item)"
                        >违规处罚</el-button
                      >
                      <div v-if="item.businessType === 24">
                        <div class="restricted-mode">
                          <span>收车数据源</span>
                          <div
                            class="switch-box"
                            @click="changeDataSource(secondTestClue, 0)"
                          >
                            <el-switch
                              v-model="secondTestClue"
                              :beforeChange="() => false"
                            />
                          </div>
                        </div>
                        <div class="restricted-mode">
                          <span>估价数据源</span>
                          <div
                            class="switch-box"
                            @click="changeDataSource(secondAssessClue, 1)"
                          >
                            <el-switch
                              v-model="secondAssessClue"
                              :beforeChange="() => false"
                            />
                          </div>
                        </div>
                      </div>
                      <div v-if="[25].includes(item.businessType)">
                        <el-button
                          size="small"
                          type="success"
                          @click="setUpSales(item.businessType)"
                          >设置销售</el-button
                        >
                      </div>
                    </el-form-item>
                  </div>
                </div>
              </div>
              <!-- 操作展示模块 -->
              <div>
                <!-- 店铺活动 -->
                <div style="margin-left: 30px">
                  <el-form-item label-width="48px">
                    <div>
                      <span>店铺活动</span>
                      <el-table
                        ref="showActiveList"
                        :data="showActiveList"
                        :height="160"
                        row-key="showActiveList"
                        border
                        style="width: 300px"
                      >
                        <el-table-column
                          prop="name"
                          label="活动名称"
                          align="center"
                        />
                      </el-table>
                      <el-pagination
                        v-model:current-page="activePage"
                        :page-size="5"
                        :total="activeTotal"
                        background
                        layout="total, prev, pager, next, jumper"
                        style="justify-content: center; margin-top: 10px"
                        @current-change="setShowActiveList"
                      />
                    </div>
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>
        </div>
        <p class="line"></p>
        <div class="content">
          <p class="content-title">在售品牌</p>
          <div class="content-content">
            <div class="button-box-style">
              <el-button type="primary" @click="chooseBrand(13)">
                新加品牌
              </el-button>
              <el-button type="primary" @click="createBrandOrder(false)">
                创建年包品牌订单
              </el-button>
              <el-button
                v-if="isPackYears"
                type="primary"
                @click="createBrandOrder(true)"
              >
                添加年包品牌
              </el-button>
              <el-button
                v-if="isPackYears"
                type="primary"
                @click="openReplaceBrand"
              >
                替换年包品牌
              </el-button>
              <el-button type="primary" @click="openSaleBrandLog"
                >操作日志</el-button
              >
              <div class="ml20">在售品牌数：{{ brandSaleNum }}个</div>
              <div class="ml20">
                商家版在售展示品牌总数：{{
                  isPackYears
                    ? '年包不限'
                    : ruleForm.brandNum > 0
                    ? ruleForm.brandNum + '个'
                    : '0个'
                }}
              </div>
            </div>
            <el-table
              v-loading="loadingCircle"
              :data="brandList"
              highlight-current-row
              max-height="40vh"
              border
              @row-dblclick="rowDoubleClick"
            >
              <el-table-column
                prop="brandId"
                label="品牌名称"
                align="center"
                min-width="120"
              >
                <template v-slot="scope">
                  <span v-if="scope.row.brandId">
                    {{ scope.row.brandName }}
                  </span>
                  <el-autocomplete
                    v-else
                    v-model="scope.row.brandName"
                    :fetch-suggestions="querySearchBrandList"
                    :trigger-on-focus="false"
                    clearable
                    placeholder="请输入内容"
                    @select="addShopBrandList"
                    @focus="updateBrand = scope.row"
                  />
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="updateTime"
                align="center"
                label="是否授权"
              >
                <template v-slot="scope">
                  <el-switch
                    v-model="scope.row.authStatus"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    @change="changeStatus(scope.row)"
                  />
                </template>
              </el-table-column> -->
              <el-table-column
                prop="goodsNum"
                width="100"
                label="可售车辆数"
                align="center"
              />
              <el-table-column
                prop="trailGoodNames"
                min-width="100"
                label="可试驾车辆"
                align="center"
              />
              <el-table-column align="center" label="是否授权" width="90">
                <template v-slot="scope">
                  <div class="mandate-style">
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click.stop="goToRecord('AuthorizationAudit')"
                      >{{ scope.row.authStatus ? '有' : '无' }}</el-button
                    >
                    <el-tooltip
                      v-if="scope.row.showWarn && !scope.row.authStatus"
                      effect="dark"
                      content="已超时，商家未补全授权信息"
                      placement="top"
                    >
                      <el-icon color="red" class="icon-style">
                        <WarnTriangleFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否有效" align="center" width="100">
                <template v-slot="scope">
                  <el-switch
                    v-model="scope.row.valid"
                    @change="changeValid(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="商家版在售展示"
                align="center"
                width="130"
              >
                <template v-slot="scope">
                  <el-switch
                    :disabled="!scope.row.valid"
                    v-model="scope.row.showStatus"
                    @change="changeShowStatus(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="摩托范在售展示"
                align="center"
                width="130"
              >
                <template v-slot="scope">
                  {{
                    (scope.row.authStatus ||
                      (!scope.row.authStatus && !scope.row.showWarn)) &&
                    scope.row.valid &&
                    scope.row.showStatus &&
                    (scope.row.payStatus || !isPackYears)
                      ? '展示'
                      : '不展示'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="年包付费" align="center" width="150">
                <template v-slot="scope">
                  {{ scope.row.payStatus ? '已付费' : '未付费' }}
                  <span v-if="!scope.row.replaceStatus" class="replace-status">
                    (审核中)
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="售卖渠道" align="center" width="180">
                <template v-slot="scope">
                  <SaleChannel
                    v-model="scope.row.channel"
                    :defaultOptions="getChannelOptions(scope.row.brandId)"
                    :immediate="true"
                    title=""
                    :item="{
                      shopId: scope.row.shopId,
                      brandId: scope.row.brandId,
                      channel: scope.row.channel
                    }"
                    @change="selectChannel(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="trailGoodNames"
                label="是否允许厂家查看数据"
                align="center"
                width="180"
              >
                <template v-slot="scope">
                  <el-switch
                    v-model="scope.row.factoryCheck"
                    @change="changeFactoryCheck(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="180">
                <template v-slot="scope">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click.stop="deleteShopCar(scope.row, scope.$index)"
                    >删除</el-button
                  >
                  <el-button
                    :disabled="scope.row.canGetTotal === false"
                    type="primary"
                    link
                    size="small"
                    @click.stop="addAllBrandGoods(scope.row)"
                    >添加全部在售车型</el-button
                  >
                  <!-- <el-button
                    :disabled="scope.row.canGetTotal === false"
                    type="primary"
                    link
                    size="small"
                    @click.stop="clearAllBrandGoods(scope.row)"
                    >移除全部</el-button
                  > -->
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <p class="line"></p>
        <div class="content">
          <p class="content-title">资质信息</p>
          <div class="content-content">
            <el-row :gutter="24">
              <el-col
                v-for="(item, index) in ruleForm.qualificationApplyInfos || []"
                :key="index"
                :span="6"
              >
                <div class="wd-mb-10px">
                  {{ item.type === 1 ? '公司业务资质' : '个体工商户业务资质' }}
                </div>
                <el-form-item label="营业执照名称" prop="registrationName">
                  {{ item.registrationName }}
                </el-form-item>
                <br />
                <el-form-item
                  style="margin-left: -10px"
                  label="统一社会信用代码"
                  label-width="130px"
                  prop="registrationNumber"
                >
                  {{ item.registrationNumber }}
                </el-form-item>
                <br />
                <el-form-item label="法人名称" prop="identificationName">
                  {{ item.identificationName }}
                </el-form-item>
                <br />
                <el-form-item label="法人身份证号" prop="identificationNumber">
                  {{ item.identificationNumber }} &ensp;<el-button
                    v-if="item.identificationNumber"
                    type="primary"
                    size="small"
                    @click="seeMobile(item.identificationNumber, 3)"
                    >查看身份证号</el-button
                  >
                </el-form-item>
                <br />
                <el-form-item label="营业执照">
                  <el-image
                    v-if="item.registrationImage"
                    :src="item.registrationImage"
                    class="img-content"
                    @click="seeBigImg(item.registrationImage)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
                <br />
                <!-- uid=36（伏星羽） -->
                <el-form-item label="法人身份证">
                  <el-image
                    v-if="item.identificationImage"
                    :src="$filters.replaceImgUrl(item.identificationImage)"
                    class="img-content"
                    @click="seeBigImg(item.identificationImage, true)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="wd-mb-10px">
              直付通签约状态 {{ signStatusList[ruleForm.signStatus] }}
            </div>
            <div class="wd-mb-10px">
              收款信息（二手车）<br />
              <el-form-item label="支付宝帐号" prop="identificationNumber">
                {{ ruleForm.payAccount }}
              </el-form-item>
              <br />
              <el-form-item label="手机号码" prop="identificationNumber">
                {{ ruleForm.payPhone }}
              </el-form-item>
            </div>

            <!-- <el-row :gutter="24" style="margin-top: 200px">
              <el-col :span="12">
                <el-form-item label="封面门头">
                  <el-button type="primary" @click="updataImg('cover')"
                    >修改</el-button
                  >
                  <el-image
                    v-if="outsideImage"
                    :src="outsideImage"
                    class="img-content"
                    @click="seeBigImg(outsideImage)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-button
                  type="success"
                  class="see-more-img"
                  @click="seeMoreImg"
                  >店内/维修 环境/维修救援</el-button
                >
              </el-col>
            </el-row> -->
            <!-- <el-row :gutter="24" style="margin-top: 200px">
              <el-col :span="12">
                <el-form-item label="救援车辆行驶证">
                  <el-button type="primary" @click="updataImg('driving')"
                    >修改</el-button
                  >
                  <el-image
                    v-if="drivingLicenseImgs"
                    :src="$filters.replaceImgUrl(drivingLicenseImgs)"
                    class="img-content"
                    @click="seeBigImg(drivingLicenseImgs, true)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="救援车辆照片">
                  <el-button type="primary" @click="updataImg('photo')"
                    >修改</el-button
                  >
                  <el-image
                    v-if="carImgs"
                    :src="carImgs"
                    class="img-content"
                    @click="seeBigImg(carImgs)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
            </el-row> -->

            <!-- <el-row :gutter="24" style="margin-top: 200px">
                            <el-col :span="12">
                              <el-form-item label="地域骑士授权证书">
                                <el-button type="primary" @click="updataImg('knight')">修改</el-button>
                                <el-image v-if="knightImgs" :src="knightImgs" class="img-content" @click="seeBigImg(knightImgs)">
                                  <div slot="placeholder" class="image-slot">
                                    加载中
                                    <span class="dot">...</span>
                                  </div>
                                </el-image>
                                <div v-else>无图片</div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <p>地域骑士授权证书有效时间:</p>
                              <el-date-picker
                                v-model="DateRange"
                                :picker-options="pickerOptions"
                                format="YYYY-MM-DD"
                                type="daterange"
                                disabled
                                start-placeholder="授权开始日期"
                                end-placeholder="授权结束日期"
                              />
                            </el-col>
                          </el-row> -->
          </div>
        </div>
        <p class="line"></p>
        <div class="content">
          <p class="content-title">店铺资料</p>
          <div class="content-content">
            <el-row :gutter="24">
              <el-col :span="4">
                <el-form-item label="封面门头">
                  <el-button type="primary" @click="updataImg('cover')"
                    >修改</el-button
                  >
                  <el-image
                    v-if="outsideImage"
                    :src="outsideImage"
                    class="img-content"
                    @click="seeBigImg(outsideImage)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="店内图片">
                  <el-button type="primary" @click="inStoreImg">修改</el-button>
                  <el-image
                    v-if="ruleForm.insideImage"
                    :src="ruleForm.insideImage"
                    class="img-content"
                    @click="inStoreImg"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="4">
                <el-form-item label="救援车辆行驶证">
                  <el-button type="primary" @click="updataImg('driving')"
                    >修改</el-button
                  >
                  <el-image
                    v-if="drivingLicenseImgs"
                    :src="$filters.replaceImgUrl(drivingLicenseImgs)"
                    class="img-content"
                    @click="seeBigImg(drivingLicenseImgs, true)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="救援车辆照片">
                  <el-button type="primary" @click="updataImg('photo')"
                    >修改</el-button
                  >
                  <el-image
                    v-if="carImgs"
                    :src="carImgs"
                    class="img-content"
                    @click="seeBigImg(carImgs)"
                  >
                    <template v-slot:placeholder>
                      <div class="image-slot">
                        加载中
                        <span class="dot">...</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else>无图片</div>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-button
                  type="success"
                  class="see-more-img"
                  @click="seeMoreImg"
                  >店内/维修 环境/维修救援</el-button
                >
              </el-col> -->
            </el-row>
          </div>
        </div>
        <p class="line"></p>
        <div class="content">
          <p class="content-title">数据权限</p>
          <div class="content-content">
            <el-row :gutter="24" style="margin-top: 20px">
              <el-col :span="24">
                <el-form-item label="数据信息权限">
                  <div class="dot">请上传商家数据保密协议</div>
                  <br />
                  <div>
                    <el-upload
                      :show-file-list="false"
                      :http-request="httpRequestFile"
                      :on-success="onSuccessFile"
                      :limit="30"
                      multiple
                      accept="*"
                      name="upfile"
                      action
                      style="width: 150px"
                    >
                      <el-button type="primary" class="fl-left"
                        >上传隐私协议文件</el-button
                      >&ensp;
                    </el-upload>
                  </div>
                  <br />
                </el-form-item>
                <div
                  v-if="downRecordAuthorDocList.length"
                  style="margin-left: 120px"
                >
                  <div
                    v-for="(link, index) in downRecordAuthorDocList"
                    class="wd-mt-10px wd-flex wd-items-center"
                    :key="link"
                    style="font-size: 14px"
                  >
                    <span>文件链接：{{ link }}</span>
                    <el-icon
                      @click="handleDelete(index)"
                      class="wd-ml-10px wd-cursor-pointer"
                      ><Delete
                    /></el-icon>
                  </div>
                </div>
                <p style="margin-left: 120px">
                  <span style="font-size: 14px; margin-right: 20px"
                    >同意开启商家自主下载客户记录数据</span
                  >
                  <el-switch
                    v-model="ruleForm.agreeOpenDownRecord"
                    :disabled="!downRecordAuthorDocList.length"
                    inline-prompt
                    :active-value="1"
                    :inactive-value="0"
                    active-text="开"
                    inactive-text="关"
                  />
                </p>
              </el-col>
            </el-row>
            <!-- <el-row :gutter="24" style="margin-top: 200px">
                            <el-col :span="12">
                              <el-form-item label="地域骑士授权证书">
                                <el-button type="primary" @click="updataImg('knight')">修改</el-button>
                                <el-image v-if="knightImgs" :src="knightImgs" class="img-content" @click="seeBigImg(knightImgs)">
                                  <div slot="placeholder" class="image-slot">
                                    加载中
                                    <span class="dot">...</span>
                                  </div>
                                </el-image>
                                <div v-else>无图片</div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <p>地域骑士授权证书有效时间:</p>
                              <el-date-picker
                                v-model="DateRange"
                                :picker-options="pickerOptions"
                                format="YYYY-MM-DD"
                                type="daterange"
                                disabled
                                start-placeholder="授权开始日期"
                                end-placeholder="授权结束日期"
                              />
                            </el-col>
                          </el-row> -->
          </div>
        </div>
      </el-form>
      <el-dialog
        v-model="showVirtualNumbers"
        :before-close="handleClose"
        title="查看虚拟号"
        center
        width="600px"
        append-to-body
      >
        <el-form label-width="100px">
          <el-form-item label="虚拟号" required>
            <el-input
              v-focus
              v-model="virtualNumbers"
              readonly
              type="number"
              style="width: 290px"
            />
            <!-- <el-button
              :disabled="virtualSwitch"
              type="primary"
              @click="BindingVirtualNumbers"
              >绑定虚拟号</el-button
            > -->
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog v-model="activeStatus" title="活动信息" class="dialog-content">
        <create-activity ref="createActivity" @addActive="addActive" />
      </el-dialog>
      <c-area ref="cArea" @backArea="backArea" />
      <BindBrandDialog
        ref="BindBrandDialog"
        :uid="uid"
        @add-brand="getBrandData"
      />
      <choose-phone
        ref="ChoosePhone"
        :source="'distributorDetails'"
        @sendPhoneData="sendPhoneData"
      />
      <choose-sales ref="ChooseSales" @sendSalesData="sendSalesData" />
      <choose-dialog-car ref="ChooseDialogCar" @sendCarData="sendCarData" />
      <c-m-position ref="cPosition" @mapData="mapPosition" />
      <choose-show-image ref="showImage" />
      <el-upload
        :show-file-list="false"
        :http-request="httpRequest"
        :on-success="updateImg"
        name="upfile"
        multiple
        style="display: inline-block"
        class="avatar-uploader"
        id="el-upload__input_common"
        action
      />
      <nearby-shop
        ref="nearbyShop"
        :latitude="ruleForm.latitude"
        :longitude="ruleForm.longitude"
        :shop-id="ruleForm.shopId"
      />
      <business-shop ref="businessShop" :shop-id="ruleForm.shopId" />
      <member-config ref="memberConfig" :shop-id="ruleForm.shopId" />
      <CluePackageConfig ref="cluePackageConfig" :shop-id="ruleForm.shopId" />
      <idCard-shop ref="idCardShop" :shop-id="ruleForm.shopId" />
      <ChooseSeePhoneNew ref="seePhone" />
      <vip-pay-logs ref="vipPayLogs" :shop-id="ruleForm.shopId" />
      <SendClue ref="sendClue" :shop-id="ruleForm.shopId" />
      <!-- <SendClue ref="testDrive" :shop-id="ruleForm.shopId" :clueType="2" /> -->
      <!-- <SendWechatClue ref="wechatClue" :shop-id="ruleForm.shopId" :clueType="5" /> -->
      <GoldClue ref="goldClue" :shop-id="ruleForm.shopId" />
      <DrivingTest
        :cityName="ruleForm.cityName"
        :drivingPenaltyDueTime="drivingPenaltyDueTime"
        ref="drivingTest"
        :shop-id="ruleForm.shopId"
        @drivingTestCallback="getData"
      />
      <CommonRejectNotice
        ref="rejectNotice"
        :show-reject-reason="false"
        @confirmRejection="confirmRejection"
      />
      <SecondHandlerConfig
        ref="SecondHandlerConfig"
        :shop-id="ruleForm.shopId"
        :shop-level="secondHandlerShopLevel"
      />
      <CloseBusinessDialog
        :shopId="$route.query.id"
        :showClose="roleCode !== 14"
        @cancel="cancel"
        @submit="updateServiceStatus"
        ref="closeBusiness"
      />
      <CloseBusinessDialog2
        :shopId="$route.query.id"
        @cancel="cancel"
        @submit="updateServiceStatus"
        ref="closeBusiness2"
      />
      <OpenBusinessDialog
        @cancel="cancel"
        :service="service"
        @submit="updateServiceStatus"
        ref="openBusiness"
      />
      <RegisterDialog @sucess="sucess" ref="RegisterDialog"></RegisterDialog>
      <MergeOptions ref="MergeOptions"></MergeOptions>
      <SetRentalCar ref="setRentalCar" :shopId="$route.query.id"></SetRentalCar>
      <IntegrationLogs
        :sourceId="$route.query.id"
        :isShop="1"
        :shop-id="ruleForm.shopId"
        ref="integrationLogs"
      />
      <SecondHandCarPenaltyInformation
        :sourceId="$route.query.id"
        :isShop="1"
        :shop-id="ruleForm.shopId"
        ref="secondHandCarPenaltyInformation"
      />
      <MaintenanceImg ref="maintenanceImg" @getData="getData" />
      <RescueImg ref="rescueImg" @getData="getData" />
      <InStoreImg @setInStoreData="setInStoreData" ref="inStoreImg" />
      <SetUpSales ref="setUpSales" @setUpSalesData="setUpSalesData" />
      <ContractManagement ref="contractManagementRef"></ContractManagement>
      <el-dialog v-model="dateChooseVisible" title="成立时间" width="500">
        <div>
          <div style="color: red" class="wd-mb-10px">
            请与商家营业执照的成立时间保持一致
          </div>

          <el-date-picker
            v-model="startDate"
            value-format="x"
            :default-value="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
            type="date"
            placeholder="请选择成立时间"
          />
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dateChooseVisible = false">取消</el-button>
            <el-button type="primary" @click="setDate"> 确定 </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
    <!-- <AddBrandOrder ref="addBrandOrderRef" @success="brandOrderReturn" /> -->
    <SaleBrandLog ref="saleBrandLogRef" />
    <ReplaceBrand ref="replaceBrandRef" @success="getData" />
    <MerchantAssociatedInfo ref="merchantAssociatedInfoRef" />
  </div>
</template>

<script>
import {
  InfoFilled,
  WarningFilled,
  Delete,
  WarnTriangleFilled
} from '@element-plus/icons-vue'
import { resetData } from '@/utils'
import { verificationCard, matchChiEng } from '@/utils/validate'
import CArea from '@/components/area/c-area.vue'
import cMPosition from '@/components/map/c-m-position-new.vue'
import BindBrandDialog from '@/components/Dialog/BindBrandDialog.vue'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import ChoosePhone from '@/components/Dialog/ChoosePhone.vue'
import ChooseSales from '@/components/Dialog/ChooseSales.vue'
import ChooseDialogCar from '@/components/Dialog/ChooseDialogCar.vue'
import CreateActivity from '../components/createActivity.vue'
import NearbyShop from '../components/nearbyShop.vue'
import IdCardShop from '../components/idCardShop.vue'
import BusinessShop from '../components/businessShop.vue'
import MemberConfig from '../components/memberConfig.vue'
import CluePackageConfig from '../components/cluePackageConfig.vue'
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
import CloseBusinessDialog from '../components/closeBusinessDialog.vue'
import CloseBusinessDialog2 from '../components/closeBusinessDialog2.vue'
import OpenBusinessDialog from '../components/openBusinessDialog.vue'
import RegisterDialog from '@/components/Dialog/registerDialog.vue'
import SetRentalCar from '../components/set-rental-car.vue'
import IntegrationLogs from '../components/integration-logs.vue'
import MaintenanceImg from '../components/maintenance-img.vue'
import RescueImg from '../components/rescue-img.vue'
import InStoreImg from '../components/In-store-img.vue'
import SetUpSales from '../components/set-up-sales.vue'
import SecondHandCarPenaltyInformation from '../components/secondHandCarPenaltyInformation.vue'
import {
  bindVirtualMobile,
  virtualMobile,
  merchantDetail,
  saveTagList,
  merchantUpdate,
  saveNewShop,
  queryShopInfoByName,
  updateBrandAuthStatus,
  deleteShopBrand,
  addShopBrand,
  addAllBrandGoods,
  merchantServiceOpen,
  merchantServiceOpen2,
  MerchantsLimitedCompile,
  MerchantInfoUpdateBenefit,
  setCanReceiveWechatClue,
  addShopContacts,
  updateShopContact,
  delShopContact,
  livrInfoAttrOpen,
  setCanReceiveMobileClue,
  setCanReceiveRentalClue,
  setDrivingTestClue,
  updateShopBrandChannel,
  getQESaleCarCnt,
  updateFactoryCheck,
  deleteShopNotice,
  setCanReceiveColletionCarClue,
  clueSourceUpdate,
  setEstablishTime,
  getEstablishTime,
  cancelPunish,
  getShopClueData,
  updateShowStatus,
  updateValid,
  getBrandChannelCfg
} from '@/api/garage'
import { searchBrand } from '@/api/articleModule'
import { mapGetters } from 'vuex'
import {
  statusObjectList,
  shopLevelList,
  storeAttributes,
  storeCategory,
  businessRoleLabel,
  businessRoleLabel2,
  memberLevelPeople,
  memberTypeEnum,
  memberButtonEnum,
  shopDetailTypeEnum,
  channelList
} from '@/utils/enum'
import { laterPickerOptions } from '@/utils/configData'
import ChooseSeePhoneNew from '@/components/Dialog/ChooseSeePhoneNew.vue'
import VipPayLogs from '../components/vip-pay-logs.vue'
import SendClue from '../components/send-clue.vue'
// import SendWechatClue from '../components/send-wechat-clue.vue'
import GoldClue from '../components/gold-clue.vue'
import CommonRejectNotice from '@/components/Notice/commonRejectNotice.vue'
import SecondHandlerConfig from '../components/secondHandlerConfig.vue'
import DrivingTest from '../components/driving-test.vue'
import MergeOptions from '../components/mergeOptions.vue'
import ContractManagement from '../components/contractManagement.vue'
import { replaceImgUrl } from '@/filters'
import { getUserPunishInfo } from '@/api/usedCarPublish'
import to from 'await-to-js'
import { deepCopy } from '@/utils'
// import AddBrandOrder from '@/components/orderService/AddBrandOrder.vue'
import { postLog } from '@/components/seeLog/SaveLog.js'
import SaleBrandLog from '../components/saleBrandLog.vue'
import dayjs from 'dayjs'
import ReplaceBrand from '../components/replaceBrand.vue'
import MerchantAssociatedInfo from '@/components/Dialog/MerchantAssociatedInfo.vue'
import SaleChannel from '@/components/SaleChannel/index.vue'

export default {
  components: {
    IntegrationLogs,
    Delete,
    CArea,
    BindBrandDialog,
    ChooseShowImage,
    CreateActivity,
    ChoosePhone,
    ChooseSales,
    cMPosition,
    ChooseDialogCar,
    NearbyShop,
    IdCardShop,
    BusinessShop,
    MemberConfig,
    CluePackageConfig,
    SeletedUser,
    ChooseSeePhoneNew,
    VipPayLogs,
    SendClue,
    // SendWechatClue,
    CommonRejectNotice,
    SecondHandlerConfig,
    GoldClue,
    CloseBusinessDialog,
    CloseBusinessDialog2,
    OpenBusinessDialog,
    DrivingTest,
    RegisterDialog,
    MergeOptions,
    InfoFilled,
    WarningFilled,
    SetRentalCar,
    MaintenanceImg,
    RescueImg,
    InStoreImg,
    SetUpSales,
    SecondHandCarPenaltyInformation,
    ContractManagement,
    WarnTriangleFilled,
    // AddBrandOrder,
    SaleBrandLog,
    ReplaceBrand,
    MerchantAssociatedInfo,
    SaleChannel
  },
  name: 'DistributorDetails',
  data() {
    return {
      clueTableData: [],
      startDate: '',
      dateChooseVisible: false,
      showPopover: true,
      sourceId: '',
      drivingPenaltyDueTime: '',
      service: {}, // 当前操作的业务
      shopLevelList: shopLevelList, // 经销商评级列表
      storeAttributes: storeAttributes, // 店铺属性
      storeCategory: storeCategory, // 店铺类别
      labelTitle: businessRoleLabel, // 标签管理
      labelTitle2: businessRoleLabel2,
      pickerOptions: laterPickerOptions,
      channelList,
      memberTypeEnum,
      memberButtonEnum,
      updataImgType: '', // 更新图片type值
      personImage: '', // 法人身份证照片
      personImageShow: '',
      businessImg: '', // 营业执照照片
      outsideImage: '', // 封面门头
      drivingLicenseImgs: '', // 救援车辆行驶证图片
      carImgs: '', // 救援车辆照片
      knightImgs: '', // 地域骑士授权证书照片
      shopStatus: '', // 状态
      activePage: 1, // 活动页码
      activeTotal: 0, // 活动总数
      activeStatus: false, // 增加活动是否显示
      loadingCircle: false, // 操作loading
      shopNameStatus: false, // 店名状态，false为正常
      statusObjectList: statusObjectList, // 状态列表
      editUsername: true,
      showBrandList: [], // 展示的销售品牌
      brandList: [], // 品牌列表（所有）
      activeList: [], // 活动列表
      showActiveList: [], // 展示的活动列表
      contactsList: [], // 所有电话号码集合
      contactsTotal: 0, // 电话号码列表总数
      salesList: [], // 所有销量集合
      showSalesList: [], // 展示的销量情况
      salesPage: 1, // 销量列表页码
      salesTotal: 0, // 销量列表总数
      salesScopeAll: [
        {
          value: '1',
          name: '售本市'
        },
        {
          value: '2',
          name: '售本省'
        },
        {
          value: '4',
          name: '售全国'
        }
        // {
        //   value: '3',
        //   name: '售多地'
        // }
      ], // 销售范围
      serviceTypes: {}, // 服务列表
      tagList: [], // 管理标签列表
      ruleForm: {
        shopLevel: '2',
        relId: '',
        shopArea: '', // 店铺面积
        openTime: '', // 开店时间
        storeAttributes: '',
        category: '', // 店铺类别
        registrationName: '', // 营业执照名称
        identificationName: '', // 法人名称
        identificationNumber: '',
        registrationNumber: '',
        receiveNumber: '', // 经销商收款账户
        shopMemberType: '', // 店铺类别
        topBeginTime: '',
        topEndTime: '',
        queryDistance: '', // 询价距离
        shopMemberInfo: {
          levelId: '',
          levelName: '',
          beginValidPeriod: '',
          endValidPeriod: ''
        },
        downRecordAuthorDoc: '', // 隐私协议文件
        agreeOpenDownRecord: ''
      }, // 提交或保存数据
      queryDistance: '', // 询价距离
      contactsTempArr: {}, // 电话临时存储数据
      salesTempArr: {}, // 销量临时存储数据
      data: {}, // 临时存储数据
      updateBrand: {}, // 更新的品牌数据
      knightBeginTime: '', // 地域骑士开始时间
      knightEndTime: '', // 地域骑士结束时间
      isShowMemberDialog: false, // 是否显示付费会员配置弹框
      memberLevelList: [], // 会员等级列表
      forbidRegistrationName: false, // 是否禁止修改营业执照名称
      forbidRegistrationNumber: false, // 是否禁止修改统一社会信用代码
      deleteReasonContent: '', // 拒绝原因，中文
      rejectNoticeContent: [
        '无效店铺',
        '附近经销商',
        '同营业执照经销商',
        '同身份证经销商'
      ],
      showVirtualNumbers: false, // 虚拟号相关
      virtualSwitch: false,
      virtualNumbers: '', // 虚拟号
      memberLevelPeople: memberLevelPeople, //memberType 1: '新车会员',2: '二手车会员',3: '驾校会员',4: '租车会员',
      shopDetailTypeEnum: shopDetailTypeEnum,
      allowAddMemberPeople: false, // 二手车，教考允许添加人员
      isAllowChooseBrand: false, // 是否允许添加经销商
      isRoleOperateArr: [], // 是否是相关角色操作内容
      operateIndex: -1, // 操作下标
      limitation: false, // 受限
      secondTemplate: false, // 个人模板
      vipNewCarInfo: {}, // 会员信息-新车会员
      vipOldCarInfo: {}, // 会员信息-二手车会员
      shopCluePackageInfoList: [], // 线索信息-年包线索
      secondHandlerShopLevel: 0, // 二手车商缴费-等级
      roleCode: 2, // 默认角色
      roleBusinessCode: [1, 2, 3, 4, 5, 7, 10, 11, 13, 14, 15, 22, 25, 26], // 默认角色业务
      operationCode: [1, 2, 3, 4, 5, 7, 10, 11, 14, 22, 25, 26], // 对应角色的业务允许操作code
      userPunishInfo: {}, // 二手车处罚
      oldRuleForm: {},
      downRecordAuthorDocList: [],
      secondTestClue: false, // 收车数据源
      secondAssessClue: false, // 估价数据源
      usedCarBussinessCode: [6, 10, 11, 18, 19, 20, 23, 24], // 二手车所有子业务code
      saleChannelList: [], // 销售渠道
      signStatusList: {
        0: '未签约',
        1: '签约成功',
        2: '签约失败'
      }
    }
  },
  computed: {
    ...mapGetters(['uid']),
    contactsListData() {
      return [...this.contactsList].sort((a, b) => a.roleType - b.roleType)
    },
    canAccess() {
      return (
        parseInt(this.uid) === 115 ||
        parseInt(this.uid) === 45 ||
        parseInt(this.uid) === 36 ||
        parseInt(this.uid) === 149 ||
        parseInt(this.uid) === 170
      ) // uid=170（石静娟） uid=115（刘敏）、uid=45（王长文）、uid=36（伏星宇）149 姚莹莹 381 仓思思
    },
    // 店铺状态修改
    shopStatusAccess() {
      const uidList = [85, 36, 170, 222, 381, 127]
      return !uidList.includes(parseInt(this.uid))
    },
    // 询价距离
    queryDistanceAccess() {
      // uid=115（刘敏）、uid=45（王长文）、uid=36（伏星宇）149 姚莹莹,3 刘学磊 uid=170（石静娟） 381 仓思思
      const uidList = [85, 36, 170, 222, 381, 127]
      return !uidList.includes(parseInt(this.uid))
    },
    // 置顶时间
    topDateRange: {
      get() {
        if (this.ruleForm.topBeginTime && this.ruleForm.topEndTime) {
          return [this.ruleForm.topBeginTime, this.ruleForm.topEndTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.topBeginTime = value[0]
          this.ruleForm.topEndTime = value[1]
        } else {
          this.ruleForm.topBeginTime = ''
          this.ruleForm.topEndTime = ''
        }
      }
    },
    // 地域骑士授权证书有效时间
    DateRange: {
      get() {
        if (this.ruleForm.knightBeginTime && this.ruleForm.knightEndTime) {
          return [this.ruleForm.knightBeginTime, this.ruleForm.knightEndTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.knightBeginTime = value[0]
          this.ruleForm.knightEndTime = value[1]
        } else {
          this.ruleForm.knightBeginTime = ''
          this.ruleForm.knightEndTime = ''
        }
      }
    },
    // 会员有效期
    validityDateRange: {
      get() {
        if (
          this.ruleForm.shopMemberInfo.beginValidPeriod &&
          this.ruleForm.shopMemberInfo.endValidPeriod
        ) {
          return [
            this.ruleForm.shopMemberInfo.beginValidPeriod,
            this.ruleForm.shopMemberInfo.endValidPeriod
          ]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.shopMemberInfo.beginValidPeriod = value[0]
          this.ruleForm.shopMemberInfo.endValidPeriod = value[1]
        } else {
          this.ruleForm.shopMemberInfo.beginValidPeriod = ''
          this.ruleForm.shopMemberInfo.endValidPeriod = ''
        }
      }
    },
    restrictedMode() {
      const endTime =
        this.vipOldCarInfo.shopMemberTimeList &&
        this.vipOldCarInfo.shopMemberTimeList[0] &&
        this.vipOldCarInfo.shopMemberTimeList[0].endTime
      const flag = endTime ? true : false
      return flag
    },
    brandSaleNum() {
      let num = 0
      this.brandList.forEach((v) => {
        if (v.valid) {
          num++
        }
      })
      return num
    },
    isPackYears() {
      return this.shopCluePackageInfoList && this.shopCluePackageInfoList.length
    }
  },
  watch: {
    shopStatus(newValue, oldValue) {
      if (newValue && !oldValue) return
      if (newValue === '3') {
        this.$refs.rejectNotice.init(this.rejectNoticeContent)
      } else {
        this.ruleForm.deleteReason = ''
        this.deleteReasonContent = ''
      }
    },
    brandList(newValue) {
      // uid=115（刘敏）、uid=45（王长文）、uid=36（伏星宇）149 姚莹莹,3 刘学磊 uid=170（石静娟）
      const sign =
        parseInt(this.uid) === 36 ||
        parseInt(this.uid) === 170 ||
        parseInt(this.uid) === 115 ||
        parseInt(this.uid) === 3 ||
        parseInt(this.uid) === 45
      if (newValue && newValue.length >= 10) {
        if (sign) {
          this.isAllowChooseBrand = false
        } else {
          this.isAllowChooseBrand = true
        }
      } else {
        this.isAllowChooseBrand = false
      }
    }
  },
  mounted() {},
  activated() {
    if (this.$route.query.id) {
      this.reviewTime = this.$route.query.reviewTime
      this.updateTime = this.$route.query.updateTime
      this.createTime = this.$route.query.createTime
      this.getData()
      this.getUserPunishInfo()
      this.getDate()
      this.getShopClueTableData()
      this.getChannelList()
    } else {
      this.ruleForm.shopLevel = 0
    }
    this.resetData()
    window.scrollTo(0, 0)
  },
  methods: {
    handleContractManagement() {
      this.$refs.contractManagementRef.init(this.$route.query.id)
    },
    async getShopClueTableData() {
      try {
        const res = await getShopClueData({ shopId: this.$route.query.id })

        if (res.data.code === 0) {
          let list = res.data.data

          const inquire = list.find((item) => item.clueType === 1)
          this.formatData(inquire || {}, 1)
          const tryDriving = list.find((item) => item.clueType === 2)
          this.formatData(tryDriving || {}, 2)
          const wechat = list.find((item) => item.clueType === 5)
          this.formatData(wechat || {}, 3)
          const usedCar = list.find((item) => item.clueType === 9)
          this.formatData(usedCar || {}, 4)
          const driving = list.find((item) => item.clueType === 6)
          this.formatData(driving || {}, 5)
          const rent = list.find((item) => item.clueType === 8)
          this.formatData(rent || {}, 6)
          const enquiry = list.find((item) => item.clueType === 11)
          this.formatData(enquiry || {}, 7)
          this.clueTableData = [
            {
              ...inquire,
              ...wechat,
              ...usedCar,
              ...driving,
              ...rent,
              ...tryDriving,
              ...enquiry
            }
          ]
          // console.log('clueTableData-----------', this.clueTableData)
        }
      } catch (error) {
        console.log(error)
      }
    },
    formatData(data, index) {
      const keys = Object.keys(data)
      let hasYear = false
      keys.forEach((key) => {
        if (data[key] >= 99999) {
          hasYear = true
        }
        data[key + index] = data[key] >= 99999 ? '无限' : data[key]
      })
      if (index === 1 && hasYear) {
        data[`baseRemaining${index}`] = '-'
        data[`baseConsume${index}`] = '-'
      }
    },
    showDateChoose() {
      this.startDate = this.initialDate
      this.dateChooseVisible = true
    },
    async setDate() {
      if (!this.startDate) return this.$message.error('请选择成立时间')
      try {
        const res = await setEstablishTime({
          establishmentTime: this.startDate,
          shopId: this.$route.query.id
        })
        if (res.data.code === 0) {
          this.$message.success('操作成功')
          this.dateChooseVisible = false
          this.getDate()
        }
      } catch (e) {
        console.log(e)
      }
    },
    async getDate() {
      try {
        const res = await getEstablishTime({
          shopId: this.$route.query.id
        })
        if (res.data.code === 0) {
          this.startDate = res.data.data || ''
          this.initialDate = this.startDate
        }
      } catch (e) {
        console.log(e)
      }
    },
    // 设置销售
    setUpSales() {
      this.$refs.setUpSales.init({
        virtualMobile: this.ruleForm.virtualMobile,
        merchantWechat: this.ruleForm.merchantWechat,
        sellerName: this.ruleForm.sellerName
      })
    },
    setUpSalesData() {
      this.getData()
      this.resetData()
    },
    // 维修图片
    maintenanceImg() {
      this.$refs.maintenanceImg.init({
        imgs: this.data.images && this.data.images.split(','),
        type: 8,
        uid: this.data.userId
      })
    },
    // 救援图片
    rescueImg() {
      this.$refs.rescueImg.init({
        imgs: this.carImgs && this.carImgs.split(','),
        imgType: 6,
        licenseImg: this.drivingLicenseImgs,
        licenseType: 5,
        uid: this.data.userId
      })
    },
    // 店内图片
    inStoreImg() {
      this.$refs.inStoreImg.init({
        imgs: this.ruleForm.insideImage && this.ruleForm.insideImage.split(','),
        type: 2
      })
    },
    setInStoreData(data) {
      console.log('data6666666', data)
      this.ruleForm.insideImage = data || ''
    },
    goToClue(type) {
      this.$router.push({
        name: 'ClueOrder',
        query: {
          type,
          shopId: this.$route.query.id
        }
      })
    },
    // 创建订单
    creatOrder() {
      this.$router.push({
        name: 'OrderService',
        query: {
          shopId: this.ruleForm.shopId,
          shopName: this.ruleForm.shopName
        }
      })
    },
    handleDelete(index) {
      this.downRecordAuthorDocList.splice(index, 1)
    },
    // 租车配置
    setRentalCar() {
      this.$refs.setRentalCar.init()
    },
    // 查看操作日志记录
    seePunishLogs() {
      this.sourceId = this.$route.query.id
      this.$nextTick(() => {
        this.$refs.integrationLogs.init('third', 1)
      })
    },
    // 查看二手车处罚信息
    seePunishMessage() {
      this.sourceId = this.$route.query.id
      this.$nextTick(() => {
        this.$refs.secondHandCarPenaltyInformation.init()
      })
    },
    seeRelevanceMessage() {
      this.$refs.merchantAssociatedInfoRef.init({
        shopId: this.ruleForm.shopId,
        shopName: this.ruleForm.shopName
      })
    },
    // 上传隐私文件
    async httpRequestFile(option) {
      option.isDocument = true // 是文件
      option.businessType = 4
      this.$oss.ossUploadFile(option)
    },
    onSuccessFile(res) {
      if (!res) return
      this.downRecordAuthorDocList.push(res.url)
    },

    getChannelList() {
      getBrandChannelCfg().then((res) => {
        this.saleChannelList = res.data.data
      })
    },

    updateShopBrandChannelFnc(item) {
      updateShopBrandChannel({
        shopId: item.shopId,
        channel: item.channel,
        brandId: item.brandId,
        channelType: item.channelType
      }).then(() => {
        this.getQESaleCarCnt(item)
      })
    },

    // 钱江渠道选择
    selectChannel(item) {
      this.getQESaleCarCnt(item)
      // updateShopBrandChannel({
      //   shopId: item.shopId,
      //   channel: item.channel,
      //   brandId: item.brandId
      // }).then(() => {
      //   this.$message.success('售卖渠道选择成功')
      //   this.getQESaleCarCnt(item)
      // })
    },
    // QE网数据切换
    getQESaleCarCnt(info) {
      getQESaleCarCnt({
        channel: info.channel,
        brandId: info.brandId,
        channelType: info.channelType
      }).then((response) => {
        const data = response.data
        if (data.code === 0) {
          this.brandList.map((item) => {
            if (info.brandId === item.brandId) {
              item.goodsNum = data.data.saleCarCnt
            }
          })
        }
      })
    },
    // 选择角色
    changLableRole(info) {
      this.roleCode = info.code
      this.roleBusinessCode = info.businessCode
      this.operationCode = info.operationCode
    },
    async getUserPunishInfo() {
      const [err, res] = await to(
        getUserPunishInfo({
          sourceId: this.$route.query.id,
          isShopUser: 1,
          businessType: 6
        })
      )
      if (err) return this.$message.error('服务端错误')
      this.userPunishInfo = res.data.data
    },
    // 跳转纪录界面
    goToRecord(item) {
      this.$router.push({
        name: item,
        query: {
          recordShopId: this.$route.query.id,
          recordShopName: this.ruleForm.shopName
        }
      })
    },
    // 角色处理
    operateRole(item) {
      this.liveRoleOPen(item)
    },

    // 直播加色开关单独处理逻辑
    liveRoleOPen(item) {
      const me = this
      if (item.status) {
        livrInfoAttrOpen({
          shopId: me.$route.query.id,
          tagCode: item.code,
          open: item.status ? 1 : 0
        })
          .then(() => {
            me.$message.success('角色开启成功')
            this.getData(true)
          })
          .catch(() => {
            item.status = !item.status
          })
      } else {
        me.$confirm('是否关闭该商家角色，对用的业务也会关闭哦？')
          .then(() => {
            livrInfoAttrOpen({
              shopId: me.$route.query.id,
              tagCode: item.code,
              open: item.status ? 1 : 0
            })
              .then(() => {
                me.$message.success('角色关闭成功')
                this.getData(true)
              })
              .catch(() => {
                item.status = !item.status
              })
          })
          .catch(() => {
            item.status = !item.status
          })
      }
    },

    resetData() {
      const me = this
      resetData(me)
      me.$refs.selectUser && me.$refs.selectUser.clearData()
    },
    async httpRequest(option) {
      if (this.updataImgType === 'person') {
        option.idCard = true
      }
      this.$oss.ossUploadImage(option)
    },
    verificationIdCard(item) {
      const me = this
      if (!verificationCard(item)) {
        me.$message.error('身份证号码号码不合法')
        me.ruleForm.identificationNumber = ''
      }
    },
    quicklyQueryUsedCars() {
      const me = this
      me.$router.push({
        name: 'UsedCarManagement',
        query: {
          shopName: me.ruleForm.shopName
        }
      })
    },
    toUsedCarOrder() {
      this.$router.push({
        name: 'UsedCarOrderList',
        query: {
          shopId: this.$route.query.id
        }
      })
    },
    toUsedCarSupply() {
      const me = this
      me.$router.push({
        name: 'UsedCarList',
        query: {
          shopId: me.$route.query.id
        }
      })
    },
    openMergeOptions() {
      const data = {
        shopName: this.ruleForm.shopName,
        shopId: this.$route.query.id
      }
      this.$refs.MergeOptions.init(data)
    },
    getData(flag) {
      const me = this
      me.allowAddMemberPeople = false
      merchantDetail({
        shopId: me.$route.query.id
      }).then((response) => {
        if (response.data.code === 0) {
          if (!response.data.data.shopMemberInfo) {
            response.data.data.shopMemberInfo = {
              levelId: '',
              levelName: '',
              beginValidPeriod: '',
              endValidPeriod: ''
            }
            me.allowAddMemberPeople = true
          }

          const data = response.data.data
          console.log(data, 'response.data.data')
          me.queryDistance =
            data.queryDistance && data.queryDistance !== '0'
              ? data.queryDistance
              : ''
          me.data = response.data.data
          me.setData(flag)
        }
      })
    },
    setData(flag = false) {
      const me = this
      let identifyImage = []
      let insideImage = [] // 店内维修
      let maintainImage = []
      let outsideImage = [] // 封面门头
      let drivingLicenseImgs = [] // 救援车辆行驶证图片，逗号隔开
      let carImgs = [] // 救援车辆照片，逗号隔开
      let knightImgs = [] // 地域骑士授权证书照片，逗号隔开
      let images = [] // 维修救援图片
      me.brandList = []
      console.log('me.data', me.data)
      me.limitation = me.data.secondLimit ? true : false
      const styleTemplate = me.data.adminMotorSecondConfigDTO || {}
      me.secondTemplate = styleTemplate.secondTemplate ? true : false
      me.secondTestClue = me.data.secondTestClue ? true : false
      me.secondAssessClue = me.data.secondAssessClue ? true : false
      if (!matchChiEng(me.data.shopName) && !flag) {
        this.$message.info('商家名称仅支持中英文及大小写，不可包含特殊符号!')
      }
      // memberType 1: '新车会员',2: '二手车会员',3: '驾校会员',4: '租车会员',
      me.data.shopMemberList &&
        me.data.shopMemberList.map((item) => {
          switch (item.memberType) {
            case 1:
              me.secondHandlerShopLevel = item.memberLevel
              me.vipNewCarInfo = item
              break
            case 2:
              me.vipOldCarInfo = item
              break
            default:
              break
          }
        })
      me.shopCluePackageInfoList = me.data.shopCluePackageInfoList

      me.data.userId = me.data.userId || null
      if (me.data.userId && me.data.username) {
        me.$refs.selectUser.setUserOptions({
          username: me.data.username,
          userId: me.data.userId
        })
      }
      me.data.region = `${me.data.provinceName || ''}${me.data.cityName || ''}${
        me.data.districtName || ''
      }`
      me.data.saleMode = me.data.saleMode || '1'
      me.data.shopImage.map(function (value) {
        images =
          value.imgCategory === '8' ? images.concat(value.imgUrl) : images // 维修救援图片
        knightImgs =
          value.imgCategory === '7'
            ? knightImgs.concat(value.imgUrl)
            : knightImgs // 地域骑士授权证书照片，逗号隔开
        carImgs =
          value.imgCategory === '6' ? carImgs.concat(value.imgUrl) : carImgs // 救援车辆照片，逗号隔开
        drivingLicenseImgs =
          value.imgCategory === '5'
            ? drivingLicenseImgs.concat(value.imgUrl)
            : drivingLicenseImgs // 救援车辆行驶证图片，逗号隔开
        identifyImage =
          value.imgCategory === '4'
            ? identifyImage.concat(value.imgUrl)
            : identifyImage // 认证信息, 第一个营业执照，第二个法人身份证
        insideImage =
          value.imgCategory === '2'
            ? insideImage.concat(value.imgUrl)
            : insideImage // 店内环境图片，逗号分隔
        maintainImage =
          value.imgCategory === '3'
            ? maintainImage.concat(value.imgUrl)
            : maintainImage // 维修环境图片，逗号分隔
        outsideImage =
          value.imgCategory === '1'
            ? outsideImage.concat(value.imgUrl)
            : outsideImage // 封面门头
      })
      me.data.identifyImage = identifyImage.length ? identifyImage.join() : ''
      me.data.insideImage = insideImage.length ? insideImage.join() : ''
      me.data.maintainImage = maintainImage.length ? maintainImage.join() : ''
      me.outsideImage = outsideImage.length ? outsideImage.join() : ''
      me.data.images = images.length ? images.join() : ''
      me.knightImgs = knightImgs.length ? knightImgs.join() : ''
      me.carImgs = carImgs.length ? carImgs.join() : ''
      me.drivingLicenseImgs = drivingLicenseImgs.length
        ? drivingLicenseImgs.join()
        : ''
      me.businessImg =
        (me.data.identifyImage && me.data.identifyImage.split(',')[0]) || ''
      me.personImage =
        (me.data.identifyImage && me.data.identifyImage.split(',')[1]) || ''
      me.personImageShow =
        (me.data.identifyImage && me.data.identifyImage.split(',')[1]) || ''
      if (me.data.contacts) {
        me.contactsList = me.data.contacts
        me.contactsTotal = me.contactsList.length
      } // 联系电话
      if (me.data.shopAnnualSales) {
        me.salesList = me.data.shopAnnualSales
        me.salesTotal = me.salesList.length
        me.setShowSalesList('1')
      } // 销量情况
      console.log(me.data.tagList, 'me.data.tagList')

      // 角色处理
      const lableCodeArr = []
      me.data.tagList &&
        me.data.tagList.map((res) => {
          // if (res.code !== 1) {
          //   lableCodeArr.push(res.code)
          // }
          lableCodeArr.push(res.code)
        })
      // 8驾考报名特殊处理：角色和业务同步
      let operateArr = [8]
      // 二手车商家微信线索开关特殊处理
      if ([3, 36, 85].includes(me.uid)) {
        me.labelTitle.map((item) => {
          if (item.code === 14) {
            item.operationCode.push(19)
            item.businessCode.push(19)
          }
        })
      }
      me.labelTitle.map((item) => {
        item.status = false
        if (lableCodeArr.includes(item.code)) {
          item.status = true
          operateArr = [...operateArr, ...item.businessCode]
        }
      })

      // 所有开启角色对应的业务可操作性综合
      this.isRoleOperateArr = [...new Set(operateArr)]

      // 驾考设置
      me.data.serviceRights &&
        me.data.serviceRights.map((item) => {
          // 有可操作性项
          if (item.businessType === 8) {
            this.drivingPenaltyDueTime = item.penaltyDueTime
          }
        })

      // if (me.data.tagList) {
      //   me.labelTitle = me.data.tagList.map(res => {
      //     if (res.code) {
      //       res.status = true
      //     }
      //   })
      // } // 角色标签
      // me.serviceTypes =
      //   me.data.serviceRights &&
      //   me.data.serviceRights.reduce((acc, cur) => {
      //     const typeNames = {
      //       2: '售新车',
      //       3: '二手车',
      //       4: '驾考',
      //       5: '租车',
      //       1: '其他'
      //     }
      //     if (!acc[typeNames[cur.category]]) {
      //       acc[typeNames[cur.category]] = [cur]
      //     } else {
      //       acc[typeNames[cur.category]].push(cur)
      //     }
      //     return acc
      //   }, {}) // 服务类型
      //   console.log(me.serviceTypes,'========serviceTypes')
      // const service = this.serviceTypes['驾考'] && this.serviceTypes['驾考'][0]
      // this.drivingPenaltyDueTime = service && service.penaltyDueTime
      if (me.data.shopBrands.length) {
        me.data.shopBrands.map(function (value) {
          me.setBrandItem(value)
        })
        me.brandList = me.data.shopBrands
      } // 获取brand list
      if (me.data.activityList) {
        me.activeList = me.data.activityList
        me.activeTotal = me.activeList.length
        me.setShowActiveList('1')
      } // 活动列表
      me.data.is4s = me.data.is4s === '1'
      me.data.topShop = me.data.topShop === '1'
      this.downRecordAuthorDocList = me.data.downRecordAuthorDoc
        ? me.data.downRecordAuthorDoc.split(',')
        : []
      me.ruleForm = {
        ...me.ruleForm,
        ...me.data
      }
      me.oldRuleForm = deepCopy(me.ruleForm)
      me.shopStatus = me.ruleForm.shopStatus
      if (me.ruleForm.deleteReason) {
        me.deleteReasonContent =
          me.rejectNoticeContent[me.ruleForm.deleteReason - 1]
      }
      // 非刘学磊（uid=3）、王长文（uid=45）账号 uid=36（伏星宇），营业执照名称、统一社会信用代码不为空时，不可修改
      if (me.ruleForm.registrationName) {
        me.forbidRegistrationName = true
      }
      if (me.ruleForm.registrationNumber) {
        me.forbidRegistrationNumber = true
      }
      me.$refs['ruleForm'].resetFields() // 重置验证
    },
    setShowSalesList(page) {
      this.showSalesList = this.salesList.slice(5 * (page - 1), 5 * page)
    },
    // 设置返回uid,name
    setUid(id, name) {
      this.ruleForm.userId = id
      this.ruleForm.username = name
    },
    // 设置显示的活动列表（页码）
    setShowActiveList(page) {
      this.showActiveList = this.activeList.slice(5 * (page - 1), 5 * page)
    },
    // 显示活动
    chooseActive(index) {
      const me = this
      this.operateIndex = index
      me.$router.push({
        name: 'ActivityContentConfiguration'
      })

      // if (!me.ruleForm.userId) {
      //   me.$message.error('数据还未获取成功')
      //   return
      // }
      // me.activeStatus = true
      // setTimeout(() => {
      //   me.$refs.createActivity.init({
      //     username: me.ruleForm.username,
      //     authorId: me.ruleForm.userId
      //   })
      // }, 100)
    },
    // 选择品牌
    chooseBrand(index) {
      this.operateIndex = index
      this.$refs.BindBrandDialog.init({
        title: '新加品牌',
        isAddBrand: true,
        shopId: this.ruleForm.shopId
      })
    },
    setBrandItem(value) {
      value.authStatus = value.authStatus === 1
      value.factoryCheck = value.factoryCheck === 1
      value.showStatus = value.showStatus === 1
      value.valid = value.valid === 1
      if (value.expireTime) {
        const time = new Date(value.expireTime).getTime()
        const time_ = time + 60 * 60 * 24 * 10
        const time_now = new Date().getTime()
        value.showWarn = time_ < time_now
      } else {
        value.showWarn = false
      }
    },
    // 获取返回的brand list信息
    getBrandData(data = [], brandName) {
      postLog(
        105,
        this.$route.query.id,
        '新增',
        `新增品牌：${brandName}`,
        '{}',
        '{}'
      )
      data.map((item) => {
        this.setBrandItem(item)
        const channelItem = this.getChannelItem(item.brandId)
        if (item.brandName === brandName && channelItem) {
          item.channel = 1
          item.channelType = channelItem.channelType
          this.updateShopBrandChannelFnc(item)
        }
      })
      this.brandList = data
    },

    getChannelItem(brandId) {
      const channelItem = this.saleChannelList.find((v) => {
        return v.brandIds.includes(brandId)
      })
      return channelItem
    },

    getChannelOptions(brandId) {
      const item = this.getChannelItem(brandId)
      return item || {}
    },

    createBrandOrder(flag) {
      if (!flag) {
        const data = {}
        if (this.isPackYears) {
          const item = this.shopCluePackageInfoList[0] || {}
          const beginTime = dayjs(item.endTime)
            .add(1, 'day')
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss')
          const endTime = dayjs(beginTime)
            .add(1, 'year')
            .endOf('day')
            .format('YYYY-MM-DD HH:mm:ss')
          data.createDateRange = [beginTime, endTime]
        }
        this.brandOrderReturn(data)
      } else {
        // this.$refs.addBrandOrderRef.init({
        //   shopId: this.ruleForm.shopId,
        //   isAddBrand: flag
        // })
        const item = this.shopCluePackageInfoList[0] || {}
        const beginTime_ = dayjs('00:00:00', 'hh:mm:ss').format(
          'YYYY-MM-DD HH:mm:ss'
        )
        const endTime_ = dayjs(item.endTime).format('YYYY-MM-DD HH:mm:ss')
        const idList = []
        this.brandList.forEach((v) => {
          if (!v.payStatus && v.brandId) {
            // 这里之前是 要判断有效和商家版展示的，现在不要啦
            idList.push({
              brandId: v.brandId,
              channel: v.channel,
              channelType: v.channelType
            })
          }
        })
        const data = {
          dateRange: [beginTime_, endTime_],
          brandIdList: idList,
          lastDays: this.ruleForm.lastDays
        }
        this.brandOrderReturn(data)
      }
    },
    openReplaceBrand() {
      this.$refs.replaceBrandRef.init(
        this.$route.query.id,
        this.brandList,
        this.saleChannelList
      )
    },
    brandOrderReturn(data) {
      data.isNewCarVip = this.vipNewCarInfo.memberLevel > 0
      data.isPackYears = this.isPackYears
      localStorage.setItem('clearBrandOrderData', JSON.stringify(data))
      this.creatOrder()
    },
    openSaleBrandLog() {
      this.$refs.saleBrandLogRef.init(this.$route.query.id)
    },
    // 更新图片
    updataImg(type) {
      this.updataImgType = type
      document
        .getElementById('el-upload__input_common')
        .children[0].children[0].click()
      // document.querySelector('.el-upload__input').click()
    },
    // 上传图片
    updateImg(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        const imgOrgUrl = res.imgOrgUrl
        if (this.updataImgType === 'business') {
          this.businessImg = imgOrgUrl
        } else if (this.updataImgType === 'person') {
          this.personImage = res.imgUrl
          this.personImageShow = imgOrgUrl
        } else if (this.updataImgType === 'driving') {
          this.drivingLicenseImgs = imgOrgUrl
        } else if (this.updataImgType === 'photo') {
          this.carImgs = imgOrgUrl
        } else if (this.updataImgType === 'knight') {
          this.knightImgs = imgOrgUrl
        } else {
          this.outsideImage = imgOrgUrl
        }
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
      // if (this.updataImgType === 'business' || this.updataImgType === 'person') {
      //   this.updataImgType === 'business' ? this.businessImg = res.data : this.personImage = res.data
      // } else {
      //   this.outsideImage = res.data
      // }
    },

    // 保存返回回来的图片
    sendData(data) {
      const me = this
      me.ruleForm.insideImage = setImg(data.tabs[0])
      me.ruleForm.maintainImage = setImg(data.tabs[1])
      me.ruleForm.images = setImg(data.tabs[2])
      function setImg(data) {
        if (!data.length) {
          return ''
        }
        const tempArr = []
        data.map(function (value) {
          tempArr.push(value.imgOrgUrl)
        })
        return tempArr.toString()
      }
    },
    // 展示地图
    showPostion() {
      const me = this
      const position = [
        me.ruleForm.longitude || me.data.longitude,
        me.ruleForm.latitude || me.data.latitude
      ]
      me.$refs.cPosition.initPostion(position, me.ruleForm.locationAddress)
    },
    // 位置信息
    mapPosition() {
      const me = this
      const address = JSON.parse(localStorage.position || '{}')
      me.ruleForm.locationAddress = address.address
      me.ruleForm.latitude = address.lat
      me.ruleForm.longitude = address.lng
      me.readloadData()
    },
    // 重新渲染dom
    readloadData() {
      const templateObject = this.ruleForm
      this.ruleForm = {}
      this.ruleForm = templateObject
      this.shopStatus = this.ruleForm.shopStatus
    },
    // 返回电话数据
    sendPhoneData(data, type) {
      data.canReceiveWechatClue = false
      // type: true 编辑，false:新增
      if (type) {
        this.updateShopContactFn(data)
      } else {
        this.addShopContactsFn(data)
      }
    },
    addShopContactsFn(data, repeatConfirm = 0) {
      const me = this
      addShopContacts({
        userId: data.userId || '',
        mobile: data.phone,
        name: data.name,
        shopId: me.$route.query.id,
        roleType: data.roleType,
        canReceiveClue: data.canReceiveClue,
        canReceiveWechatClue: false,
        repeatConfirm
      })
        .then((response) => {
          const info = response.data
          if (info.code === 0) {
            me.$message.success('人员添加成功')
            this.getData()
          } else {
            if (info.data.groupId) {
              const msg =
                '该人员已' +
                info.data.groupName +
                info.data.banTime +
                '是否继续操作'
              me.$confirm(msg, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
                .then(() => {
                  this.addShopContactsFn(data, 1)
                })
                .catch(() => {})
            } else {
              me.$message.error(
                response.data.message || response.data.msg || '人员添加失败'
              )
            }
          }
        })
        .catch((err) => {
          me.$message.error(err.message || err.msg || '人员添加失败')
        })
    },
    updateShopContactFn(data, repeatConfirm = 0) {
      const me = this
      updateShopContact({
        id: data.id,
        userId: data.userId || '',
        mobile: data.phone,
        name: data.name,
        shopId: me.$route.query.id,
        roleType: data.roleType,
        canReceiveClue: data.canReceiveClue,
        canReceiveWechatClue: data.canReceiveWechatClue,
        repeatConfirm
      })
        .then((response) => {
          const info = response.data
          if (info.code === 0) {
            me.$message.success('人员编辑成功')
            this.getData()
          } else {
            if (info.data.groupId) {
              const msg =
                '该人员已' +
                info.data.groupName +
                info.data.banTime +
                '是否继续操作'
              me.$confirm(msg, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
                .then(() => {
                  this.updateShopContactFn(data, 1)
                })
                .catch(() => {})
            } else {
              me.$message.error(
                response.data.message || response.data.msg || '人员编辑失败'
              )
            }
          }
        })
        .catch((err) => {
          me.$message.error(err.message || err.msg || '人员编辑失败')
        })
    },
    // 返回销量数据
    sendSalesData(data, type) {
      const me = this
      if (!type) {
        me.salesList.push(data)
      } else {
        me.salesList.map(function (value, index) {
          if (value.year === me.salesTempArr.year) {
            me.salesList.splice(index, 1, data)
            me.setShowSalesList(me.salesPage)
          }
        })
      }
      me.setShowSalesList(me.salesPage)
      console.log(data, type)
    },
    // 大图查看图片
    seeBigImg(link, flag) {
      this.$refs.showImage.init(flag ? replaceImgUrl(link) : link)
    },
    punish(type, value, service) {
      this.fromPunish = true
      this.service = service
      this.params = {
        businessType: type,
        openStatus: value
      }
      let instance
      // 售新车、租车、驾考
      if ([8, 9, 13].includes(type)) {
        instance = this.$refs['closeBusiness2']
      } else {
        instance = this.$refs['closeBusiness']
      }
      instance.visible = true
      instance.init(type)
    },
    operate(type, value, service, flag = true) {
      console.log(type, 'type', value, 'value', service, 'service')
      this.service = service
      this.params = {
        businessType: type,
        openStatus: value
      }
      if (service.businessName === '驾考报名' && value && flag) {
        service.openStatus = !service.openStatus
        return this.seeDrivingTest()
      }
      let instance = ''
      if (!value) {
        if ([2, 14, 18, 17].includes(this.roleCode)) {
          this.updateServiceStatus()
        } else {
          instance = this.$refs['closeBusiness']
          instance.visible = true
          instance.init()
        }
        // extraParams.penaltyDay = instance.penaltyDay
        // extraParams.penaltyReason = instance.penaltyReason
      } else {
        // if (!service.penaltyDueTime) return this.updateServiceStatus()
        this.updateServiceStatus()
        // const instance = this.$refs['openBusiness']
        // instance.visible = true
      }
    },
    cancel() {
      if (!this.fromPunish) {
        this.service.openStatus = !this.params.openStatus
      }
      this.fromPunish = false
    },
    updateServiceStatus(fromShopPunish) {
      if (!this.params.openStatus) {
        let instance = ''
        if ([8, 9, 13].includes(this.params.businessType)) {
          instance = this.$refs['closeBusiness2']
          if (fromShopPunish) {
            this.params.fromShopPunish = 1
          }
        } else {
          instance = this.$refs['closeBusiness']
        }
        this.params.penaltyDay = Number(instance.penaltyDay)
        this.params.penaltyReason = instance.penaltyReason
        this.params.isPunish = instance.radio
        this.params.operationType = instance.radio ? 0 : ''
      } else {
        this.params.penaltyDay = 0
        delete this.params.penaltyReason
      }
      let fn =
        this.params.isPunish === 2
          ? cancelPunish
          : this.params.fromShopPunish
          ? merchantServiceOpen2
          : merchantServiceOpen
      fn({
        shopId: this.$route.query.id,
        sourceName: this.ruleForm.shopName,
        mobile: this.ruleForm.mobile,
        tagCode: this.roleCode,
        ...this.params
      })
        .then(() => {
          this.$message.success('修改类型成功')
          this.getData()
        })
        .catch(() => {
          if (!this.params.fromShopPunish) {
            this.service.openStatus = !this.params.openStatus
          }
        })
    },
    // 保存标签
    addTagList() {
      const me = this
      saveTagList({
        shopId: me.$route.query.id,
        tagCodeList: me.tagList.join(',')
      })
        .then(() => {
          this.$message.success('保存成功')
        })
        .catch(() => {})
    },
    // 获取位置
    getRef() {
      const me = this
      me.$refs.cArea.setArea(
        me.ruleForm.provinceName,
        me.ruleForm.cityName,
        me.ruleForm.districtName
      )
      me.$refs.cArea.init('cache')
    },
    // 增加手机号码
    addMobile() {
      this.$refs.ChoosePhone.setData(
        { name: '', phone: '' },
        this.contactsList,
        true
      )
    },
    // 增加销量情况
    addSales() {
      this.$refs.ChooseSales.setData(
        { year: '', saleNumber: '' },
        this.salesList
      )
    },
    // 修改手机号码
    updataMobile(data) {
      this.contactsTempArr = data
      this.$refs.ChoosePhone.setData(data, this.contactsList, true)
    },
    // 修改销量情况
    updataSales(data) {
      this.salesTempArr = data
      this.$refs.ChooseSales.setData(data, this.salesList)
    },
    // 删除手机号码
    delMobile(data) {
      const me = this
      if (data.canReceiveWechatClue) {
        return this.$message.error('微信线索接收开启中，无法删除该人员')
      }
      delShopContact({
        id: data.id
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('人员删除成功')
            this.getData()
          } else {
            me.$message.error(
              response.data.message || response.data.msg || '人员删除失败'
            )
          }
        })
        .catch((err) => {
          // me.$message.error(err.message || err.msg || '人员删除失败')
        })
    },
    // 删除销量情况
    delSalesMobile(data) {
      const me = this
      me.salesList.map(function (value, index) {
        if (value.year === data.year) {
          me.salesList.splice(index, 1)
          me.setShowSalesList(me.salesPage)
        }
      })
    },
    // 增加的活动数据
    addActive(data) {
      this.activeStatus = false
      if (data) {
        this.activeList.push({
          name: data.activityName
        })
        this.setShowActiveList(this.activePage)
      }
    },
    // 查看附近经销商
    seeNearbyShop() {
      const me = this
      if (!(me.ruleForm.latitude && me.ruleForm.longitude)) {
        return this.$message.error('校对定位信息缺失，无法查看附近经销商')
      }
      me.$refs.nearbyShop.setStatus()
    },
    // 查看同营业执照的经销商
    seeBusinessShop() {
      const me = this
      me.$refs.businessShop.setStatus()
    },
    // 查看同身份证的经销商
    seeIdCardShop() {
      const me = this
      me.$refs.idCardShop.setStatus()
    },
    // 显示付费会员配置dialog
    seeMemberConfig(memberType) {
      this.$refs.memberConfig.init(memberType)
    },
    // 显示线索包配置dialog
    seeCluePackageConfig() {
      this.$refs.cluePackageConfig.init()
    },
    // 返回的地址
    backArea(data) {
      const me = this
      console.log(data)
      me.ruleForm.provinceName = data.provinceName
      me.ruleForm.cityName = data.cityName
      me.ruleForm.districtName = data.distName
      me.ruleForm.region = data.provinceName + data.cityName + data.distName
      const templateObject = me.ruleForm
      me.ruleForm = []
      me.ruleForm = templateObject
    },
    // 更新授权状态
    changeStatus(data) {
      if (!data.relId) {
        data.authStatus = false
        return
      }
      const me = this
      updateBrandAuthStatus({
        relId: data.relId,
        isAuth: data.authStatus ? '1' : '0'
      })
        .then((response) => {
          if (response.data.code !== 0) {
            return me.$message.error(response.data.msg || '数据更新失败')
          }
          me.getData()
        })
        .catch((err) => {
          data.authStatus = !data.authStatus
          me.$message.error(err.message || '数据更新失败')
        })
    },
    // 查询品牌
    querySearchBrandList(queryString, cb) {
      const me = this
      this.$tools.debounce(searchAsyncBrand, 300)()
      function searchAsyncBrand() {
        let existBrands = []
        me.brandList.map(function (value) {
          existBrands =
            value.brandId !== ''
              ? existBrands.concat(value.brandId)
              : existBrands
        })
        searchBrand({
          name: me.updateBrand.brandName,
          existBrands: existBrands.join(),
          page: 1,
          limit: 20
        }).then((response) => {
          if (response.data.code === 0) {
            const brandList = []
            const result = response.data.data && response.data.data.listData
            result.map(function (value) {
              const newObj = {
                value: value.brandName,
                brandId: value.brandId
              }
              brandList.push(newObj)
            })
            console.log(brandList)
            cb(brandList)
          }
        })
      }
    },
    // 添加品牌
    addShopBrandList(item) {
      const me = this
      addShopBrand({
        shopId: me.ruleForm.shopId,
        brandId: item.brandId
      })
        .then((response) => {
          if (response.data.code !== 0) {
            return me.$message.error(
              response.data.msg || '添加商铺销售品牌失败'
            )
          }
          me.updateBrand.relId = response.data.data
          me.updateBrand.brandName = item.value
          me.updateBrand.brandId = item.brandId
        })
        .catch((err) => {
          me.$message.error(err.message || '添加商铺销售品牌失败')
        })
    },
    // 添加品牌下所有可售车辆
    addAllBrandGoods(brandObj) {
      console.log(brandObj)
      const me = this
      if (!brandObj.brandId) {
        return me.$message.error('请先输入品牌')
      }
      brandObj.canGetTotal = false
      addAllBrandGoods({
        shopId: me.ruleForm.shopId || '',
        brandId: brandObj.brandId
      })
        .then((response) => {
          brandObj.canGetTotal = true
          if (response.data.code === 0) {
            const data = response.data.data
            const saleGoodsNum = brandObj.goodsNum || 0 // 如果已添加，需要先删除
            me.ruleForm.saleGoodsNum =
              me.ruleForm.saleGoodsNum - saleGoodsNum + data.goodsNum
            brandObj.goodsNum = data.goodsNum
            brandObj.goodsIds = data.goodsIds
            brandObj.goodsNames = data.goodsNames
            return
          }
        })
        .catch(() => {
          brandObj.canGetTotal = true
        })
    },
    // clearAllBrandGoods(brandObj) {
    //   const me = this
    //   if (!brandObj.brandId) {
    //     return me.$message.error('请先输入品牌')
    //   }
    //   me.$set(brandObj, 'canGetTotal', false)
    //   editShopGoods({
    //     brandId: brandObj.brandId,
    //     goodsIds: '',
    //     shopId: me.$route.query.id
    //   }).then(response => {
    //     me.$set(brandObj, 'canGetTotal', true)
    //     if (response.data.code === 0) {
    //       me.$set(me.ruleForm, 'saleGoodsNum', me.ruleForm.saleGoodsNum - brandObj.goodsNum)
    //       me.$set(brandObj, 'goodsNum', 0)
    //       me.$set(brandObj, 'goodsIds', '')
    //       me.$set(brandObj, 'goodsNames', '')
    //       return
    //     }
    //   }).catch(_ => {
    //     me.$set(brandObj, 'canGetTotal', true)
    //   })
    // },
    // 删除在售品牌
    deleteShopCar(data, index) {
      const me = this

      me.$confirm('确认删除？')
        .then(() => {
          me.loadingCircle = true

          if (!data.relId) {
            me.loadingCircle = false
            me.brandList.splice(index, 1)
            return
          }

          deleteShopBrand({
            relId: data.relId
          })
            .then((response) => {
              if (response.data.code !== 0) {
                return me.$message.error(response.data.msg || '删除在售失败')
              }
              me.ruleForm.saleGoodsNum =
                me.ruleForm.saleGoodsNum - data.goodsNum
              me.brandList.splice(index, 1)
              postLog(
                105,
                this.$route.query.id,
                '删除',
                `删除品牌：${data.brandName}`,
                '{}',
                '{}'
              )
            })
            .finally(() => {
              me.loadingCircle = false
            })
        })
        .catch(() => {})
    },
    // 双击查看该品牌下可选车
    rowDoubleClick(item) {
      if (!item.brandId) {
        return
      }
      let label = []
      const goodsIds = item.goodsIds.split(',')
      const goodsNames = item.goodsNames.split(',')
      goodsIds.map(function (value, index) {
        label.push({
          goodName: goodsNames[index],
          goodId: value
        })
      })
      label = goodsIds.length === 1 && goodsIds[0] === '' ? [] : label
      this.$refs.ChooseDialogCar.init({
        labels: label,
        brandId: item.brandId,
        initTime: Math.round(new Date().getTime()),
        needEditShop: true
      })
    },
    sendCarData(data) {
      const me = this
      me.brandList.map(function (value) {
        if (value.brandId === data.brandId) {
          me.ruleForm.saleGoodsNum =
            me.ruleForm.saleGoodsNum - value.goodsNum + data.number
          value.goodsNum = data.number
          value.goodsNames = data.goodsName
          value.goodsIds = data.goodsIds
        }
      })
    },
    // 确认删除
    confirmRejection(rejectData) {
      console.log(rejectData)
      this.deleteReasonContent = rejectData.content
      this.ruleForm.deleteReason =
        this.rejectNoticeContent.findIndex((_) => _ === rejectData.content) + 1
    },
    // 验证商家名称
    verificationShopName() {
      const me = this
      if (!me.ruleForm.shopName) {
        return
      }
      queryShopInfoByName({
        shopName: me.ruleForm.shopName,
        shopId: me.ruleForm.shopId || ''
      })
        .then((response) => {
          if (!response.data.code === '0') {
            return me.$message.error(response.data.msg)
          }
          const result = response.data.data
          result.typeId === 1
            ? me.$message.success('店铺名称未占用')
            : me.$message.error(
                '名称不可重复，建议加入城市信息.如苏州春风旗舰店，名称中不可带有代理、总代、有限公司、授权、直营、销售服务等关键词'
              )
          me.shopNameStatus = result.typeId !== 1
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 准备数据
    readyData() {
      const me = this
      me.ruleForm.brands = {}
      console.log(me.ruleForm, 'me.ruleForm')
      if (!me.ruleForm.shopName) {
        return me.$message.error('请输入店铺名称')
      }
      // if (me.ruleForm.topShop === true || me.ruleForm.topShop === '1') {
      //   if (!me.ruleForm.topBeginTime || !me.ruleForm.topEndTime) {
      //     return me.$message.error('请选择置顶时间')
      //   }
      // }
      if (me.shopStatus === '3' && !me.deleteReasonContent) {
        return me.$message.error('删除原因必填')
      }
      if (me.brandList.length) {
        me.brandList.map(function (value) {
          me.ruleForm.brands[value.brandId] = value.brandName
        })
      }
      if (!me.ruleForm.region) {
        me.ruleForm.provinceName = ''
        me.ruleForm.cityName = ''
        me.ruleForm.districtName = ''
      }
      me.ruleForm.shopImage = []
      me.ruleForm.shopImage = !me.outsideImage
        ? me.ruleForm.shopImage
        : me.ruleForm.shopImage.concat({
            imgUrl: this.outsideImage,
            imgCategory: '1'
          })
      me.ruleForm.shopImage = !me.drivingLicenseImgs
        ? me.ruleForm.shopImage
        : me.ruleForm.shopImage.concat({
            imgUrl: this.drivingLicenseImgs,
            imgCategory: '5'
          })
      me.ruleForm.shopImage = !me.carImgs
        ? me.ruleForm.shopImage
        : me.ruleForm.shopImage.concat(
            this.carImgs.split(',').map((item) => ({
              imgUrl: item,
              imgCategory: '6'
            }))
          )
      me.ruleForm.shopImage = !me.knightImgs
        ? me.ruleForm.shopImage
        : me.ruleForm.shopImage.concat({
            imgUrl: this.knightImgs,
            imgCategory: '7'
          })
      if (me.queryDistance < 0) {
        me.$message.error('询价距离不能为负数!')
        return
      }
      me.ruleForm.downRecordAuthorDoc = me.downRecordAuthorDocList.join(',')
      me.ruleForm.queryDistance = me.queryDistance
      if (me.ruleForm.insideImage) {
        // 店内图片 增加
        setImg(me.ruleForm.insideImage, '2')
      }
      if (me.ruleForm.maintainImage) {
        // 维修环境 增加
        setImg(me.ruleForm.maintainImage, '3')
      }
      if (me.ruleForm.images) {
        // 维修救援图片
        setImg(me.ruleForm.images, '8')
      }
      function setImg(data, num) {
        data = data.split(',')
        data.map(function (value) {
          me.ruleForm.shopImage.push({
            imgUrl: value,
            imgCategory: num
          })
        })
      }
      me.ruleForm.shopImage = !me.businessImg
        ? me.ruleForm.shopImage
        : me.ruleForm.shopImage.concat({
            imgUrl: me.businessImg,
            imgCategory: '4'
          })
      me.ruleForm.shopImage = !me.personImage
        ? me.ruleForm.shopImage
        : me.ruleForm.shopImage.concat({
            imgUrl: me.personImage,
            imgCategory: '4'
          })
      // me.ruleForm.contacts = me.contactsList
      console.log(me.ruleForm.contacts)
      me.ruleForm.shopAnnualSales = me.salesList
      me.ruleForm.topBeginTime = me.ruleForm.topShop
        ? Math.round(me.ruleForm.topBeginTime)
        : ''
      me.ruleForm.topEndTime = me.ruleForm.topShop
        ? Math.round(me.ruleForm.topEndTime)
        : ''
      me.ruleForm.shopStatus = me.shopStatus
      // me.ruleForm.topShop = me.ruleForm.topShop ? '1' : '0'
      delete me.ruleForm.createTime
      delete me.ruleForm.updateTime
      delete me.ruleForm.brandMap
      delete me.ruleForm.activityList
      console.log(me.oldRuleForm.shopStatus, 'me.oldRuleForm')
      console.log(me.ruleForm.shopStatus, 'me.ruleForm')
      if (
        me.ruleForm.shopStatus !== me.oldRuleForm.shopStatus &&
        me.ruleForm.shopStatus === '3'
      ) {
        deleteShopNotice({ shopId: me.ruleForm.shopId }).then((res) => {
          const data = res.data
          if (data.code === 0 && data.msg) {
            me.$confirm(data.msg, '提示', {
              confirmButtonText: '继续删除',
              cancelButtonText: '知道了',
              type: 'warning'
            }).then(() => {
              me.updataData2()
            })
          } else {
            me.updataData()
          }
        })
      } else {
        if (me.ruleForm.mobile !== me.oldRuleForm.mobile) {
          me.$confirm('确认修改商家数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.$refs.RegisterDialog.init({
                phone: me.ruleForm.mobile
              })
            })
            .catch((err) => {
              me.ruleForm.topShop === '1'
              me.$message.error(`${err.data.msg}` || '修改失败')
              console.log(err)
            })
        } else {
          me.$route.query.id ? me.updataData() : me.postNewShop()
        }
      }
    },
    // 注册成功
    sucess() {
      this.$route.query.id ? this.updataData2() : this.postNewShop()
    },
    // 发送数据
    updataData() {
      const me = this
      me.$confirm('确认修改商家数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          me.ruleForm.is4s = me.ruleForm.is4s ? '1' : '0'
          me.ruleForm.topShop = me.ruleForm.topShop === true ? '1' : '0'
          merchantUpdate({ jsonStr: JSON.stringify(me.ruleForm) }).then(
            (response) => {
              if (response.data.code === 0) {
                me.$message.success('修改成功')
                setTimeout(() => {
                  me.goBack()
                }, 2000)
                return
              }
              me.$message.error(`${response.data.msg}` || '修改失败')
            }
          )
        })
        .catch((err) => {
          me.ruleForm.topShop === '1'
          me.$message.error(`${err.data.msg}` || '修改失败')
          console.log(err)
        })
    },
    // 发送数据-注册逻辑特殊处理
    updataData2() {
      const me = this
      me.ruleForm.is4s = me.ruleForm.is4s ? '1' : '0'
      me.ruleForm.topShop = me.ruleForm.topShop === true ? '1' : '0'
      merchantUpdate({ jsonStr: JSON.stringify(me.ruleForm) }).then(
        (response) => {
          if (response.data.code === 0) {
            me.$message.success('修改成功')
            setTimeout(() => {
              me.goBack()
            }, 2000)
            return
          }
          me.$message.error(`${response.data.msg}` || '修改失败')
        }
      )
    },
    // 新增商家
    postNewShop() {
      const me = this
      if (me.shopNameStatus) {
        me.$message.error('店名重复，请先修改')
        return
      }
      me.ruleForm.topShop = me.ruleForm.topShop === true ? '1' : '0'
      me.ruleForm.types = me.ruleForm.types ? me.ruleForm.types : '2'
      saveNewShop({ jsonStr: JSON.stringify(me.ruleForm) })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('新增成功')
            setTimeout(() => {
              me.goBack()
            }, 2000)
            return
          }
          me.$message.error(`${response.data.msg}` || '新增失败')
        })
        .catch((err) => {
          console.log(err)
          me.ruleForm.topShop === '1'
          me.$message.error(`${err.data.msg}` || '新增失败')
        })
    },
    goBack() {
      if (this.$route.query && this.$route.query.openNew) {
        return this.$message('请手动关闭此页面')
      }
      this.$router.go(-1)
    },
    // 查看手机号码
    seeMobile(mobile, type) {
      const data = {
        decryptContent: mobile,
        source: 'distributorDetails',
        type, // 1手机号解密，2微信号解密，3身份证解密
        logModule: 22, // 22经销商详情
        shopStatus: '' // 1为用户，商家可以传空
      }
      this.$refs.seePhone && this.$refs.seePhone.init(data)
    },
    // 查看会员付费记录
    seePayRecords() {
      // this.$refs.vipPayLogs.setStatus(memberType)
      this.$router.push({
        name: 'DealerOrderList',
        query: {
          shopId: this.$route.query.id
        }
      })
    },
    // 查看操作日志记录
    seeOperateLogRecords(type, catalog) {
      this.$refs.integrationLogs.init(type, catalog)
    },
    // 展示线索赠送设置
    showSendClue(type) {
      this.$refs.sendClue.setStatus(type)
    },
    // 展示金币赠送记录
    showGoldClue() {
      this.$refs.goldClue.setStatus()
    },
    // 删除店铺操作日志
    deleteShopLog() {
      this.$refs.integrationLogs.init('first', 0)
    },
    // showTestDrive() {
    //   this.$refs.testDrive.setStatus()
    // },
    // showSendWechatClue() {
    //   this.$refs.wechatClue.setStatus()
    // },
    // 切换是否接收线索
    changeSwitch() {},
    // 切换查看微信线索
    changeChatSwitch(data) {
      setCanReceiveWechatClue({
        id: data.id,
        status: data.canReceiveWechatClue ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.success('修改异常')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    // 切换查看电话线索
    changeMObileSwitch(data) {
      setCanReceiveMobileClue({
        id: data.id,
        status: data.canViewMobileClue ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.success('修改异常')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    // 切换查看租车线索
    changeRentalClueSwitch(data) {
      // 对应的租车线索控制接口暂不开发
      setCanReceiveRentalClue({
        id: data.id,
        status: data.canViewRentalClue ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.success('修改异常')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    // 切换查看分配收车线索
    changeCollectionClueSwitch(data) {
      setCanReceiveColletionCarClue({
        id: data.id,
        status: data.canReceiveCollectionCarClue ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.success('修改异常')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    // 切换驾考线索
    changeDrivingTestSwitch(data) {
      setDrivingTestClue({
        id: data.id,
        status: data.canReceiveDrivingSchoolClue ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.success('修改异常')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    // 显驾考报名设置dialog
    seeDrivingTest() {
      this.$refs.drivingTest.setStatus()
    },
    // 查看虚拟号
    viewingVirtualNumbers() {
      this.showVirtualNumbers = true
      virtualMobile({
        shopId: this.$route.query.id
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.virtualNumbers = res.data.data || ''
            if (this.virtualNumbers) {
              this.virtualSwitch = true
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 绑定虚拟号
    BindingVirtualNumbers() {
      bindVirtualMobile({
        shopId: this.$route.query.id
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('绑定成功')
            this.virtualNumbers = res.data.data || ''
            if (this.virtualNumbers) {
              this.virtualSwitch = true
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleClose() {
      this.virtualNumbers = ''
      this.showVirtualNumbers = false
      this.virtualSwitch = false
    },
    changeLimitation(e) {
      MerchantsLimitedCompile({
        open: e,
        shopId: this.$route.query.id
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.error('修改失败')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    changeSecondTemplate(e) {
      const secondConfigDTO = {
        secondTemplate: e ? 1 : 0
      }
      MerchantInfoUpdateBenefit({
        shopId: this.$route.query.id,
        secondConfigDTO: JSON.stringify(secondConfigDTO),
        businessType: 6
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.error('修改失败')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    changeDataSource(flag, type) {
      const content = `是否确认${flag ? '关闭' : '打开'}${
        type ? '估价数据源' : '收车数据源'
      }?`
      this.$confirm(content, '二次确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.clueSourceUpdateFun(flag, type)
        })
        .catch(() => {})
    },
    clueSourceUpdateFun(flag, type) {
      const parameter = {
        shopId: this.$route.query.id
      }
      if (type) {
        parameter.secondAssessClue = flag ? 0 : 1
      } else {
        parameter.secondTestClue = flag ? 0 : 1
      }
      clueSourceUpdate(parameter)
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
          } else {
            this.$message.error('修改失败')
          }
        })
        .finally(() => {
          this.getData()
        })
    },
    changeValid(data) {
      if (!data.valid) {
        this.$confirm(`${data.brandName}品牌是否确认无效`, '确认无效', {
          confirmButtonText: '确认无效',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.changeValidFun(data)
          })
          .catch(() => {
            this.getData(true)
          })
      } else {
        this.changeValidFun(data)
      }
    },
    changeValidFun(data) {
      updateValid({
        shopId: this.$route.query.id,
        brandId: data.brandId,
        valid: data.valid ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
            postLog(
              105,
              this.$route.query.id,
              '编辑',
              `${data.brandName}：${data.valid ? '打开' : '关闭'}「是否有效」`,
              '{}',
              '{}'
            )
          } else {
            this.$message.error('修改失败')
          }
        })
        .finally(() => {
          this.getData(true)
        })
    },

    changeShowStatusFunc(data) {
      updateShowStatus({
        shopId: this.$route.query.id,
        brandId: data.brandId,
        showStatus: data.showStatus ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
            postLog(
              105,
              this.$route.query.id,
              '编辑',
              `${data.brandName}：${
                data.showStatus ? '打开' : '关闭'
              }「商家版在售展示」`,
              '{}',
              '{}'
            )
          } else {
            this.$message.error('修改失败')
          }
        })
        .finally(() => {
          this.getData(true)
        })
    },

    changeShowStatus(data) {
      if (!data.showStatus) {
        this.$confirm(`确认关闭商家版在售展示`, '确认关闭', {
          confirmButtonText: '确认关闭',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.changeShowStatusFunc(data)
          })
          .catch(() => {
            this.getData(true)
          })
      } else {
        this.changeShowStatusFunc(data)
      }
    },

    changeFactoryCheckFunc(data) {
      updateFactoryCheck({
        relId: data.relId,
        factoryCheck: data.factoryCheck ? 1 : 0
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('修改成功')
            postLog(
              105,
              this.$route.query.id,
              '编辑',
              `${data.brandName}：${
                data.factoryCheck ? '打开' : '关闭'
              }「是否允许厂家查看数据」`,
              '{}',
              '{}'
            )
          } else {
            this.$message.error('修改失败')
          }
        })
        .catch(() => {
          this.$message.error('修改失败')
        })
        .finally(() => {
          this.getData(true)
        })
    },

    changeFactoryCheck(data) {
      if (!data.factoryCheck) {
        this.$confirm(`确认关闭厂家查看数据`, '确认关闭', {
          confirmButtonText: '确认关闭',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.changeFactoryCheckFunc(data)
          })
          .catch(() => {
            this.getData(true)
          })
      } else {
        this.changeFactoryCheckFunc(data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.head {
  padding: 20px 0 10px 20px;
  line-height: 50px;
  border-bottom: 1px solid #ddd;

  .price {
    width: 200px;
    margin-left: 10px;
    border-radius: 5px;
  }
}

.head-title {
  padding-left: 20px;

  span {
    font-weight: 400;
  }

  .is-strong {
    font-weight: 600;
  }
}

.detail-entry {
  padding: 10px 10px 20px;
  border-radius: 5px;

  .content {
    /* border: 1px solid #dcdfe6; */
    border-radius: 5px;
    height: 100%;
    /* overflow: hidden; */
    overflow-x: auto;

    .content-title {
      padding: 10px;
      margin: 0;
      font-size: 20px;
      font-weight: bold;
      /* border-bottom: 1px solid #dcdfe6; */
    }

    .content-content {
      padding-top: 10px;

      .type-operate {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin-right: 10px;
        }
      }

      .limit-date {
        color: red;
        line-height: 1;
        /* margin-left: 30px; */
        margin: 0;
        padding: 0;
      }

      .el-form-item {
        /* width: 500px; */
        position: relative;
      }

      .brand-list {
        /* width: 800px; */
        .item {
          color: #909399;
          font-size: 14px;
          text-align: center;
          line-height: 40px;
          border: 1px solid #ebeef5;
        }

        .el-row {
          padding: 0 !important;
        }
      }

      .service-list {
        // width: auto;
        .el-checkbox:nth-of-type(1) {
          margin-left: 30px;
        }
      }

      .img-content {
        display: block;
        margin-top: 10px;
        height: 100px;
        width: 100px;
        object-fit: cover;
        cursor: pointer;
      }

      .see-more-img {
        position: relative;
        top: 40px;
        left: 90px;
      }

      .bussness-data {
        display: flex;

        .roleCodeShow {
          background-color: #e6f8e7;
        }

        .roleCodeHide {
          background-color: #fff;
        }

        .bussness-data-bus {
          position: relative;
          border: 1px solid #eee;
          font-size: 14px;
          width: 240px;
          height: 40px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .span-sty-text {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 198px;
            padding: 0 30px;
          }

          .business-day {
            position: absolute;
            right: 24px;
            top: 12px;
          }
        }
      }
    }
  }
}

.el-row {
  padding: 20px;
}

.dialog-content {
  z-index: 2;

  .el-dialog {
    margin-top: 5vh;
  }
}

.line {
  width: 100%;
  height: 1px;
  background-color: #ddd;
}

.member-info {
  padding-left: 20px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  /* height: 40px; */
  width: 900px;

  .member-info-type {
    width: 110px;
    line-height: 36px;
  }

  .member-info-item {
    line-height: 36px;

    .member-info-level {
      display: inline-block;
      width: 185px;
    }

    .member-info-timer {
      display: inline-block;
      width: 380px;
    }
  }
}

.restricted-mode {
  display: inline-block;
  span {
    margin: 0 5px 0 10px;
    position: relative;
    top: 2px;
  }
  .switch-box {
    display: inline-block;
  }
}
.hidden-input input {
  display: none;
}

.button-box-style {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.mandate-style {
  display: inline-block;
  position: relative;
  .icon-style {
    position: absolute;
    top: 5px;
  }
}

.replace-status {
  margin-left: 5px;
  color: #409eff;
}
</style>
