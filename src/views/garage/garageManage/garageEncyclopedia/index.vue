/** 车辆参数百科 **/
<template>
  <div class="c-new-garage">
    <div class="header">
      <div class="actions">
        <el-button type="primary" @click="goSaveData" :loading="saving"
          >保存</el-button
        >
        <el-button @click="goBack" :disabled="saving">取消</el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <el-form v-else ref="ruleForm" :model="ruleForm" label-width="120px" inline>
      <div class="modules-container">
        <div class="modules" v-for="(module, index) in formConfig" :key="index">
          <p class="modules-name">{{ module.moduleName }}</p>
          <div class="form-grid">
            <el-form-item
              v-for="(field, idx) in module.fields"
              :key="idx"
              :label="field.label"
              :label-width="field.labelWidth || defaultLabelWidth"
            >
              <el-input
                v-model="ruleForm[field.prop]"
                :placeholder="field.placeholder || defaultPlaceholder"
                clearable
              />
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { carReleateEassy, getCarReleateEassy } from '@/api/garage'
export default {
  name: 'GarageEncyclopedia',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      saving: false,
      defaultLabelWidth: '160px',
      defaultPlaceholder: '请输入文章ID',
      ruleForm: {},

      // 表单配置
      formConfig: [
        {
          moduleName: '基本参数',
          fields: [
            { label: '参考价', prop: 'goodPrice' },
            { label: '车辆类型', prop: 'typeName' },
            { label: '厂家', prop: 'factoryName' },
            { label: '品牌', prop: 'brandName' },
            { label: '生产方式', prop: 'product' },
            { label: '产地', prop: 'productPlace' },
            { label: '整车质保', prop: 'vehicleWarranty' },
            { label: '驾照政策', prop: 'needDriversLicense' },
            { label: '官方续航里程(km)', prop: 'goodEndurance' },
            {
              label: '重要部件质保',
              prop: 'qualityOfImportantComponents'
            },
            { label: '车辆属性', prop: 'newEnergyTypes' },
            { label: '电池规格', prop: 'goodBatterySpecification' },
            {
              label: '官方0-50km/h加速(s)',
              prop: 'officialRealGoodBreak50Metre'
            },
            {
              label: '官方0-100km/h加速(s)',
              prop: 'goodBreakMetre'
            },
            {
              label: '实测0-100km/h加速(s)',
              prop: 'realGoodBreakMetre'
            },

            { label: '最高车速(km/h)', prop: 'goodMaxSpeed' }
          ]
        },
        {
          moduleName: '发动机参数',
          fields: [
            { label: '发动机型号', prop: 'engineType' },
            { label: '排量(cc)', prop: 'goodVolume' },
            { label: '精确排量(cc)', prop: 'goodExactVolume' },
            { label: '进气形式', prop: 'intakeType' },
            { label: '发动机结构', prop: 'engineStructure' },
            { label: '发动机气缸数', prop: 'goodCylinder' },
            { label: '配气结构', prop: 'goodGasStructure' },
            { label: '缸径x行程(mm)', prop: 'goodCylinderStroke' },
            { label: '发动机冲程', prop: 'goodTravel' },
            { label: '压缩比', prop: 'goodReductRatio' },
            { label: '冷却方式', prop: 'goodCoolDown' },
            { label: '最大功率(kW)', prop: 'goodMaxPower' },
            { label: '最大马力(ps)', prop: 'goodMaxHorse' },
            { label: '最大功率转速(rpm)', prop: 'goodMaxPowerSpeed' },
            { label: '最大扭矩(N·m)', prop: 'goodAcrotOrque' },
            { label: '最大扭矩转速(rpm)', prop: 'goodMaxTorqueSpeed' },
            { label: '燃油形式', prop: 'fuelForm' },
            { label: '燃油标号', prop: 'goodFuel' },
            { label: '供油方式', prop: 'goodOilMode' },
            { label: '环保标准', prop: 'goodEnvStandard' },
            {
              label: '官方平均油耗(L/100km)',
              prop: 'goodOilWear',
              labelWidth: '180px'
            },
            {
              label: '实测平均油耗(L/100km)',
              prop: 'realGoodOilWear',
              labelWidth: '180px'
            }
          ]
        },
        {
          moduleName: '电动机',
          fields: [
            { label: '电机型号', prop: 'goodMotorBrand' },
            { label: '电机布局', prop: 'goodMotorLayout' },
            { label: '电动机冷却方式', prop: 'goodElectricCoolDown' },
            { label: '电机额定功率(kW)', prop: 'goodMotorPower' },
            { label: '电机最大马力(ps)', prop: 'goodMotorMaxHorse' },
            { label: '电机最大(峰值)功率kW)', prop: 'goodMotorMaxPower' },
            {
              label: '电机最大功率转速(rpm)',
              prop: 'goodMotorMaxPowerSpeed'
            },
            { label: '电机最大扭矩(N·m)', prop: 'goodMotorAcrotOrque' },
            {
              label: '电机最大扭矩转速(rpm)',
              prop: 'goodMotorMaxTorqueSpeed'
            },
            { label: '控制器', prop: 'goodMotorControl' },
            { label: '控制器冷却方式', prop: 'goodMotorControlMode' }
          ]
        },
        {
          moduleName: '电池/充电',
          fields: [
            { label: '电池类型', prop: 'goodBatteryType' },
            { label: '电芯类型', prop: 'goodBatteriesType' },
            { label: '额定电压(V)', prop: 'goodVoltage' },
            { label: '额定容量(Ah)', prop: 'goodBatteryCapacity' },
            { label: '电池能量(kWh/度)', prop: 'goodBatteryEnergy' },
            { label: '电池组数量', prop: 'goodBatteryNum' },
            { label: '快拆电池组', prop: 'goodBatteryTearOpen' },
            { label: '充电时间(h)', prop: 'goodChargingTime' },
            { label: '快充时间(h)', prop: 'goodQuickCharge' },
            { label: '充放电循环次数', prop: 'goodCycleCharge' },
            { label: '可兼容充电', prop: 'goodBatteryCompatible' },
            { label: '充放电耐温范围(℃)', prop: 'goodTemperatureRange' },
            { label: '车辆充电口', prop: 'goodBatteryChargingPort' },
            { label: '电池自加热系统', prop: 'goodBatterySelfHeating' },
            { label: '外放电功能', prop: 'goodExternalDischarge' },
            { label: '充电器功能', prop: 'goodChargerFunctionShow' },
            { label: 'BMS电池管理系统', prop: 'goodBmsShow' }
          ]
        },
        {
          moduleName: '悬挂系统',
          fields: [
            { label: '前悬挂系统', prop: 'goodFrontSuspension' },
            { label: '前悬挂调节', prop: 'goodFrontSuspensionTj' },
            { label: '前悬挂直径/行程', prop: 'goodFrontSuspensionXc' },
            { label: '后悬挂系统', prop: 'goodRearSuspension' },
            { label: '后悬挂调节', prop: 'goodRearSuspensionTj' },
            { label: '后悬挂行程', prop: 'goodBackSuspensionTrip' },
            { label: '后摇臂', prop: 'goodRockerArm' },
            { label: '电子悬挂', prop: 'dynamicESA' }
          ]
        },
        {
          moduleName: '动力传输',
          fields: [
            { label: '驾驶模式', prop: 'goodDrivingMode' },
            { label: '变速器型式', prop: 'goodSpeedMode' },
            { label: '离合器', prop: 'goodClutchMode' },
            { label: '传动方式', prop: 'goodDriveType' },
            { label: '动力档位', prop: 'goodDriveGear' },
            { label: '电子离合器', prop: 'electronicClutch' },
            { label: '滑动离合器', prop: 'goodClutchModeStandard' }
          ]
        },
        {
          moduleName: '车体参数',
          fields: [
            { label: '长x宽x高(mm)', prop: 'goodSize' },
            { label: '座高(mm)', prop: 'goodSaddleHigh' },
            { label: '轴距(mm)', prop: 'goodWheelBase' },
            { label: '座垫长度(mm)', prop: 'goodSeatCushionLength' },
            { label: '车架型式', prop: 'goodFrame' },
            { label: '主油箱容量(L)', prop: 'goodOilBox' },
            { label: '副油箱容量(L)', prop: 'viceGoodOilBox' },
            { label: '干重(kg)', prop: 'goodEmptyWeight' },
            { label: '整备质量(kg)', prop: 'goodAllWeight' },
            { label: '前倾角度(°)', prop: 'goodForwardAngle' },
            { label: '拖曳距(mm)', prop: 'goodDragDistance' },
            { label: '最小离地间隙(mm)', prop: 'goodClearance' },
            { label: '最小转弯半径(m)', prop: 'goodMinTurn' },
            { label: '最大有效载荷(kg)', prop: 'goodMaxLoad' },
            { label: '可选颜色', prop: 'goodColor' }
          ]
        },
        {
          moduleName: '车轮制动',
          fields: [
            { label: '轮胎品牌', prop: 'brandModel' },
            { label: '前轮规格', prop: 'goodFrontWheel' },
            { label: '后轮规格', prop: 'goodBackWheel' },
            { label: '轮胎形式', prop: 'goodTyreType' },
            { label: '轮辋', prop: 'goodWheelRim' },
            { label: '前制动系统', prop: 'goodFrontBrake' },
            { label: '后制动系统', prop: 'goodRearBrake' }
          ]
        },
        {
          moduleName: '主/被动安全配置',
          fields: [
            { label: 'ABS防抱死', prop: 'goodAbs' },
            { label: 'TCS牵引力控制', prop: 'goodTCS' },
            { label: 'CBS联动刹车', prop: 'goodCBS' },
            { label: 'ABS关闭功能', prop: 'aBSOnOff' },
            { label: '胎压监测', prop: 'tirePressureMonitoring' },
            { label: '牵引力控制', prop: 'tractionControlSystem' },
            { label: '转向阻尼器', prop: 'steeringControlSystem' },
            { label: '毫米波雷达', prop: 'millimeterWaveRadarShow' },
            { label: 'SOS紧急呼叫', prop: 'goodSOS' },
            {
              label: '弯道ABS(ABS PRO/ABS EVO等)',
              prop: 'curveABS',
              labelWidth: '240px'
            }
          ]
        },
        {
          moduleName: '辅助/操控配置',
          fields: [
            {
              label: '主动安全预警系统',
              prop: 'activeSafetyWarningSystemShow'
            },
            { label: '油门配置', prop: 'electronicThrottle' },
            { label: 'HHC坡道驻车', prop: 'hillStartAssistControl' },
            { label: 'OTD单转把模式', prop: 'singleHandleMode' },
            { label: '巡航系统', prop: 'cruiseSystemShow' },
            { label: '倒车辅助', prop: 'reverseAssist' },
            { label: '助力推行', prop: 'assistedPushing' },
            { label: '边撑感应', prop: 'edgeSupportSensing' },
            { label: '能量回收', prop: 'energyRecoveryShow' },
            { label: '转向灯自动回正', prop: 'turnSignalAutoCorrection' },
            { label: '模式选择', prop: 'modeSelection' },
            { label: '快速换挡系统', prop: 'quickShiftSystem' },
            { label: '定速巡航', prop: 'cruiseControl' },
            { label: '液压离合器', prop: 'fluidClutch' },
            { label: '发动机启停', prop: 'engineStartOrStop' },
            { label: '脉冲点火系统', prop: 'pulseIgnitionSystem' }
          ]
        },
        {
          moduleName: '智能化配置',
          fields: [
            { label: '仪表屏幕', prop: 'dashboardSizeShow' },
            { label: '智能驾驶系统', prop: 'intelligentDrivingSystem' },
            {
              label: '手机连接功能(蓝牙/WIFI等)',
              prop: 'electronicConnection',
              labelWidth: '200px'
            },
            { label: '手机互联映射', prop: 'phoneMapping' },
            { label: '语音控制', prop: 'voiceControl' },
            { label: '面部识别', prop: 'faceRecognition' },
            { label: '智能APP', prop: 'smartAppShow' },
            { label: 'OTA升级', prop: 'dynamicOTA' }
          ]
        },
        {
          moduleName: '多媒体/舒适性配置',
          fields: [
            { label: '仪表盘', prop: 'goodDashboard' },
            { label: '导航投屏功能', prop: 'navigationSystem' },
            { label: '挡位显示', prop: 'goodDriveGearShow' },
            { label: 'USB充电口', prop: 'uSBCharging' },
            { label: '电加热手把', prop: 'electricHeatingHandle' },
            { label: '电加热座垫', prop: 'electricHeatingSeatCushion' },
            { label: '风挡', prop: 'windshieldModel' },
            {
              label: '手机App远程控制',
              prop: 'mobileAppRemoteControlShow'
            },
            { label: '无钥匙启动', prop: 'keylessStartShow' }
          ]
        },
        {
          moduleName: '影音娱乐',
          fields: [{ label: '扬声器', prop: 'speakers' }]
        },
        {
          moduleName: '舒适/防盗配置',
          fields: [
            { label: '无钥匙解锁', prop: 'keylessUnlockShow' },
            { label: '防盗', prop: 'antiTheftShow' },
            { label: 'USB/Type-C接口', prop: 'usbCountShow' }
          ]
        },
        {
          moduleName: '储物空间',
          fields: [
            { label: '座桶容积', prop: 'bucketVolumeShow' },
            { label: '踏板尺寸', prop: 'pedalSize' },
            { label: '前置物篮', prop: 'frontBasket' },
            { label: '前储物格', prop: 'frontStorage' }
          ]
        },
        {
          moduleName: '灯光配置',
          fields: [
            { label: '前灯', prop: 'goodHeadlights' },
            { label: '后灯', prop: 'goodTaillight' },
            { label: '转向灯', prop: 'goodTurnlight' },
            { label: '辅助灯', prop: 'goodAssistlight' },
            { label: '危险警示灯(双闪)', prop: 'goodWarninglight' },
            { label: '自动大灯', prop: 'automaticHeadlamp' },
            { label: '氛围灯', prop: 'goodAmbientlightShow' }
          ]
        },
        {
          moduleName: '其他配置',
          fields: [
            { label: '上市时间', prop: 'goodTime' },
            { label: '生产状态', prop: 'goodSale' },
            { label: '配置/选项', prop: 'labelsConfig' },
            { label: '选装包', prop: 'labelsPack' }
          ]
        }
      ]
    }
  },
  activated() {
    this.$refs.ruleForm && this.$refs.ruleForm.resetFields() // 重置验证
    this.initializeForm()
    this.getCarReleateEassy()
  },
  methods: {
    // 初始化表单数据
    initializeForm() {
      const formData = {}
      this.formConfig.forEach((module) => {
        module.fields.forEach((field) => {
          formData[field.prop] = ''
        })
      })
      this.ruleForm = formData
    },
    getCarReleateEassy() {
      getCarReleateEassy()
        .then((response) => {
          const data = response.data
          if (data.code === 0) {
            if (data.data && data.data.length) {
              const objs = {}
              data.data.map((item) => {
                objs[item.attrValues] = item.eassyId || ''
              })
              this.ruleForm = { ...this.ruleForm, ...objs }
              console.log('this.ruleForm==========', this.ruleForm)
            }
          }
        })
        .finally(() => {})
    },
    // 保存
    goSaveData() {
      const attrArr = []
      for (const key in this.ruleForm) {
        const obj = {
          eassyId: this.ruleForm[key],
          attrValues: key
        }
        attrArr.push(obj)
      }

      carReleateEassy({
        attributeStr: JSON.stringify(attrArr)
      }).then((response) => {
        if (response.data.code === 0) {
          this.goBack()
        }
      })
    },
    // 取消
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss">
.c-new-garage {
  padding: 20px;
  .modules {
    border-bottom: 1px solid grey;
    margin-bottom: 20px;
  }
  .modules-name {
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
