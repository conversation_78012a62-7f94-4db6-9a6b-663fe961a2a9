<template>
  <div class="design-content">
    <div class="design-form">
      <div class="design-form-item">
        <span class="label" style="width: 40px">标题</span>
        <DesignTitle
          v-model="designItem.title"
          v-model:titleId="designItem.titleId"
        />
        <!-- <el-input v-model="designItem.title" placeholder="请输入标题" /> -->
      </div>
      <div class="design-form-item">
        <span class="label">描述</span>
        <el-input
          v-model="designItem.desc"
          type="textarea"
          style="width: 300px"
          :rows="3"
          placeholder="请输入描述"
        />
      </div>
      <div class="design-form-item item-img">
        <span class="label">图片/动图</span>
        <el-upload
          v-model:file-list="designItem.img"
          list-type="picture-card"
          multiple
          :http-request="httpRequest"
          :on-success="onSuccess"
          :on-preview="preview"
          name="upfile"
          class="design-uploader"
          action
        >
          <div>
            <el-icon><Plus /></el-icon>
          </div>
        </el-upload>
      </div>
      <div class="design-form-item">
        <el-checkbox v-model="designItem.isSet" :true-label="1" :false-label="0"
          >展示</el-checkbox
        >
      </div>
      <div class="design-form-item">
        <el-button
          type="primary"
          size="small"
          @click="deleteItem(design, index)"
          >删除</el-button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { useDesign } from './useDesign'
import { $aliOss } from '@/utils/svc/aliOss'
import DesignTitle from './design-title.vue'

const { deleteItem } = useDesign()

const emit = defineEmits([])

const props = defineProps({
  designItem: {
    type: Object,
    default: () => {}
  },
  index: {
    type: Number,
    default: 0
  },
  design: {
    type: Object,
    default: () => {}
  }
})

const designItem = computed(() => {
  return props.designItem
})

const httpRequest = (option) => {
  //压缩透明度丢失
  option.notCompress = true
  $aliOss.ossUploadImage(option)
}
const onSuccess = (res) => {
  if (!res) return
  const index = designItem.value.img.findIndex((item) => {
    return item.name === res.name
  })
  if (index > -1) {
    designItem.value.img.splice(index, 1, {
      url: res.imgUrl
    })
  } else {
    designItem.value.img.push({
      url: res.imgUrl
    })
  }
  // designItem.value.img.splice(props.designItem.img.length - 1, 1, {
  //   url: res.imgUrl
  // })
}

const preview = (file) => {
  window.open(file.url)
}
</script>

<style scoped lang="scss">
.design-content {
  padding: 10px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 10px;
}

.design-form {
  display: flex;
  /* flex-wrap: wrap; */
}

.design-form-item {
  display: flex;
  align-items: center;
  padding: 10px;
}

.item-img {
  flex: 1;
}

.design-form-item .label {
  width: 70px;
  text-align: right;
  margin-right: 10px;
}

.design-uploader {
  display: flex;
  flex-wrap: wrap;
  :deep(.el-upload-list__item) {
    width: 90px;
    height: 90px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  :deep(.el-upload) {
    width: 90px;
    height: 90px;
    line-height: 0px;
  }
}
</style>
