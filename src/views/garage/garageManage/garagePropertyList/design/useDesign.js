import { ElMessageBox } from 'element-plus'
import { ref } from 'vue'
import { v4 as uuidv4 } from 'uuid'

export const useDesign = () => {
  const emptyItem = {
    title: '',
    desc: '',
    img: [],
    titleId: '',
    isSet: 0
  }
  const plusItem = (parent, item) => {
    const id = new Date().getTime()
    if (!item.designList?.length) {
      item.designList = []
    }
    item.designList.push({
      ...emptyItem,
      id,
      categoryId: parent.carTypeId,
      categoryGroupId: item.carTypeId
    })
  }

  const deleteItem = (item, index) => {
    ElMessageBox.confirm('确定删除吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      item.designList?.splice(index, 1)
    })
  }

  return {
    plusItem,
    deleteItem,
    emptyItem
  }
}
