<template>
  <div style="padding: 20px" :class="{ 'point-none': isFreeze }">
    <div class="design-header">
      <div>
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="cancle" class="point-auto">取消</el-button>
      </div>
      <div class="header-right">
        <el-popover placement="bottom-end" width="500px" trigger="click">
          <template #reference>
            <el-button class="mr10">同步亮点至</el-button>
          </template>
          <div class="right-tag">
            <div v-for="item in vehicleList" :key="item.id">
              <el-tag
                size="small"
                :type="selectVehicle.includes(item.carId) ? '' : 'info'"
                @click="selectVehicleHandle(item)"
                >{{ item.goodsCarName }}</el-tag
              >
            </div>
            <el-empty
              v-if="vehicleList.length === 0"
              style="width: 100%"
            ></el-empty>
          </div>
        </el-popover>
        {{ selectVehicle.length ? selectVehicle : '' }}
      </div>
      <div>
        <el-input
          v-model="vehicleId"
          placeholder="多个款型ID用英文逗号隔开"
          style="width: 200px; margin: 0 10px"
        ></el-input>
        <el-button type="primary" @click="syncDesign">同步并保存</el-button>
      </div>
    </div>
    <div>{{ vehicleName }}</div>
    <div class="mt20">
      <div v-for="(item, indx) in designArr" :key="indx">
        <div class="type-name">{{ item.carTypeName }}</div>
        <div v-for="(design, inx) in item.carTypCategoryList" :key="inx">
          <div class="module">
            <span class="mr20 module-name">{{ design.carTypeName }}</span>
            <el-button
              type="primary"
              size="small"
              @click="plusItem(item, design)"
              >添加</el-button
            >
          </div>
          <Draggable v-model="design.designList" item-key="id" animation="300">
            <template #item="{ element, index }">
              <DesignItem
                :designItem="element"
                :design="design"
                :index="index"
              ></DesignItem>
            </template>
          </Draggable>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import Draggable from 'vuedraggable'
import { useDesign } from './useDesign'
import DesignItem from './design-item.vue'
import { debounce } from 'lodash-es'

import {
  getLightTypeCategoryList,
  getLightTypeCategory,
  setLightTypeCategoryData,
  getAgeList,
  getLightTypeCategorySyschrCar
} from '@/api/garage'

const { plusItem } = useDesign()

const route = useRoute()
const router = useRouter()

const designArr = ref([])
const designData = ref([])

const vehicleId = ref('')
const vehicleList = ref([])
const selectVehicle = ref([])

const { id, name, carId, isFreeze } = route.query
const vehicleName = ref(name)

onActivated(() => {
  getDesignCategory()
  getVehicleList()
  vehicleId.value = ''
  designArr.value = []
  designData.value = []
  vehicleId.value = ''
  vehicleList.value = []
  selectVehicle.value = []
})
const getDesignList = () => {
  getLightTypeCategoryList({
    carId: carId
  }).then((res) => {
    designData.value = res.data.data || []
    initSetData()
  })
}

const getDesignCategory = () => {
  getLightTypeCategory({
    carId: carId
  }).then((res) => {
    designArr.value = res.data.data || []
    getDesignList()
  })
}

const initSetData = () => {
  designData.value.forEach((item) => {
    item.img =
      item.imgList?.map((e) => {
        return {
          url: e
        }
      }) || []
    const parent = designArr.value.find(
      (ele) => ele.carTypeId === item.categoryId
    )
    if (parent) {
      const child = parent.carTypCategoryList.find(
        (ele) => ele.carTypeId === item.categoryGroupId
      )
      if (child) {
        if (!child.designList) {
          child.designList = []
        }
        child.designList.push(item)
      }
    }
  })
  vehicleId.value = ''
  console.log(designData.value)
}

const save = debounce(() => {
  initSave().then((res) => {
    ElMessage.success('保存成功')
    setTimeout(() => {
      router.go(-1)
    }, 800)
  })
}, 500)

const initSave = () => {
  return new Promise((resolve, reject) => {
    const temp = JSON.parse(JSON.stringify(designArr.value))

    const dataMap = []
    let emptyStr = ''
    temp.forEach((item) => {
      item.carTypCategoryList?.forEach((ele) => {
        ele.designList?.forEach((e) => {
          e.imgList = e.img.map((i) => i.url)
          dataMap.push(e)
          if (!e.title || !e.imgList.length || !e.desc) {
            emptyStr = `请完善${item.carTypeName}下的${ele.carTypeName}的数据`
          }
        })
      })
    })
    if (emptyStr) {
      return ElMessage.error(emptyStr)
    }

    setLightTypeCategoryData({
      carId: carId,
      lightStr: JSON.stringify(dataMap)
    })
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

const getVehicleList = () => {
  getAgeList({
    goodsId: id,
    page: 1,
    limit: 100
  }).then((res) => {
    const list = res.data.data.list || []
    vehicleList.value = list.filter((item) => item.carId != carId)
  })
}

const cancle = () => {
  router.go(-1)
}

const selectVehicleHandle = (item) => {
  if (selectVehicle.value.includes(item.carId)) {
    selectVehicle.value = selectVehicle.value.filter((ele) => ele != item.carId)
  } else {
    selectVehicle.value.push(item.carId)
  }
}

const syncDesign = () => {
  if (!selectVehicle.value.length && !vehicleId.value) {
    return ElMessage.error('请选择款型或输入款型ID')
  }
  ElMessageBox.confirm('确定同步并保存吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const tempArr = vehicleId.value.split(',').filter((item) => item)
      const temp = [...tempArr, ...selectVehicle.value]
      const carIds = temp.join(',')

      await initSave()
      getLightTypeCategorySyschrCar({
        fromCarId: carId,
        toCarIds: carIds
      }).then((res) => {
        ElMessage.success('操作成功')
        setTimeout(() => {
          router.go(-1)
        }, 800)
      })
    })
    .catch(() => {
      ElMessage.info('已取消')
    })
}
</script>
<style>
.right-tag {
  display: flex;
  max-width: 1000px;
  gap: 10px;
  flex-direction: row;
  flex-wrap: wrap;
}
</style>
<style lang="scss" scoped>
.design-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-right {
    display: flex;
    flex: 1;
    align-items: center;
    margin-left: 20px;
    justify-content: flex-end;
  }

  .right-tag {
    display: flex;
    max-width: 1000px;
    height: 100px;
    // overflow-y: auto;
    gap: 10px;
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.type-name {
  font-size: 20px;
  font-weight: 600;
  color: #67c23a;
}

.module {
  margin: 20px 0;
}

.module-name {
  font-size: 17px;
  font-weight: 600;
}
</style>
