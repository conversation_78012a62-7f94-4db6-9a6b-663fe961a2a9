<template>
  <div class="flex">
    <el-select
      remote
      filterable
      v-model="currentTag"
      placeholder="不超过20个字符"
      clearable
      :remote-method="remoteMethod"
      :loading="loading"
      style="width: 330px"
      @change="updateTag"
    >
      <el-option
        v-for="(option, index) in options"
        :key="index"
        :label="option.title"
        :value="option.title"
      />
    </el-select>
    <!-- <el-button
      v-if="!options.length && currentTag"
      class="ml10"
      type="primary"
      link
      @click="insertTag"
      >添加至标题库</el-button
    > -->
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import {
  getLightTypeCategoryTitle,
  setLightTypeCategoryTitle
} from '@/api/garage'
import { ref, getCurrentInstance, watchPostEffect } from 'vue'
const proxy = getCurrentInstance().proxy
const emit = defineEmits(['update:titleId', 'update:modelValue'])

const loading = ref(false)
const options = ref([])

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  titleId: {
    default: ''
  }
})
const currentTag = computed({
  get: () => props.modelValue,
  set: (val) => {
    if (val?.length > 20) {
      ElMessage.error('标题长度不能超过20个字符')
      val = val?.substring(0, 20)
    }
    emit('update:modelValue', val)
  }
})

const tagId = computed({
  get: () => props.titleId,
  set: (val) => {
    emit('update:titleId', val)
  }
})

const updateTag = (val) => {
  const opt = options.value.find((item) => item.title == val)
  tagId.value = opt?.id || ''
}

const remoteMethod = (query) => {
  if (!query) return
  loading.value = true
  currentTag.value = query
  tagId.value = ''
  proxy.$tools.debounce(() => getList(query), 300)()
}

const getList = (query) => {
  getLightTypeCategoryTitle({ title: query })
    .then((res) => {
      if (res.data.code == 0) {
        options.value = res.data.data || []
      } else {
        options.value = []
      }
    })
    .finally(() => {
      loading.value = false
    })
}
</script>
<style lang="scss" scoped></style>
