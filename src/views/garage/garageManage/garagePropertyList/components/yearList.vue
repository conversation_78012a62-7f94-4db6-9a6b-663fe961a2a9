<template>
  <div class="year-list">
    <el-row :gutter="24">
      <el-col :span="8">
        &emsp;年代款信息<span class="rule-title_tip">(编辑状态下支持拖拽)</span>
        <el-button type="primary" link class="fl-right" @click="updateYearEdit"
          >{{ isYearEdit ? '保存' : '调整顺序' }}&emsp;</el-button
        >
        <el-button
          type="primary"
          link
          class="fl-right"
          v-if="isYearEdit"
          @click="updateYearEditCancel"
          >取消</el-button
        >
      </el-col>
    </el-row>
    <p class="rule-title rule-title-button" v-if="!isYearEdit">
      <el-button
        type="primary"
        style="margin: 10px 0 0 10px"
        size="small"
        @click="addAgeData()"
        >新建年代款</el-button
      >
      <el-button
        type="primary"
        style="margin: 10px 0 0 10px"
        size="small"
        @click="seeAgeLog()"
        >查看年代款日志</el-button
      >
    </p>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-row class="item header">
          <el-col :span="!isYearEdit ? 6 : 8">年代款</el-col>
          <el-col :span="!isYearEdit ? 6 : 8">年代款状态</el-col>
          <el-col :span="!isYearEdit ? 6 : 8">款型数量</el-col>
          <el-col v-if="!isYearEdit" :span="6">操作</el-col>
        </el-row>
        <draggable
          v-if="isYearEdit && yearList && yearList.length"
          v-model="yearList"
          item-key="carName"
          class="item-container"
        >
          <template #item="{ element, index }">
            <yearAgeList
              :data="element"
              :index="index"
              :isEdit="true"
              :onSaleEnumAll="onSaleEnumAll"
            />
          </template>
        </draggable>
        <div v-if="!isYearEdit && yearList && yearList.length">
          <template v-for="(element, index) in yearList" :key="index">
            <yearAgeList
              :data="element"
              :index="index"
              :isEdit="false"
              :onSaleEnumAll="onSaleEnumAll"
              :car-name="carName"
              @updateAgeData="updateAgeData"
              @deleAgeData="deleAgeData"
              @editAgeData="editAgeData"
            />
          </template>
        </div>
        <div v-if="!yearList.length" class="data-none">暂无信息</div>
      </el-col>
      <el-col v-if="isShowType" :span="16">
        &emsp;款型列表（{{ totalType }}）<span class="rule-title_tip"
          >(编辑状态下支持拖拽)</span
        >
        <el-button
          v-if="carName"
          type="primary"
          link
          class="fl-right"
          @click="updateCarEdit"
          >{{ isCarEdit ? '保存' : '调整顺序' }}&emsp;</el-button
        >
        <el-button
          v-if="carName && isCarEdit"
          type="primary"
          link
          class="fl-right"
          @click="updateCarEditCancel"
          >取消&emsp;</el-button
        >
        <p class="rule-title rule-title-button" v-if="!isCarEdit">
          <el-button
            v-if="carName"
            type="primary"
            style="margin: 10px 0 0 10px"
            size="small"
            @click="addType()"
            >新建款型</el-button
          >
          <el-button
            type="primary"
            style="margin: 10px 0 0 10px"
            size="small"
            class="point-auto"
            @click="clearAgeData()"
            >查询全部款型</el-button
          >
          <el-button
            v-if="carName"
            type="primary"
            style="margin: 10px 0 0 10px"
            size="small"
            @click="seeSortLog()"
            >查看款型顺序日志</el-button
          >
        </p>
        <div class="scroll-box point-auto">
          <div class="list-box">
            <el-row :gutter="24" class="item header">
              <el-col :span="2">摩托范款型ID</el-col>
              <el-col :span="2">电摩范款型ID</el-col>
              <el-col :span="4">款型</el-col>
              <el-col :span="1">价格</el-col>
              <!-- <el-col :span="isCarEdit ? 3 : 2">年代款绑定状态</el-col> -->
              <el-col :span="isCarEdit ? 3 : 2">绑定年代款</el-col>
              <!-- <el-col :span="isCarEdit ? 3 : 2">外观 细节 参数</el-col> -->
              <el-col :span="2">亮点完成度</el-col>
              <el-col :span="2">实测完成度</el-col>
              <el-col :span="1">在售状态</el-col>
              <el-col :span="2">是否上架</el-col>
              <el-col :span="2">图片数量</el-col>
              <el-col :span="4" v-if="!isCarEdit">操作</el-col>
            </el-row>
            <draggable
              v-if="isCarEdit && typeList && typeList.length"
              v-model="typeList"
              class="item-container"
              item-key="carId"
            >
              <template #item="{ element }">
                <yearCarList
                  :data="element"
                  :isEdit="true"
                  :saleEnumAll="saleEnumAll"
                />
              </template>
            </draggable>
            <div v-if="!isCarEdit && typeList && typeList.length">
              <template v-for="(element, index) in typeList" :key="index">
                <yearCarList
                  :data="element"
                  :isEdit="false"
                  :saleEnumAll="saleEnumAll"
                  :goodsCarName="goodsCarName"
                  :carId="selectedCarId"
                  @updateCarData="updateCarData"
                  @editCarType="editType"
                  @changeSwitch="changeSwitch"
                  @editCarData="editCarData"
                  @seeLog="seeLog"
                />
              </template>
            </div>
            <div v-else-if="!typeList.length" class="data-none">暂无信息</div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog v-model="ageUpadteStatus" title="年代款信息" width="80%">
      <el-form :model="updateAge" status-icon label-width="80px">
        <el-form-item label="年代款">
          <el-select
            v-if="isShowAgeListStataus"
            v-model="updateAge.carName"
            filterable
            @change="setUpdataAge(updateAge.carName)"
          >
            <el-option
              v-for="(item, index) in yearList"
              :key="index"
              :label="item.carName"
              :value="item.carName"
            />
          </el-select>
          <div v-else style="display: flex !important">
            <el-select
              v-model="updateAge.carName"
              filterable
              placeholder="请选择年代款"
              @change="updateCarName"
            >
              <el-option
                v-for="(item, index) in allCarNameList"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item
          v-if="!isShowAgeListStataus"
          label="年代款状态"
          label-width="94px"
          required
        >
          <el-select
            v-model="updateAge.status"
            :disabled="
              ['未上市', '停售', '大陆未引进', '即将上市'].includes(
                updateAge.carName
              )
            "
          >
            <el-option
              v-for="(value, index) in onSaleEnum"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="dialog-content-button">
        <el-button type="success" @click="confirmAge()">确认</el-button>
        <el-button type="danger" @click="ageUpadteStatus = false"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <SeeLog ref="seeLog" @success="postReadyData" />
    <seeLog ref="seeLog1" />
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { mapGetters } from 'vuex'
import {
  searchAgePattern,
  addAgePattern,
  updateAgePattern,
  delAgePattern,
  getAgeList,
  updatePattern,
  delPattern,
  updateAgeList,
  updatePatternList,
  getTypeAttr,
  updateCarStatus
} from '@/api/garage'
import { $emit } from '../../../../../utils/gogocodeTransfer'
import SeeLog from '../../components/SeeLog.vue'
import yearAgeList from './yearAgeList.vue'
import yearCarList from './yearCarList.vue'
import seeLog from '@/components/seeLog/SeeLog.vue'
import { postLog } from '@/components/seeLog/SaveLog.js'
import { convertKeyValueEnum } from '@/utils/convert'
import { deepCopy } from '@/utils'
export default {
  name: 'YearList',
  components: {
    draggable,
    SeeLog,
    seeLog,
    yearAgeList,
    yearCarList
  },
  props: {
    brandId: {
      type: Number,
      default: 0
    },
    goodsName: {
      type: String,
      required: true
    },
    energyType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      id: '', // 车辆id
      carName: '', // 年代款名称
      goodsCarName: '', //  款型名称
      selectedCarId: '', // 选中的款型id
      carYearStatus: '', // 年代款状态
      totalType: 0, // 款型个数
      yearList: [], // 年代款信息列表
      draYearList: [], // 年代款信息列表(用于拖拽)
      oldYearList: [], // 历史数据 年代款信息列表
      typeList: [], // 款型列表
      oldTypeList: [], // 历史数据款型列表
      allCarNameList: [], // 所有年代款名字
      saleEnumAll: {
        0: '停售',
        1: '在售',
        2: '即将上市',
        3: '大陆未引进',
        4: '延期上市',
        5: '未知',
        6: '未上市'
      }, // 在售状态列表
      updateAge: {}, // 更新的年代款数据
      isYearEdit: false, // 年代款是否编辑
      isCarEdit: false, // 款型是否编辑
      isShowType: true, // 是否显示款型数据
      ageUpadteStatus: false, // 是否显示年代款弹框（新增，编辑，修改）
      isNewAge: false, // 是否是新增年代款
      isShowAgeListStataus: false, // 是否是款型修改年代款
      onSaleEnum: {
        停售: 0,
        在售: 1,
        即将上市: 2,
        大陆未引进: 3,
        未上市: 4,
        未知: 5
      },
      onSaleEnumAll: {}
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  watch: {
    ageUpadteStatus(value) {
      if (!value) {
        this.updateAge = {
          carName: '',
          carId: '',
          status: ''
        }
        this.isNewAge = false
        this.isShowAgeListStataus = false
      }
    }
  },
  activated() {
    this.invoke()
    console.log('yearlist activated')
  },
  mounted() {
    this.invoke()
    console.log('yearlist mounted')
  },
  methods: {
    invoke() {
      if (this.$route.query.id) {
        this.id = this.$route.query.id
        this.getList()
        if (this.isShowType) {
          this.showMoreData()
        }
      }
      this.onSaleEnumAll = convertKeyValueEnum(this.onSaleEnum)
      this.setAllCarNameList()
    },
    // 设置所有年代款信息
    setAllCarNameList() {
      let allCarNameList = []
      for (let i = 1900; i < 2101; i++) {
        allCarNameList.push(`${i}款`)
      }
      ;[
        '未上市',
        '停售',
        '大陆未引进',
        '即将上市',
        '概念款',
        '公务车款',
        '出口款',
        '定制款'
      ].map((item) => {
        allCarNameList.push(item)
      })
      this.allCarNameList = allCarNameList
    },
    // 获取年代款列表
    getList() {
      searchAgePattern({
        goodsId: this.id
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.yearList = response.data.data
            this.oldYearList = this.yearList
            this.draYearList = this.yearList
          }
        })
        .finally((_) => {})
    },
    // 查看更多数据
    showMoreData() {
      const me = this
      me.isShowType = true
      getAgeList({
        goodsId: me.id,
        carName: me.carName,
        page: 1,
        limit: 50
      })
        .then((response) => {
          if (response.data.code === 0) {
            const res = response.data.data
            res.list.map(function (value) {
              value.isOnStatus = value.isOnStatus === 1
            })
            me.typeList = res.list
            me.oldTypeList = me.typeList
            me.totalType = res.total || 0
          } else {
            me.$message.error(response.data.msg)
            me.ageUpadteStatus = false
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
          me.ageUpadteStatus = false
        })
    },
    // 更新年代款列表
    updataYearList() {
      const me = this
      const carNames = []
      me.yearList.map(function (value) {
        carNames.push(value.carName)
      })
      updateAgeList({
        goodsId: me.id,
        carNames: carNames.join()
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('更新列表成功')
            me.oldYearList = me.yearList
            postLog(
              87,
              me.id || '',
              '调整年代款顺序',
              carNames.join(),
              '{}',
              '{}'
            )
            me.isYearEdit = !me.isYearEdit
          } else {
            me.$message.error(response.data.msg)
            me.yearList = me.oldYearList
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
          me.yearList = me.oldYearList
        })
    },
    // 更新款型列表
    updataTypeList() {
      const me = this
      const carIds = []
      const carNames = []
      me.typeList.map(function (value) {
        carIds.push(value.carId)
        carNames.push(value.carName)
      })
      updatePatternList({
        carIds: carIds.join()
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('更新列表成功')
            me.oldTypeList = me.typeList
            me.isCarEdit = false
            postLog(90, me.id || '', '调整款型顺序', carIds.join(), '{}', '{}')
          } else {
            me.$message.error(response.data.msg)
            me.typeList = me.oldTypeList
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
          me.typeList = me.oldTypeList
        })
    },
    // 删除年代款/款型
    dele(data, index, type) {
      const me = this
      me.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          if (type === 'year') {
            const delName = data[index].carName
            delAgePattern({
              goodsId: me.id,
              carName: delName
            })
              .then((response) => {
                if (response.data.code === 0) {
                  me.$message.success('删除成功')
                  data.splice(index, 1)
                  postLog(87, me.id || '', '年代款删除', delName, '{}', '{}')
                } else {
                  me.$message.error(response.data.msg)
                }
              })
              .catch((err) => {
                me.$message.error(err.data.msg)
              })
            return
          }
          const arr = [{ carId: data[index].carId }]
          delPattern({
            ids: JSON.stringify(arr)
          })
            .then((response) => {
              if (response.data.code === 0) {
                me.$message.success('删除成功')
                data.splice(index, 1)
              } else {
                me.$message.error(response.data.msg)
              }
            })
            .catch((err) => {
              me.$message.error(err.data.msg)
            })
        })
        .catch((err) => {
          me.$message({
            type: 'info',
            message: (err && err.message) || '已取消删除'
          })
        })
    },
    // 编辑年代款
    editAge(data, type) {
      this.updateAge = deepCopy(data)
      this.isShowAgeListStataus = type === 'type'
      if (type === 'type') {
        this.updateAge.carName = this.carName
      }
      this.updateAge.oldCarName = this.updateAge.carName
      this.updateAge.oldStatus = this.updateAge.status
      this.ageUpadteStatus = true
    },

    // 确认更新年代款
    confirmAge() {
      if (this.isNewAge) {
        this.isNewAge ? this.addAge() : null
        return
      }
      this.setLog()
    },
    // 设置日志
    setLog() {
      console.log(this.updateAge)
      if (
        this.updateAge.oldCarName === this.updateAge.carName &&
        this.updateAge.oldStatus === this.updateAge.status
      ) {
        return this.postReadyData()
      }
      let showList = []
      if (this.updateAge.oldCarName !== this.updateAge.carName) {
        showList.push({
          name: 'yearName',
          oldValue: this.updateAge.oldCarName,
          newValue: this.updateAge.carName
        })
      }
      if (this.updateAge.oldStatus !== this.updateAge.status) {
        showList.push({
          name: 'status',
          oldValue: this.onSaleEnumAll[this.updateAge.oldStatus],
          newValue: this.onSaleEnumAll[this.updateAge.status]
        })
      }
      this.$refs.seeLog.showList(showList, {
        yearName: '年代款名称',
        status: '年代款状态'
      })
    },
    // 准备发送
    postReadyData() {
      if (this.isNewAge || !this.isShowAgeListStataus) {
        return this.addAge()
      } else {
        this.postUpdateAge()
      }
    },
    // 新增年代款或更新
    addAge() {
      const me = this
      if (me.updateAge.carName === '') {
        return me.$message.error('请填写年代款')
      }
      if (!me.updateAge.status && me.updateAge.status !== 0) {
        return me.$message.error('请选择年代款状态')
      }
      const url = me.isNewAge ? addAgePattern : updateAgePattern
      url({
        goodsId: me.id,
        carName: me.updateAge.carName,
        oldCarName: me.updateAge.oldCarName,
        status: me.updateAge.status
      })
        .then((response) => {
          if (response.data.code === 0) {
            const toastTitle = me.isNewAge ? '新建成功' : '更新成功'
            me.$message.success(toastTitle)
            me.getList()
            me.ageUpadteStatus = false
            me.isShowType = false
            let tip = `状态为 ${me.onSaleEnumAll[me.updateAge.status]}`
            tip =
              me.updateAge.carName === me.updateAge.oldCarName || me.isNewAge
                ? `名称: ${
                    me.updateAge.oldCarName || me.updateAge.carName
                  }, ${tip}`
                : `名称: ${me.updateAge.oldCarName} 变更为 ${me.updateAge.carName},${tip}`
            postLog(
              87,
              me.id || '',
              me.isNewAge ? '新增' : '编辑',
              tip,
              '{}',
              '{}'
            )
          } else {
            me.$message.error(response.data.msg)
            me.ageUpadteStatus = false
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
          me.ageUpadteStatus = false
        })
    },
    // selecte 选框 change 事件，处理因dom节点无更新，页面无改动
    setUpdataAge(data) {
      const tempObject = this.updateAge
      this.updateAge = {}
      this.updateAge = tempObject
    },
    // 新增或编辑是，判定，如果是停售，状态也是停售
    updateCarName(name) {
      if (['未上市', '停售', '大陆未引进', '即将上市'].includes(name)) {
        this.updateAge.status = this.onSaleEnum[name]
      }
    },
    // 款型修改年代款时
    postUpdateAge() {
      const me = this
      updatePattern({
        carId: me.updateAge.carId,
        carName: me.updateAge.carName
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('更新成功')
            me.getList()
            me.showMoreData()
            me.ageUpadteStatus = false
            postLog(
              77,
              me.updateAge.carId || '',
              '编辑',
              `carNa me: 变更为 ${me.updateAge.carName}`,
              '{}',
              '{}'
            )
          } else {
            me.$message.error(response.data.msg)
            me.ageUpadteStatus = false
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
          me.ageUpadteStatus = false
        })
    },
    // 修改在售状态
    changeSwitch(item) {
      if (this.isYearEdit || this.isCarEdit) {
        item.isOnStatus = !item.isOnStatus
        return this.$message.error('调整顺序下，不可操作')
      }
      const tip = `是否确认${item.isOnStatus ? '上' : '下'}架？`
      const me = this
      me.$confirm(tip, '二次确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      })
        .then(() => {
          Action()
        })
        .catch((err) => {
          item.isOnStatus = !item.isOnStatus
        })
      const Action = () => {
        updateCarStatus({
          carId: item.carId,
          status: item.isOnStatus === true ? 1 : 0
        })
          .then((response) => {
            if (response.data.code === 0) {
              this.$message.success('修改成功')
              this.showMoreData()
              postLog(
                77,
                item.carId || '',
                '编辑',
                `isOnStatus: ${item.isOnStatus} -> ${!item.isOnStatus}`,
                '{}',
                '{}'
              )
            } else {
              this.$message.error('修改失败')
            }
          })
          .catch(() => {
            item.isOnStatus = !item.isOnStatus
          })
      }
    },
    // 新建款型
    addType() {
      if (this.isYearEdit || this.isCarEdit) {
        return this.$message.error('调整顺序下，不可操作')
      }
      if (this.energyType == '2') {
        return this.$message.error('纯电动车型请前往电摩范管理！')
      }
      sessionStorage.removeItem('createGarage')
      this.$router.push({
        name: 'createGarage',
        query: {
          id: this.id,
          brandId: this.brandId,
          carYear: this.carName,
          carYearStatus: this.carYearStatus,
          goodsName: this.goodsName,
          energyType: this.energyType
        }
      })
    },
    // 编辑
    editType(item) {
      if (this.isYearEdit || this.isCarEdit)
        return this.$message.error('调整顺序下，不可操作')
      sessionStorage.removeItem('editGarage')
      let current = {}
      if (this.carYearStatus) {
        current = this.yearList.find((yl) => yl.carName === item.carYear) || {}
      }
      this.$router.push({
        name: 'editGarage',
        query: {
          id: this.id,
          brandId: this.brandId,
          carId: item.carId,
          carYear: this.carName || item.carYear,
          carYearStatus: this.carYearStatus || current.status,
          eCarId: item.eCarId || ''
        }
      })
    },
    // 查看日志
    seeLog(data) {
      this.$refs.seeLog1.init('77', data.carId)
    },
    // 年代款编辑｜保存年代款顺序
    updateYearEdit() {
      if (this.isCarEdit) return this.$message.error('调整顺序下，不可操作')
      if (!this.isYearEdit) {
        this.isYearEdit = !this.isYearEdit
      } else {
        const me = this
        me.$confirm(`是否确认修改当前顺序?`, '二次确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          center: true
        }).then(() => {
          me.updataYearList()
        })
      }
    },
    // 取消保存年代款顺序
    updateYearEditCancel() {
      this.isYearEdit = !this.isYearEdit
      this.yearList = this.oldYearList
    },
    // 点击年代款
    updateAgeData(data) {
      if (this.isYearEdit || this.isCarEdit) return
      this.carName = data.carName
      this.carYearStatus = data.status
      this.showMoreData()
    },
    // 点击款型
    updateCarData(data) {
      if (this.isYearEdit || this.isCarEdit) return
      this.goodsCarName = data.goodsCarName
      this.selectedCarId = data.carId
    },
    // 清除年代款
    clearAgeData() {
      if (this.isYearEdit || this.isCarEdit)
        return this.$message.error('调整顺序下，不可操作')
      this.carName = ''
      this.carYearStatus = ''
      this.showMoreData()
    },
    // 删除年代款
    deleAgeData(index) {
      if (this.isCarEdit) return this.$message.error('调整顺序下，不可操作')
      this.dele(this.yearList, index, 'year')
    },
    // 新建年代款
    addAgeData() {
      if (this.isCarEdit) return this.$message.error('调整顺序下，不可操作')
      this.ageUpadteStatus = true
      this.isNewAge = true
    },
    // 编辑年代款
    editAgeData(element) {
      if (this.isCarEdit) return this.$message.error('调整顺序下，不可操作')
      this.editAge(element, 'year')
    },
    // 编辑款型
    editCarData(element) {
      if (this.isYearEdit) return this.$message.error('调整顺序下，不可操作')
      this.editAge(element, 'type')
    },
    // 查看年代款日志
    seeAgeLog() {
      this.$refs.seeLog1.init('87', this.id)
    },
    // 款型调整顺序
    updateCarEdit() {
      if (this.isYearEdit) return this.$message.error('调整顺序下，不可操作')
      if (!this.isCarEdit) {
        this.isCarEdit = !this.isCarEdit
      } else {
        const me = this
        me.$confirm(`是否确认修改当前顺序?`, '二次确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          center: true
        }).then(() => {
          me.updataTypeList()
        })
      }
    },
    // 款型取消调整顺序
    updateCarEditCancel() {
      this.isCarEdit = !this.isCarEdit
      this.typeList = this.oldTypeList
    },
    // 查看款型顺序日志
    seeSortLog() {
      this.$refs.seeLog1.init('90', this.id)
    }
  },
  emits: ['success']
}
</script>

<style lang="scss">
.item {
  color: #333;
  font-size: 14px;
  text-align: center;
  line-height: 40px;
  border: 1px solid #ebeef5;
  border-width: 0 0px 1px 1px;
  margin: 0 !important;
  .el-col {
    word-break: break-all;
    border-right: 1px solid #ebeef5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-input__inner {
    width: 90%;
    height: 30px !important;
    line-height: 30px !important;
  }
  .el-input__suffix {
    right: 15px;
  }
  &.header {
    border-width: 1px 0 1px 1px;
    width: 100%;
  }
  &.active {
    background: #dcdcdc;
  }
  .item-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-size: 100% 100%;
    position: relative;
    top: 5px;
  }
}
.item-container {
  max-height: calc(90vh - 240px);
}
.data-none {
  color: #606266;
  text-align: center;
  padding: 10px 0;
}
.rule-title {
  margin: 20px 0 10px 0;
}
.rule-title_tip {
  color: #999;
  font-size: 14px;
  padding-left: 10px;
}
.rule-title_button {
  margin-left: 7vw;
}
.rule-title-button {
  margin: 0 0 10px 0;
}
</style>

<style lang="scss" scoped>
.scroll-box {
  width: 100%;
  overflow-x: auto;
  .list-box {
    width: 2000px;
  }
}
</style>
