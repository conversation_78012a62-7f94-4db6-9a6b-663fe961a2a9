<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="year-show-list">
    <el-row
      :gutter="18"
      class="item"
      @dblclick="editType()"
      @click="updateCarData()"
      :class="{ 'item-selected': data.carId === carId }"
    >
      <el-col :span="2">
        <span>{{ data.carId }}</span>
      </el-col>
      <el-col :span="2">
        <span>{{ data.eCarId }}</span>
      </el-col>
      <el-col :span="4">
        <p class="goodsCarName">
          {{ data.goodsCarName }}
        </p>
      </el-col>
      <el-col :span="1">
        <span>{{ data.goodsPrice }}</span>
      </el-col>
      <!-- <el-col :span="isEdit ? 3 : 2">
        <span>{{ data.carYear ? '已' : '未' }}绑定</span>
      </el-col> -->
      <el-col :span="isEdit ? 3 : 2">
        <span>{{ data.carYear }}</span>
      </el-col>
      <!-- <el-col :span="isEdit ? 3 : 2">
        <img
          v-if="data.standardNum && data.standardNum.indexOf('1') > -1"
          src="@/assets/image/select-icon.png"
          class="item-icon"
          alt
        />
        <img v-else src="@/assets/image/icon.png" class="item-icon" alt />
        <img
          v-if="data.standardNum && data.standardNum.indexOf('2') > -1"
          src="@/assets/image/select-icon.png"
          class="item-icon"
          alt
        />
        <img v-else src="@/assets/image/icon.png" class="item-icon" alt />
        <img
          v-if="data.standardNum && data.standardNum.indexOf('3') > -1"
          src="@/assets/image/select-icon.png"
          class="item-icon"
          alt
        />
        <img v-else src="@/assets/image/icon.png" class="item-icon" alt />
      </el-col> -->
      <el-col :span="2">
        <span>{{ data.carLightPer || '' }}</span>
      </el-col>
      <el-col :span="2">
        <span>{{ data.percent || '' }}</span>
      </el-col>
      <el-col :span="1">
        <span>{{ saleEnumAll[data.isOnSale] || data.isOnSale }}</span>
      </el-col>
      <el-col :span="2">
        <el-switch
          :disabled="!!eGoodsId"
          v-model="data.isOnStatus"
          @change="changeSwitch(data)"
        />
      </el-col>
      <el-col :span="2">
        <span>{{ data.imgCount || '0' }}</span>
      </el-col>
      <el-col :class="{ 'point-none': eGoodsId }" :span="4" v-if="!isEdit">
        <el-button
          type="primary"
          link
          size="small"
          @click="editAge(data, 'type')"
          >改年代</el-button
        >
        <el-button
          type="primary"
          link
          size="small"
          @click="configMeasuredData(data)"
          >配置实测数据</el-button
        >
        <el-button type="primary" link size="small" @click="seeLog(data)"
          >操作日志</el-button
        >
        <!-- <el-button type="primary" link size="small" @click="editImg(item)">编辑图片</el-button> -->
        <!-- <el-button type="primary" link size="small" @click="dele(typeList, index, 'type')">删除</el-button> -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'yearShowList',
  components: {},
  props: {
    data: {
      default: {},
      typeof: Object
    },
    isEdit: {
      default: false,
      typeof: Boolean
    },
    saleEnumAll: {
      default: {},
      typeof: Object
    },
    goodsCarName: {
      default: '',
      typeof: String
    },
    carId: {
      default: '',
      typeof: String
    }
  },
  data() {
    return {
      selected: {},
      eGoodsId: ''
    }
  },
  mounted() {
    this.eGoodsId = this.$route.query.eId
  },
  methods: {
    updateCarData() {
      $emit(this, 'updateCarData', this.data)
    },
    editType() {
      $emit(this, 'editCarType', this.data)
    },
    changeSwitch() {
      $emit(this, 'changeSwitch', this.data)
    },
    editAge() {
      $emit(this, 'editCarData', this.data)
    },
    seeLog() {
      $emit(this, 'seeLog', this.data)
    },
    configMeasuredData(data) {
      this.$router.push({
        name: 'ActualConfigDetails',
        query: {
          id: data.id,
          carId: data.carId
        }
      })
    }
  },
  emits: [
    'updateCarData',
    'editCarType',
    'changeSwitch',
    'editCarData',
    'seeLog'
  ]
}
</script>

<style lang="scss">
.goodsCarName {
  white-space: pre-wrap;
  line-height: 15px;
  text-align: left;
}
.year-show-list {
  .item-selected {
    background-color: #add8e6;
  }
}

.year-show-list:hover {
  background-color: #ecf5ff;
}
</style>
