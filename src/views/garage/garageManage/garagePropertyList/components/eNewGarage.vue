/** * 编辑款型 */
<template>
  <div v-loading="loadingGarage" class="c-new-garage point-none">
    <div class="new-garage-header">
      <el-button type="primary" @click="goSaveData()">保存</el-button>
      <el-button class="point-auto" @click="goBack()">取消</el-button>
      <span class="wd-mr-10px" style="margin-left: 20px">款型</span>
      <el-input
        v-model="pullTypeCarId"
        :clearable="true"
        type="number"
        min="0"
        placeholder="请输入款型id"
        style="width: 160px; margin-right: 10px"
      />
      <el-button
        v-if="!isEdit"
        :type="copyGarage === false ? 'success' : 'primary'"
        @click="getDetail(false)"
        >拉取</el-button
      >
      <el-button
        :type="copyGarage ? 'success' : 'primary'"
        @click="getDetail(true)"
        >复制</el-button
      >
      <div
        style="
          display: flex;
          margin-left: 20px;
          margin-right: 50px;
          align-items: center;
        "
      >
        <span style="margin-right: 20px">款型标准化</span>
        <el-checkbox-group v-model="checkList">
          <el-checkbox label="1">外观</el-checkbox>
          <el-checkbox label="2">细节</el-checkbox>
          <el-checkbox label="3">参数</el-checkbox>
        </el-checkbox-group>
      </div>
      <el-checkbox
        v-model="factoryNotProvide"
        :true-label="1"
        :false-label="0"
        @change="isModifyStatus = true"
      >
        厂家暂未提供参数配置</el-checkbox
      >
      <div style="margin-top: 14px">
        <el-form-item label="选择年代款" required label-width="120px">
          <el-select
            placeholder="请选择"
            v-model="ruleForm.carYear"
            filterable
            :disabled="isCarYear"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in yearList"
              :key="index"
              :label="item.carName"
              :value="item.carName"
            />
          </el-select>
        </el-form-item>
      </div>
    </div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      :inline="true"
      label-width="100px"
      class="new-garage-form"
    >
      <div class="modules">
        <el-form-item label="款型名称" required label-width="80px">
          <el-input
            v-model="ruleForm.goodsCarNameNew"
            :clearable="true"
            style="width: 240px"
            placeholder="请输入款型名称"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="参考价"
          prop="goodsPrice"
          label-width="90px"
        >
          <el-input
            v-model="ruleForm.goodsPrice"
            placeholder="请输入参考价格"
            type="text"
            style="width: 140px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox
            v-model="ruleForm.isOnStatus"
            @change="isModifyStatus = true"
            >是否上架</el-checkbox
          >
        </el-form-item>
        <el-form-item
          class="lable-red"
          v-if="isAddType"
          label="车型名称"
          required
          label-width="80px"
        >
          <el-select
            v-model="ruleForm.goodsName"
            :filter-method="remoteMethod"
            :loading="false"
            placeholder="请输入车型名称"
            filterable
            remote
            clearable
            @change="addBrand"
          >
            <el-option
              v-for="(item, index) in goodsList"
              :key="index"
              :label="item.labelName"
              :value="item.labelName"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="isAddType"
          label="年代款"
          required
          label-width="80px"
          placeholder="请选择年代款"
        >
          <el-select v-model="ruleForm.carName">
            <el-option
              v-for="(item, index) in yearList"
              :key="index"
              :label="item.carName"
              :value="item.carName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="在售状态" required label-width="80px">
          <el-select
            v-model="ruleForm.isOnSale"
            style="width: 150px"
            @change="changeisOnSale"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, index) in isOnSaleList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="行驶证名称" label-width="100px">
          <el-input
            v-model="ruleForm.drivingLicenseName"
            :clearable="true"
            style="width: 240px"
            @change="isModifyStatus = true"
            placeholder="请输入行驶证名称"
          />
        </el-form-item>
        <el-form-item label="活动价" label-width="100px">
          <el-input
            v-model="ruleForm.discount"
            :clearable="true"
            style="width: 240px"
            placeholder="请输入活动价"
          />
        </el-form-item>
        <el-form-item v-if="ruleForm.isOnSale === 1" label="">
          <el-checkbox
            :true-label="1"
            :false-label="0"
            v-model="ruleForm.newCarOnsaleLable"
            >新车上市</el-checkbox
          >
          <span
            v-if="
              ruleForm.newCarOnsaleLable === 1 && ruleForm.newCarOnsaleEndTime
            "
            style="margin-left: 10px; color: red"
            >截止时间：
            {{
              $dayjs(ruleForm.newCarOnsaleEndTime).format('YYYY年MM月DD日')
            }}</span
          >
        </el-form-item>
      </div>
      <div class="modules" style="margin-bottom: 10px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="款型展图" required label-width="80px">
              <el-upload
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="updategoodsThumb"
                name="upfile"
                class="avatar-uploader"
                action
              >
                <img
                  v-if="ruleForm.goodsThumb"
                  :src="ruleForm.goodsThumb"
                  alt
                  style="display: inline-block; height: 100px"
                />
                <img v-else src="@/assets/image/<EMAIL>" alt />
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="视频首图" label-width="80px">
              <div
                v-if="showVideoList && showVideoList.length"
                class="video-content"
              >
                <div
                  v-for="(video, index) in showVideoList"
                  :key="index"
                  class="video-show-content"
                >
                  <template v-if="index === 0">
                    <img
                      v-if="video.imgOrgUrl"
                      :src="video.imgOrgUrl"
                      alt
                      class="video-img"
                    />
                    <img
                      v-else
                      src="@/assets/image/video-default.jpg"
                      alt
                      class="video-img"
                    />
                    <img
                      class="icon play-icon"
                      src="@/assets/image/<EMAIL>"
                      alt
                      @click="seeVideoPlay(video)"
                    />
                  </template>
                </div>
              </div>
              <div v-else class="video-content" @click="initVideo()">
                暂无视频
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="使用说明书" label-width="85px">
              <div v-if="manualName" class="video-content">
                <div>{{ manualName }}</div>
                <div>
                  <el-button
                    type="primary"
                    size="small"
                    circle
                    @click="handlePictureCardPreview"
                  >
                    <template #icon>
                      <IconZoomIn />
                    </template>
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="removeFile"
                    ><template #icon> <IconDelete /> </template
                  ></el-button>
                </div>
              </div>
              <div v-else class="video-content">暂无说明书</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="modules">
        <div style="display: flex; justify-content: space-between">
          <el-button
            type="primary"
            class="point-auto"
            style="margin: 10px"
            @click="initImage()"
            >款型图片</el-button
          >
          <el-button
            type="primary"
            class="point-auto"
            style="margin: 10px"
            @click="initDesign()"
            >款型亮点</el-button
          >
          <el-button
            type="primary"
            class="point-auto"
            style="margin: 10px"
            @click="initVideo()"
            >视频说明书</el-button
          >
          <el-form-item>
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest1"
              :on-success="onSuccess1"
              accept=".pdf"
              name="upfile"
              action
              style="width: 150px"
            >
              <el-button type="primary">使用说明书</el-button>
            </el-upload>
          </el-form-item>
        </div>
      </div>
      <div class="title modules">参数配置</div>
      <div class="modules">
        <p class="modules-name">基础参数</p>
        <el-form-item
          class="lable-red"
          label="车辆类型"
          required
          label-width="80px"
        >
          <el-checkbox-group
            v-model="checkedCategory"
            @change="handleCheckedCategoryChange"
          >
            <div class="wd-flex wd-h-120px">
              <div
                v-for="carCategory in carCategoryList"
                :key="carCategory"
                style="margin-right: 5px"
              >
                <el-checkbox
                  :disabled="['三轮', '踏板'].includes(carCategory)"
                  :class="[
                    ['三轮', '踏板'].includes(carCategory)
                      ? 'custom-disabled_style'
                      : ''
                  ]"
                  :label="carCategory"
                  :key="carCategory"
                  >{{ carCategory }}</el-checkbox
                >
                <div class="wd-absolute">
                  <el-checkbox-group
                    v-if="carCategory === '三轮'"
                    v-model="checkedWheel"
                    @change="handleCheckedWheelChange"
                  >
                    <div class="wd-flex wd-flex-col">
                      <el-checkbox
                        v-for="(child, index) in childWheelOptions"
                        :key="index"
                        :label="child"
                        >{{ child }}</el-checkbox
                      >
                    </div>
                  </el-checkbox-group>
                </div>
                <div class="wd-absolute">
                  <el-checkbox-group
                    v-if="carCategory === '踏板'"
                    v-model="checkedPedal"
                    @change="handlecheckedPedalChange"
                  >
                    <div class="wd-flex wd-flex-col">
                      <el-checkbox
                        v-for="(child, index) in childPedalOptions"
                        :key="index"
                        :label="child"
                        >{{ child }}</el-checkbox
                      >
                    </div>
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </el-checkbox-group> </el-form-item
        ><br />
        <el-form-item label="车辆属性">
          <el-select
            placeholder="请选择"
            v-model="ruleForm.newEnergyTypes"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in energyType"
              :key="index"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="驾照政策">
          <el-select
            placeholder="请选择"
            v-model="ruleForm.needDriversLicense"
            clearable
            @change="
              changeSwitchOptions(
                'needDriversLicense',
                'driversType',
                '无需驾照'
              )
            "
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in needDriversLicenseType"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-select
            v-if="ruleForm.needDriversLicense === '需要'"
            placeholder="请选择"
            v-model="ruleForm.driversType"
            clearable
            style="width: 150px; margin-left: 10px"
          >
            <el-option
              v-for="(item, index) in driversTypeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="厂家" prop="factoryName" label-width="50px">
          <el-input
            v-model="ruleForm.factoryName"
            placeholder="请填写厂家名称"
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="品牌" prop="brand" label-width="50px">
          <el-input
            v-model="ruleForm.brandName"
            disabled
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="生产方式"
          required
          label-width="80px"
          placeholder="请选择"
        >
          <el-select
            v-model="ruleForm.goodProductType"
            style="width: 150px"
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(value, index) in produceWayList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="电池规格" label-width="90px">
          <el-input
            v-model="ruleForm.goodBatterySpecification"
            placeholder="请填写"
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="官方续航里程(km)" label-width="140px">
          <span>工况续航</span>
          <el-input
            v-model="ruleForm.goodEndurance"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          >
            <template #append>km</template>
          </el-input>
          <span>等速续航</span>
          <el-input
            v-model="ruleForm.goodConstantSpeedEndurance"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
            style="width: 150px; margin-left: 4px"
          >
            <template #append>km</template>
          </el-input>
        </el-form-item>
        <el-form-item label="重要部件质保">
          <span>动力电池质保</span>
          <el-input
            v-model="ruleForm.batteryWarrantyMonth"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          >
          </el-input>
          <span>车架质保</span>
          <el-input
            v-model="ruleForm.frameWarrantyMonth"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          >
          </el-input>
          <span>电机质保</span>
          <el-input
            v-model="ruleForm.motorWarrantyMonth"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          >
          </el-input>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">电动机</p>
        <el-form-item label="电机型号" label-width="85px">
          <el-input
            v-model="ruleForm.goodMotorBrand"
            @change="isModifyStatus = true"
            placeholder="请填写"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="电机布局" label-width="85px">
          <el-select
            v-model="ruleForm.goodMotorLayout"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, index) in goodMotorLayoutType"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="电动机冷却方式"
          prop="goodElectricCoolDown"
          label-width="150px"
        >
          <el-select
            v-model="ruleForm.goodElectricCoolDown"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in goodElectricCoolDownList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="电机额定功率(kW)" label-width="140px">
          <el-input
            v-model="ruleForm.goodMotorPower"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          >
            <template #append>KW</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="电机最大马力(ps)"
          prop="goodMotorMaxHorse"
          label-width="125px"
        >
          <el-input
            v-model="goodMotorMaxHorse"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
            disabled
          />
        </el-form-item>
        <el-form-item label="电机最大(峰值)功率(kW)" label-width="170px">
          <el-input
            v-model="ruleForm.goodMotorMaxPower"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          >
            <template #append>KW</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="电机最大功率转速(rpm)"
          prop="goodMotorMaxPowerSpeed"
          label-width="180px"
        >
          <el-input
            v-model="ruleForm.goodMotorMaxPowerSpeed"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item
          label="电机最大扭矩(N·m)"
          prop="goodMotorAcrotOrque"
          label-width="135px"
        >
          <el-input
            v-model="ruleForm.goodMotorAcrotOrque"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item
          label="电机最大扭矩转速(rpm)"
          prop="goodMotorMaxTorqueSpeed"
          label-width="160px"
        >
          <el-input
            v-model="ruleForm.goodMotorMaxTorqueSpeed"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="控制器" prop="goodMotorControl" label-width="60px">
          <el-input
            v-model="ruleForm.goodMotorControl"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="控制器冷却方式" label-width="120px">
          <el-select
            v-model="ruleForm.goodMotorControlMode"
            clearable
            style="width: 150px"
            placeholder="请选择"
            @change="
              changeSwitchOptions(
                'goodMotorControlMode',
                'goodMotorControlType',
                '水冷'
              )
            "
          >
            <el-option
              v-for="(value, index) in goodMotorControlModeList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
          <el-select
            v-if="ruleForm.goodMotorControlMode === '风冷'"
            v-model="ruleForm.goodMotorControlType"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, index) in goodMotorControlTypeList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="最高车速(km/h)"
          prop="goodMaxSpeed"
          label-width="110px"
        >
          <el-input
            v-model="ruleForm.goodMaxSpeed"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <!-- <el-form-item
          label="官方百公里电耗(kW·h/km)"
          prop="goodKilometersElectricity"
          label-width="190px"
        >
          <el-input
            v-model="ruleForm.goodKilometersElectricity"
            @change="isModifyStatus = true"
            placeholder="请输入"
          />
        </el-form-item> -->
        <el-form-item label="官方0-50km/h加速(s)" label-width="160px">
          <el-input
            v-model="ruleForm.officialRealGoodBreak50Metre"
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请输入"
          >
            <template #append>秒</template>
          </el-input>
        </el-form-item>
        <el-form-item label="实测0-50km/h加速(s)" label-width="160px">
          <el-input
            v-model="ruleForm.realGoodBreak50Metre"
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请输入"
            disabled
          />
          <span style="margin-left: 10px; color: #999">同步实测数据</span>
        </el-form-item>
        <el-form-item label="实测百公里电耗(kW·h/km)" label-width="190px">
          <el-input
            v-model="ruleForm.realGoodKilometersElectricity"
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请输入"
            disabled
          />
          <span style="margin-left: 10px; color: #999">同步实测数据</span>
        </el-form-item>
        <!-- <el-form-item label="电池重量(kg)" prop="goodBatteryWeight">
          <el-input
            v-model="ruleForm.goodBatteryWeight"
            @change="isModifyStatus = true"
          />
        </el-form-item> -->
        <!-- <el-form-item
          label="标准充电电流(A)"
          prop="goodStandardCharging"
          label-width="120px"
        >
          <el-input
            v-model="ruleForm.goodStandardCharging"
            @change="isModifyStatus = true"
          />
        </el-form-item> -->
        <!-- <el-form-item label="循环充电次数" prop="goodCycleCharge">
          <el-input
            v-model="ruleForm.goodCycleCharge"
            @change="isModifyStatus = true"
          />
        </el-form-item> -->
      </div>
      <div class="modules">
        <p class="modules-name">电池/充电</p>
        <el-form-item
          label="电池类型"
          prop="goodBatteryType"
          class="lable-border"
          label-width="85px"
        >
          <el-select
            v-model="ruleForm.goodBatteryType"
            clearable
            @change="isModifyStatus = true"
            @clear="ruleForm.goodBatteryType = ''"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in batteryList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="电芯类型"
          prop="goodBatteriesType"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodBatteriesType"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>

        <el-form-item label="额定电压(V)" prop="goodVoltage">
          <el-input
            v-model="ruleForm.goodVoltage"
            placeholder="示例：12, 12.5"
            style="width: 180px"
          >
            <template #append>V</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="电池额定容量(Ah)"
          prop="goodBatteryCapacity"
          label-width="130px"
        >
          <el-input
            v-model="ruleForm.goodBatteryCapacity"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="电池能量(kWh/度)" label-width="130px">
          <el-tooltip
            content="【电压】*【容量】/1000 = 电池能量"
            effect="dark"
            placement="top-start"
          >
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
          <el-input
            v-model="ruleForm.goodBatteryEnergy"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px; margin-left: 10px"
          />
        </el-form-item>
        <el-form-item label="电池组数量" label-width="130px">
          <el-input
            v-model="ruleForm.goodBatteryNum"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="快拆电池组" label-width="120px">
          <el-select
            v-model="ruleForm.goodBatteryTearOpen"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodBatteryTearOpenList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="充电时间(h)">
          <el-input
            v-model="ruleForm.goodChargingTime"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="快充时间(h)">
          <el-input
            v-model="ruleForm.goodQuickCharge"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="充放电循环次数" label-width="120px">
          <el-input
            v-model="ruleForm.goodCycleCharge"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="充电器功能">
          <el-select
            v-model="ruleForm.goodChargerFunction"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            multiple
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodChargerFunctionList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="BMS电池管理系统" label-width="130px">
          <el-select
            v-model="ruleForm.goodBms"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="可兼容充电" label-width="120px">
          <el-select
            v-model="ruleForm.goodBatteryCompatible"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in goodBatteryCompatibleList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="充放电耐温范围(℃)"
          prop="goodTemperatureRange"
          label-width="140px"
        >
          <div class="flex">
            <el-input
              v-model="ruleForm.goodTemperatureRange"
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            />~
            <el-input
              v-model="ruleForm.goodTemperatureRangeRight"
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            />
          </div>
        </el-form-item>
        <el-form-item label="车辆充电口" label-width="100px">
          <div class="flex">
            <el-input
              v-model="ruleForm.goodBatteryChargingPort"
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            />
          </div>
        </el-form-item>
        <el-form-item label="电池自加热系统" label-width="130px">
          <el-select
            v-model="ruleForm.goodBatterySelfHeating"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in goodBatterySelfHeatingList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="外放电功能" label-width="130px">
          <el-select
            v-model="ruleForm.goodExternalDischarge"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in goodBatterySelfHeatingList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">悬挂系统</p>
        <el-form-item label="前悬挂系统" style="width: 100%">
          <span style="margin-right: 10px">结构类型</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodFrontSuspensionType"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionTypeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 10px">前悬挂气囊</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodFrontSuspensionAirBag"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionAirBagList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 20px">品牌型号</span>
          <el-input
            placeholder="请输入"
            v-model="ruleForm.goodFrontSuspensionBrand"
            style="width: 150px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="前悬挂调节" style="width: 100%">
          <span style="margin-right: 10px">调节方式：预载</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodFrontSuspensionMode"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 10px">压缩阻尼</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodFrontSuspensionCompress"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 10px">回弹阻尼</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodFrontSuspensionResilience"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="前悬挂直径/行程"
          label-width="120px"
          style="width: 100%"
        >
          <span style="margin-right: 10px">减震器内管直径 φ </span>
          <el-input
            v-model="ruleForm.goodFrontSuspensionDiameter"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          >
            <template #append>mm</template>
          </el-input>
          <span style="margin: 0 10px">前悬挂行程 行程</span>
          <el-input
            v-model="ruleForm.goodFrontSuspensionTrip"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          >
            <template #append>mm</template>
          </el-input>
        </el-form-item>

        <el-form-item label="后悬挂系统" style="width: 100%">
          <span style="margin-right: 10px">结构类型 中置式</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodBackSuspensionType"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodBackSuspensionTypeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 10px">边置式</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodBackSuspensionSide"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodBackSuspensionSideList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 10px">后悬挂气囊</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodBackSuspensionAirBag"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionAirBagList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 20px">品牌型号</span>
          <el-input
            placeholder="请输入"
            v-model="ruleForm.goodBackSuspensionBrand"
            style="width: 150px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="后悬挂调节" style="width: 100%">
          <span style="margin-right: 10px">调节方式：预载</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodBackSuspensionMode"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 10px">压缩阻尼</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodBackSuspensionCompress"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <span style="margin: 0 10px">回弹阻尼</span>
          <el-select
            placeholder="请选择"
            v-model="ruleForm.goodBackSuspensionResilience"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodFrontSuspensionModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="后悬挂行程 行程" label-width="120px">
          <el-input
            v-model="ruleForm.goodBackSuspensionTrip"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          >
            <template #append>mm</template>
          </el-input>
        </el-form-item>
        <el-form-item label="后摇臂" label-width="60px">
          <el-input
            v-model="ruleForm.goodRockerArm"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="电子悬挂">
          <el-select
            v-model="ruleForm.dynamicESA"
            clearable
            @change="
              changeSwitchOptions('dynamicESA', 'dynamicESASwitch', '无')
            "
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.dynamicESA)"
            v-model="ruleForm.dynamicESASwitch"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in dynamicESAswitchList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">动力传输</p>
        <el-form-item label="驾驶模式" class="lable-border">
          <el-input
            v-model="ruleForm.goodDrivingMode"
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="变速器型式" prop="goodSpeedMode">
          <el-cascader
            v-model="goodSpeedMode"
            clearable
            @change="isModifyStatus = true"
            :options="goodSpeedModeList"
            :show-all-levels="false"
            placeholder="请选择"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="滑动离合器" prop="goodClutchModeStandard">
          <el-select
            v-model="ruleForm.goodClutchModeStandard"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in goodClutchModeStandardList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="lable-red" label="离合器" label-width="60px">
          <el-select
            v-model="ruleForm.goodClutchMode"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in goodClutchModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="lable-red" label="传动方式" label-width="80px">
          <el-select
            v-model="ruleForm.goodDriveType"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in goodDriveTypeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="动力档位" class="lable-border">
          <el-input
            v-model="ruleForm.goodDriveGear"
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请输入"
          />
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">车体参数</p>
        <el-form-item
          label="长x宽x高(mm)"
          prop="goodSize"
          class="lable-border"
          label-width="110px"
        >
          <el-input
            v-model="ruleForm.long"
            style="width: 100px"
            @change="isModifyStatus = true"
            placeholder="请输入"
          /><span>x</span>
          <el-input
            v-model="ruleForm.width"
            style="width: 100px"
            @change="isModifyStatus = true"
            placeholder="请输入"
          /><span>x</span>
          <el-input
            v-model="ruleForm.high"
            style="width: 100px"
            @change="isModifyStatus = true"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="座高(mm)">
          <el-input
            v-model="ruleForm.goodSaddleHigh"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item
          label="轴距(mm)"
          prop="goodWheelBase"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodWheelBase"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item
          label="座垫长度(mm)"
          prop="goodSeatCushionLength"
          class="lable-border"
          label-width="110px"
        >
          <el-input
            v-model="ruleForm.goodSeatCushionLength"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item
          label="额定负载(kg)"
          prop="goodRatedLoad"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodRatedLoad"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>

        <el-form-item label="车架型式" class="lable-border">
          <el-input
            v-model="ruleForm.goodFrame"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="干重(kg)">
          <el-input
            v-model="ruleForm.goodEmptyWeight"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="整备质量(kg)">
          <el-input
            v-model="ruleForm.goodAllWeight"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <el-form-item label="前倾角度(°)">
          <el-input
            v-model="ruleForm.goodForwardAngle"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="拖曳距(mm)">
          <el-input
            v-model="ruleForm.goodDragDistance"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="最小离地间隙(mm)" label-width="135px">
          <el-input
            v-model="ruleForm.goodClearance"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item
          label="最小转弯半径(m)"
          prop="goodMinTurn"
          label-width="120px"
        >
          <el-input
            v-model="ruleForm.goodMinTurn"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item
          label="最大有效载荷(kg)"
          prop="goodMaxLoad"
          label-width="130px"
        >
          <el-input
            v-model="ruleForm.goodMaxLoad"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <!-- <el-form-item label="仪表盘">
          <el-input
            v-model="ruleForm.goodDashboard"
            clearable
            @change="isModifyStatus = true"
          />
        </el-form-item> -->
        <el-form-item label="可选颜色" prop="colorExps">
          <el-tooltip
            v-for="(item, index) in colorExpsList"
            :key="index"
            :content="item.name"
            :disabled="true"
            effect="dark"
            placement="top-start"
            style="margin: 5px"
          >
            <el-tag>{{ $filters.subString(item.name, 20) }}</el-tag>
          </el-tooltip>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">车轮制动</p>
        <el-form-item label="轮胎品牌" class="lable-border">
          <el-input
            v-model="ruleForm.brandModel"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="前轮规格">
          <el-input
            v-model="ruleForm.goodFrontWheel"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="后轮规格">
          <el-input
            v-model="ruleForm.goodBackWheel"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="轮胎形式">
          <el-select
            v-model="ruleForm.goodTyreType"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in goodTyreTypeList"
              :key="index"
              :label="value"
              :value="value"
              @click="ruleForm.goodTyreType = value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="轮辋" label-width="50px">
          <el-select
            v-model="ruleForm.goodWheelRim"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, index) in goodWheelRimList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="前制动系统" class="lable-border">
          <el-input
            v-model="ruleForm.goodFrontBrake"
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="后制动系统" class="lable-border">
          <el-input
            v-model="ruleForm.goodRearBrake"
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          />
        </el-form-item>
      </div>
      <!-- 主/被动安全配置 -->
      <div class="modules">
        <p class="modules-name">主/被动安全配置</p>
        <el-form-item class="lable-red" label="ABS防抱死" label-width="120px">
          <el-select
            v-model="ruleForm.goodAbs"
            clearable
            @change="changeSwitchOptions('goodAbs', 'goodAbsStandard', '无')"
            style="width: 150px"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, index) in goodNewAbsConfigurationList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
          <el-select
            v-if="ruleForm.goodAbs === '标配'"
            v-model="ruleForm.goodAbsStandard"
            clearable
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, index) in goodNewAbsTypeList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="TCS牵引力控制" label-width="130px">
          <el-select
            v-model="ruleForm.goodTCS"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in commonModelList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="SOS紧急呼叫" label-width="130px">
          <el-select
            v-model="ruleForm.goodSOS"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in commonModelList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="CBS联动刹车" label-width="130px">
          <el-select
            v-model="ruleForm.goodCBS"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in goodAbsList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="弯道ABS(ABS PRO/ABS EVO等)" label-width="250px">
          <el-select
            v-model="ruleForm.curveABS"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="胎压监测">
          <el-select
            v-model="ruleForm.tirePressureMonitoring"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="牵引力控制" label-width="120px">
          <el-select
            v-model="ruleForm.tractionControlSystem"
            clearable
            @change="
              changeSwitchOptions(
                'tractionControlSystem',
                'tractionControlSystemSwitch',
                '无'
              )
            "
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.tractionControlSystem)"
            v-model="ruleForm.tractionControlSystemSwitch"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in switchList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="转向阻尼器" label-width="120px">
          <el-select
            v-model="ruleForm.steeringControlSystem"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="动能回收">
          <el-select
            v-model="ruleForm.kineticEnergyRecovery"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.kineticEnergyRecoveryRegulation = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in kineticEnergyRecoveryList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.kineticEnergyRecovery = item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.kineticEnergyRecovery)"
            v-model="ruleForm.kineticEnergyRecoveryRegulation"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in kineticEnergyRecoveryRegulationList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.kineticEnergyRecoveryRegulation = item"
            />
          </el-select>
        </el-form-item> -->
      </div>
      <!-- 辅助/操控配置 -->

      <div class="modules">
        <p class="modules-name">辅助/操控配置</p>

        <!-- <el-form-item label="模式选择">
          <search-label
            :type="'label'"
            class="search-label-content"
            @addLabel="addLabel"
          />
          <label-content
            ref="label"
            :labels="labels"
            :type="'label'"
            @deleteLabel="deleteLabel"
            @labelAllData="updataLabelAllData"
            @delLabel="delLabel"
          />
        </el-form-item> -->
        <el-form-item label="主动安全预警系统" label-width="130px">
          <el-select
            v-model="ruleForm.activeSafetyWarningSystem"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            multiple
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in activeSafetyWarningSystemList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="毫米波雷达">
          <el-select
            v-model="ruleForm.millimeterWaveRadar"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.millimeterWaveRadarDirectional = ''
                ruleForm.millimeterWaveRadarConfiguration = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.millimeterWaveRadar = item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.millimeterWaveRadar)"
            v-model="ruleForm.millimeterWaveRadarDirectional"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.millimeterWaveRadarConfiguration = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarDirectionalList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.millimeterWaveRadarDirectional = item"
            />
          </el-select>
          <!-- <el-select
            v-if="ruleForm.millimeterWaveRadarDirectional === '前向'"
            v-model="ruleForm.millimeterWaveRadarConfiguration"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarConfigurationPreList"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.millimeterWaveRadarConfiguration = item
                }
              "
            />
          </el-select>
          <el-select
            v-if="ruleForm.millimeterWaveRadarDirectional === '后向'"
            v-model="ruleForm.millimeterWaveRadarConfiguration"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarConfigurationAftList"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.millimeterWaveRadarConfiguration = item
                }
              "
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="ABS关闭功能" label-width="130px">
          <el-select
            v-model="ruleForm.aBSOnOff"
            clearable
            @change="changeSwitchOptions('aBSOnOff', 'aBSOnOffSwitch', '无')"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.aBSOnOff)"
            v-model="ruleForm.aBSOnOffSwitch"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in aBSOnOffSwitchList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="HHC坡道驻车">
          <el-select
            v-model="ruleForm.hillStartAssistControl"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="OTD单转把模式" label-width="120px">
          <el-select
            v-model="ruleForm.singleHandleMode"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="巡航系统">
          <el-select
            v-model="ruleForm.cruiseSystem"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            multiple
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in cruiseSystemList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="倒车辅助">
          <el-select
            v-model="ruleForm.reverseAssist"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="助力推行">
          <el-select
            v-model="ruleForm.assistedPushing"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="边撑感应">
          <el-select
            v-model="ruleForm.edgeSupportSensing"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="能量回收">
          <el-select
            v-model="ruleForm.energyRecovery"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.energyRecoveryGear = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.energyRecovery)"
            v-model="ruleForm.energyRecoveryGear"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in energyRecoveryGearList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="转向灯自动回正" label-width="130px">
          <el-select
            v-model="ruleForm.turnSignalAutoCorrection"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="油门配置">
          <el-select
            v-model="ruleForm.electronicThrottle"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in electronicThrottleList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.electronicThrottle = item"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="定速巡航">
          <el-select
            v-model="ruleForm.cruiseControl"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item> -->

        <!-- <el-form-item label="快速换挡系统" label-width="100px">
          <el-select
            v-model="ruleForm.quickShiftSystem"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.quickShiftSystem = item"
            />
          </el-select>
          <el-select
            v-model="ruleForm.quickShiftSystemtype"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in quickShiftSystemList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.quickShiftSystemtype = item"
            />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="动力模式">
          <el-select
            v-model="ruleForm.powerMode"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.powerModeGear = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in powerModeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.powerMode = item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.powerMode)"
            v-model="ruleForm.powerModeGear"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in powerModeGearList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.powerModeGear = item"
            />
          </el-select>
        </el-form-item> -->
      </div>
      <div class="modules">
        <p class="modules-name">智能化配置</p>
        <el-form-item label="仪表屏幕" style="width: 100%">
          <el-form-item label="尺寸">
            <el-input
              v-model="ruleForm.dashboardSize"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="亮度">
            <el-input
              v-model="ruleForm.dashboardBrightness"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="材质">
            <el-input
              v-model="ruleForm.dashboardMaterial"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item label="智能驾驶系统">
          <el-input
            v-model="ruleForm.intelligentDrivingSystem"
            clearable
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="手机连接功能(蓝牙/WIFI等)" label-width="190px">
          <el-select
            v-model="ruleForm.electronicConnection"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手机互联映射" label-width="190px">
          <el-select
            v-model="ruleForm.phoneMapping"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="语音控制">
          <el-select
            v-model="ruleForm.voiceControl"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="面部识别">
          <el-select
            v-model="ruleForm.faceRecognition"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="智能APP">
          <el-select
            v-model="ruleForm.smartApp"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            multiple
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in smartAppList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="OTA升级">
          <el-select
            v-model="ruleForm.dynamicOTA"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </div>

      <div class="modules">
        <p class="modules-name">影音娱乐</p>
        <el-form-item label="扬声器">
          <el-input
            v-model="ruleForm.speakers"
            clearable
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">舒适/防盗配置</p>
        <el-form-item label="挡位显示" class="lable-border">
          <el-select
            v-model="ruleForm.goodDriveGearShow"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in commonModelList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="电加热手把">
          <el-select
            v-model="ruleForm.electricHeatingHandle"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="电加热座垫">
          <el-select
            v-model="ruleForm.electricHeatingSeatCushion"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="风挡" label-width="100px">
          <el-select
            v-model="ruleForm.windshieldModel"
            clearable
            @change="
              () => {
                ruleForm.windshieldType = ''
                ruleForm.windshieldChecke = ''
                isModifyStatus = true
              }
            "
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.windshieldModel)"
            v-model="ruleForm.windshieldType"
            clearable
            @change="
              () => {
                ruleForm.windshieldChecke = ''
                isModifyStatus = true
              }
            "
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in windshieldTypeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.windshieldType = item"
            />
          </el-select>
          <el-select
            v-if="
              ruleForm.windshieldType && ruleForm.windshieldType !== '不可调节'
            "
            v-model="ruleForm.windshieldChecke"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 200px"
          >
            <el-option
              v-for="(item, index) in windshieldCheckeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.windshieldChecke = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="无钥匙解锁">
          <el-select
            v-model="ruleForm.keylessUnlock"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            multiple
            style="width: 200px"
          >
            <el-option
              v-for="(item, index) in keylessUnlockList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="防盗">
          <el-select
            v-model="ruleForm.antiTheft"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            multiple
            style="width: 200px"
          >
            <el-option
              v-for="(item, index) in antiTheftList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="USB/Type-C接口"
          label-width="140px"
          style="width: 100%"
        >
          <el-form-item label="USB">
            <el-input
              v-model="ruleForm.usbCount"
              clearable
              type="number"
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            >
              <template #append>个</template>
            </el-input>
          </el-form-item>
          <el-form-item label="Type-C">
            <el-input
              v-model="ruleForm.typeCCount"
              clearable
              type="number"
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            >
              <template #append>个</template>
            </el-input>
          </el-form-item>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">储物空间</p>
        <el-form-item label="座桶容积" style="width: 100%">
          <el-form-item label="容积">
            <el-input
              v-model="ruleForm.bucketVolume"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            >
              <template #append>L</template>
            </el-input>
          </el-form-item>
          <el-form-item label="尺寸">
            <el-input
              v-model="ruleForm.bucketSize"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item label="踏板尺寸">
          <el-input
            v-model="ruleForm.pedalSize"
            clearable
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="前置物篮">
          <el-input
            v-model="ruleForm.frontBasket"
            clearable
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="前储物格">
          <el-input
            v-model="ruleForm.frontStorage"
            clearable
            @change="isModifyStatus = true"
            placeholder="请输入"
            type="number"
            style="width: 150px"
          >
            <template #append>个</template>
          </el-input>
        </el-form-item>
      </div>
      <!-- 灯光配置 -->
      <div class="modules">
        <p class="modules-name">灯光配置</p>
        <el-form-item label="前灯: " prop="goodHeadlights" style="width: 100%">
          <el-form-item label="类型">
            <el-select
              v-model="ruleForm.goodHeadlights"
              clearable
              @change="isModifyStatus = true"
              placeholder="请选择"
              style="width: 150px"
            >
              <el-option
                v-for="(item, index) in lightsList.concat(['氙气'])"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="光照距离长度">
            <el-input
              v-model="ruleForm.goodHeadlightsLength"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            >
              <template #append>米</template>
            </el-input>
          </el-form-item>
          <el-form-item label="宽度">
            <el-input
              v-model="ruleForm.goodHeadlightsWidth"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            >
              <template #append>米</template>
            </el-input>
          </el-form-item>

          <el-form-item label="光照强度近光">
            <el-input
              v-model="ruleForm.goodHeadlightsNear"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            >
              <template #append>cd</template>
            </el-input>
          </el-form-item>
          <el-form-item label="远光">
            <el-input
              v-model="ruleForm.goodHeadlightsFar"
              clearable
              @change="isModifyStatus = true"
              placeholder="请输入"
              style="width: 150px"
            >
              <template #append>cd</template>
            </el-input>
          </el-form-item>
        </el-form-item>
        <el-form-item label="氛围灯">
          <el-select
            v-model="ruleForm.goodAmbientlight"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.goodAmbientlightStandard = ''
              }
            "
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in commonModelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-input
            v-if="['标配', '选配'].includes(ruleForm.goodAmbientlight)"
            v-model="ruleForm.goodAmbientlightStandard"
            clearable
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="后灯" prop="goodTaillight">
          <el-select
            v-model="ruleForm.goodTaillight"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in lightsList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="转向灯" prop="goodTurnlight">
          <el-select
            v-model="ruleForm.goodTurnlight"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in lightsList.slice(0, 2)"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="辅助灯" prop="goodAssistlight">
          <el-select
            v-model="ruleForm.goodAssistlight"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="危险警示灯(双闪)"
          prop="goodWarninglight"
          label-width="130px"
        >
          <el-select
            v-model="ruleForm.goodWarninglight"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="自动大灯">
          <el-select
            v-model="ruleForm.automaticHeadlamp"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- 选装包 -->
      <div class="modules">
        <p class="modules-name">选装包</p>
        <el-form-item label="选装包">
          <!-- <search-label
            :type="'label'"
            class="search-label-content"
            @addLabel="addLabelPack"
          />
          <label-content
            ref="labelsPack"
            :labels="labelsPack"
            :type="'label'"
            @deleteLabel="deleteLabelPack"
            @labelAllData="updataLabelAllDataPack"
            @delLabel="delLabelPack"
          /> -->
          <el-button
            type="primary"
            size="small"
            @click="setTagConfig('标配', TagType.PACKAGE)"
            >+ 标配</el-button
          >
          <el-button
            type="primary"
            size="small"
            @click="setTagConfig('选配', TagType.PACKAGE)"
            >+ 选配</el-button
          >
          <el-tag
            class="ml5"
            v-for="(label, index) in labelsPack"
            :key="label.labelName"
            closable
            round
            effect="dark"
            :type="label.labelType == '标配' ? 'success' : 'warning'"
            @close="deleteLabelPack(index)"
          >
            {{ label.labelType }} {{ label.labelName }}
          </el-tag>
        </el-form-item>
      </div>
      <!-- 其他配置 -->
      <div class="modules">
        <p class="modules-name">其他配置</p>
        <el-form-item label="上市时间" prop="goodTime" class="lable-border">
          <el-date-picker
            v-model="ruleForm.goodTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择日期时间"
            @change="isModifyStatus = true"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="产地" label-width="50px">
          <el-select
            v-model="ruleForm.productPlace"
            clearable
            @change="isModifyStatus = true"
            @clear="ruleForm.productPlace = ''"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in productPlaceList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="生产状态" class="lable-border">
          <el-select
            v-model="ruleForm.goodSale"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="(item, index) in goodSaleList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置/选项">
          <!-- <search-label
            :type="'label'"
            class="search-label-content"
            @addLabel="addLabelConfig"
          />
          <label-content
            ref="labelsConfig"
            :labels="labelsConfig"
            :type="'label'"
            @deleteLabel="deleteLabelConfig"
            @labelAllData="updataLabelAllDataConfig"
            @delLabel="delLabelConfig"
          /> -->
          <el-button
            type="primary"
            size="small"
            @click="setTagConfig('标配', TagType.CONfIG)"
            >+ 标配</el-button
          >
          <el-button
            type="primary"
            size="small"
            @click="setTagConfig('选配', TagType.CONfIG)"
            >+ 选配</el-button
          >
          <el-tag
            class="ml5"
            v-for="(label, index) in labelsConfig"
            :key="label.labelName"
            closable
            round
            effect="dark"
            :type="label.labelType == '标配' ? 'success' : 'warning'"
            @close="deleteLabelConfig(index)"
          >
            {{ label.labelType }} {{ label.labelName }}
          </el-tag>
        </el-form-item>
      </div>
    </el-form>
    <image-dialog
      ref="ImageDialog"
      :more-fun-status="true"
      @sendData="getImageData"
      @refreshImage="refreshImage"
    />
    <video-dialog ref="VideoDialog" @sendData="getVideoData" />
    <!-- <choose-dialog ref="ChooseDialog" @sendData="getColorData" /> -->
    <choose-video ref="ChooseVideo" />

    <RealData ref="RealData" @sendData="getRealData"></RealData>
    <SeeLog ref="seeLog" @success="postReadyData" />
    <SelectTag
      v-model="selectTagVisiable"
      :title="`标签选择(${tagModelType})`"
      :tagType="selectTagtype"
      @updateData="updateDataConfig"
    />
  </div>
</template>

<script>
import {
  ZoomIn as IconZoomIn,
  Delete as IconDelete,
  QuestionFilled
} from '@element-plus/icons-vue'
import { mapGetters } from 'vuex'
import { $emit } from '../../../../../utils/gogocodeTransfer'

import { ElLoading as Loading } from 'element-plus'
import ImageDialog from '@/components/Dialog/AliImageDialog.vue'
import VideoDialog from '@/components/Dialog/VideoDialog.vue'
import ChooseVideo from '@/components/Dialog/ChooseVideo.vue'
import searchLabel from '@/components/label/searchLabel.vue'
import labelContent from '@/components/label/labelContent.vue'
import RealData from './realData.vue'
import { deepCopy } from '@/utils'
import {
  updatePatternDetail,
  getAgeDetail,
  searchAgePattern,
  searchCarList
} from '@/api/garage'
import { getBrandDetail } from '@/api/brand'
import {
  onSaleEnum,
  appearMarket,
  produceWay,
  carOnSaleEnum,
  TagType
} from '@/utils/enum'
import { resetData } from '@/utils'
import {
  validatePositiveNumber,
  validatePositiveNumberOne,
  alidatePositiveInteger
} from '@/utils/validate'
import SeeLog from '../../components/SeeLog.vue'
import { carLog, setValue, setValueNumber } from '../../components/log.js'
import { convertKeyValueEnum } from '@/utils/convert'
import { postLog } from '@/components/seeLog/SaveLog.js'
import SelectTag from '@/components/label/selectTags.vue'
export default {
  name: 'CeNewGarage',
  components: {
    ImageDialog,
    VideoDialog,
    ChooseVideo,
    // searchLabel,
    // labelContent,
    RealData,
    IconZoomIn,
    IconDelete,
    SeeLog,
    SelectTag,
    QuestionFilled
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  emits: ['quit', 'getData'],
  data() {
    return {
      TagType,
      selectTagVisiable: false,
      tagModelType: '',
      selectTagtype: TagType.CONfIG,
      checkedWheel: [],
      childWheelOptions: ['正三轮', '倒三轮', '边三轮'],
      checkedPedal: [],
      childPedalOptions: ['平踏', '非平踏'],
      labels: [], // 模式选择标签
      labelsPack: [], // 包装标签
      labelsConfig: [], // 其他配置标签
      motorCarOtherListedInfoENTList: [],
      checked: true,
      isCarYear: false, // 年代款是否传递
      loadingGarage: false,
      isOnSaleList: onSaleEnum,
      produceWayList: produceWay,
      appearMarket: appearMarket,
      goodAbsList: ['无', '标配', '选配'],
      goodNewAbsList: ['单前', '单后', '前后', '无'],
      goodNewAbsConfigurationList: ['标配', '无'], // ABS防抱死系统
      goodNewAbsTypeList: ['标配前后', '标配单前'], // ABS防抱死系统
      // goodSpeedModeList: ['国际6挡', '国际5挡', 'DCT6速自动挡', 'DCT7速自动挡', 'CVT无级变速自动挡', '循环5挡', '循环4挡', '国际4挡', '前进4挡+1倒挡', '前进5挡+1倒挡', '前进6挡+1倒挡','往复式3挡','往复式4挡','往复式5挡','往复式6挡'],
      goodSpeedModeList: [
        {
          value: '国际挡',
          label: '国际挡',
          children: [
            {
              value: '国际6挡',
              label: '国际6挡'
            },
            {
              value: '国际5挡',
              label: '国际5挡'
            },
            {
              value: '国际4挡',
              label: '国际4挡'
            }
          ]
        },
        {
          value: 'CVT自动挡',
          label: 'CVT自动挡',
          children: [
            {
              value: 'CVT无级变速自动挡',
              label: 'CVT无级变速自动挡'
            }
          ]
        },
        {
          value: 'DCT自动挡',
          label: 'DCT自动挡',
          children: [
            {
              value: 'DCT3速自动挡',
              label: 'DCT3速自动挡'
            },
            {
              value: 'DCT4速自动挡',
              label: 'DCT4速自动挡'
            },
            {
              value: 'DCT5速自动挡',
              label: 'DCT5速自动挡'
            },
            {
              value: 'DCT6速自动挡',
              label: 'DCT6速自动挡'
            },
            {
              value: 'DCT7速自动挡',
              label: 'DCT7速自动挡'
            },
            {
              value: 'DCT8速自动挡',
              label: 'DCT8速自动挡'
            }
          ]
        },
        {
          value: 'AMT自动挡',
          label: 'AMT自动挡',
          children: [
            {
              value: 'AMT3速自动挡',
              label: 'AMT3速自动挡'
            },
            {
              value: 'AMT4速自动挡',
              label: 'AMT4速自动挡'
            },
            {
              value: 'AMT5速自动挡',
              label: 'AMT5速自动挡'
            },
            {
              value: 'AMT6速自动挡',
              label: 'AMT6速自动挡'
            },
            {
              value: 'AMT7速自动挡',
              label: 'AMT7速自动挡'
            }
          ]
        },
        {
          value: '循环挡',
          label: '循环挡',
          children: [
            {
              value: '循环5挡',
              label: '循环5挡'
            },
            {
              value: '循环4挡',
              label: '循环4挡'
            }
          ]
        },
        {
          value: '往复式挡',
          label: '往复式挡',
          children: [
            {
              value: '往复式6挡',
              label: '往复式6挡'
            },
            {
              value: '往复式5挡',
              label: '往复式5挡'
            },
            {
              value: '往复式4挡',
              label: '往复式4挡'
            },
            {
              value: '往复式3挡',
              label: '往复式3挡'
            }
          ]
        },
        {
          value: '含倒挡',
          label: '含倒挡',
          children: [
            {
              value: '前进6挡+1倒挡',
              label: '前进6挡+1倒挡'
            },
            {
              value: '前进5挡+1倒挡',
              label: '前进5挡+1倒挡'
            },
            {
              value: '前进4挡+1倒挡',
              label: '前进4挡+1倒挡'
            },
            {
              value: '前进3挡+1倒挡',
              label: '前进3挡+1倒挡'
            }
          ]
        },
        {
          value: '单速挡',
          label: '单速挡',
          children: [
            {
              value: '单速挡',
              label: '单速挡'
            }
          ]
        }
      ],
      goodClutchModeList: [
        '湿式多片双离合',
        '湿式多片离合',
        '干式多片离合',
        '干式单片离合',
        '湿式多片滑动离合',
        '干式离心式离合',
        '湿式离心式离合',
        '干式多片滑动离合'
      ],
      lightsList: ['LED', '卤素', '激光'],
      goodTyreTypeList: ['真空胎', '非真空胎'],
      goodWheelRimList: [
        '整体式轮毂（铝合金铸造）',
        '整体式轮毂（铝合金锻造）',
        '整体式（钢制）',
        '辐条式轮毂（铝合金）',
        '辐条式轮毂（钢制）',
        '碳纤维'
      ],
      goodElectricCoolDownList: ['风冷', '水冷'],
      goodSaleList: ['在产', '停产', '未知'],

      batteryList: [
        '三元锂电池',
        '铁锂电池',
        '猛锂电池',
        '铅酸电池',
        '燃料电池'
      ], // 电池类别
      goodDriveTypeList: [
        '链条传动',
        '皮带传动',
        '轴传动',
        '电机传动',
        '齿轮传动'
      ], // 传动方式列表
      // slipClutchList:['无', '标配', '选配'], // 滑动离合器
      goodVoltageList: [
        '24V',
        '36V',
        '48V',
        '60V',
        '64V',
        '72V',
        '80V',
        '84V',
        '96V',
        '108V',
        '120V',
        '130V',
        '132V',
        '144V',
        '156V',
        '168V'
      ], // 电压列表
      goodClutchModeStandardList: ['标配', '选配'], // 离合器标准列表
      keylessGoList: ['无', '标配', '选配'], // 无钥匙启动等
      electronicThrottleList: ['单拉线油门', '双拉线油门', '电子油门'], // 油门配置
      uSBChargingList: [
        '无',
        '标配 USB',
        '标配 DC12V',
        '标配 USB+DC12V',
        '选配 USB',
        '选配 DC12V',
        '选配 USB+DC12V'
      ], // USB充电口配置
      electricHeatingSeatCushionList: [
        '无',
        '标配 前后座',
        '标配 前座',
        '标配 后座',
        '选配 前后座',
        '选配 前座',
        '选配 后座'
      ],
      windshieldTypeList: ['不可调节', '手动', '电动'], // 风挡
      windshieldCheckeList: [
        '2段可调',
        '3段可调',
        '4段可调',
        '5段可调',
        '多段可调',
        '无级可调',
        '无级可调带记忆功能'
      ], // 风挡
      productPlaceList: [
        '中国',
        '中国台湾省',
        '日本',
        '韩国',
        '泰国',
        '菲律宾',
        '印度',
        '越南',
        '印尼',
        '马来西亚',
        '美国',
        '加拿大',
        '意大利',
        '德国',
        '法国',
        '英国',
        '捷克',
        '俄罗斯',
        '奥地利'
      ], //产地
      modeList: [
        '经济模式',
        '运动模式',
        '赛道模式',
        '雨天模式',
        '泥地模式',
        '雪地模式',
        '越野模式',
        '砂石路面'
      ], // 模式选择
      modeSelectionList: [], // 选择的模式
      quickShiftSystemList: ['双向', '仅升档', '仅降档'], // 快速换挡系统
      colorExpsList: [], // 颜色列表
      yearList: [], // 年代款信息列表
      energyType: [
        { value: '1', name: '电摩' },
        { value: '2', name: '电轻摩' },
        { value: '3', name: '电动自行车' }
      ], // 新能源类型
      goodsList: [], // 品牌列表
      imageTabList: null, // 分tab图片
      pullTypeCarId: '', // 拉取款型id
      powerModeList: ['无', '标配', '选配'],
      powerModeGearList: ['1挡', '2挡', '3挡', '4挡'],
      kineticEnergyRecoveryList: ['无', '标配', '选配'],
      kineticEnergyRecoveryRegulationList: ['可调节强度'],
      millimeterWaveRadarList: ['无', '标配', '选配'],
      millimeterWaveRadarDirectionalList: ['前向', '后向', '前后向'],
      millimeterWaveRadarConfigurationPreList: [
        'ACC自适应巡航',
        'FCW前方碰撞预警'
      ],
      millimeterWaveRadarConfigurationAftList: [
        'BSD盲区监测',
        'RCW后方碰撞预警'
      ],
      mobileAppRemoteControlStandardOptionalList: ['无', '标配', '选配'],
      mobileAppRemoteControlFeaturesList: [
        '车辆监控',
        '远程控制',
        '充电管理',
        '服务预约',
        '智能寻车',
        '车辆分享'
      ],
      keylessStartFeaturesList: [
        'NFC/RFID钥匙',
        '手机蓝牙钥匙',
        '可穿戴钥匙',
        '智能遥控钥匙'
      ],
      goodChargerFunctionList: ['温控', '可调功率'],
      commonModelList: ['无', '标配', '选配'],
      activeSafetyWarningSystemList: [
        'FCW前方碰撞预警',
        'BSD盲区监测',
        'RCW后方碰撞预警'
      ],
      cruiseSystemList: ['ACC自适应巡航', '定速巡航'],
      energyRecoveryGearList: ['可调节强度'],
      smartAppList: [
        '车辆监控',
        '远程控制',
        '充电管理',
        '维保预约',
        '智能寻车',
        '个性音效',
        '车辆分享',
        '智能服务费'
      ],
      keylessUnlockList: [
        '乘坐感应',
        '驻车感应',
        '密码解锁',
        '蓝牙解锁/App解锁',
        'NFC解锁'
      ],
      antiTheftList: ['智能龙头锁', '电子座垫锁', '电子围栏', '自动锁车'],
      ruleForm: {
        newEnergyTypes: '', // 新能源类型
        motorCarOtherListedInfoENTList: [],
        goodsCarNameNew: '', // 款型名称
        brandId: '', // 品牌Id
        brandName: '', // 品牌名称
        goodsPrice: '', // 价格
        isOnSale: '', // 在售状态
        goodsName: '', // 车型名称
        goodsId: '', // 车型id
        carName: '', // 年代款
        tags: [], // 标签列表
        colorChange: 0, // 颜色是否更改
        goodColors: '', // 颜色id，逗号分隔
        goodsImgs: '', // 图片json字符串
        goodsThumb: '', // 款型展图
        key: '', // 关键字输入框
        goodType: '', // 车型

        goodAbs: '', // ABS
        goodAbsStandard: '', // ABS类型

        goodAllWeight: '', // 整备质量
        goodBackWheel: '', // 后轮规格
        officialRealGoodBreak50Metre: '', // 破百时间
        goodClearance: '', // 最小离地间隙
        goodClutchMode: '', // 离合器型式
        goodElectricCoolDown: '', // 电车冷却方式

        goodDashboard: '', // 仪表盘
        goodDragDistance: '', // 拖拽距
        goodDriveType: '', // 传动方式
        goodEmptyWeight: '', // 干重
        goodEndurance: '', // 续航里程
        goodConstantSpeedEndurance: '', // 等速续航里程

        goodForwardAngle: '', // 前倾角度
        goodFrame: '', // 车架型式
        goodFrontBrake: '', // 前制动系统

        goodFrontWheel: '', // 前轮规格

        goodHeadlights: '', // 前灯

        goodMaxLoad: '', // 最大有效载荷

        goodMaxSpeed: '', // 最高车速
        goodMinTurn: '', // 最小转弯半径

        goodRearBrake: '', // 后制动系统

        goodRockerArm: '', // 后摇臂
        goodSaddleHigh: '', // 座高
        goodSale: '', // 生产状态
        goodSize: '', // 长x宽x高
        long: '',
        width: '',
        high: '',
        goodSpeedMode: '', // 变速器型式 CVT无极变速自动挡
        goodTaillight: '', // 后灯
        goodTechnology: '', // 技术
        goodTime: '', // 上市时间

        goodTyreType: '', // 轮胎形式

        goodWheelBase: '', // 轴距
        goodWheelNums: '',
        goodWheelRim: '', // 轮辋
        goodBatteryType: '', // 电池类型
        goodBatteriesType: '', // 电芯类型
        goodBatterySpecification: '', // 电池规格
        goodVoltage: '', // 电压(V)
        goodBatteryCapacity: '', // 电池容量(Ah)
        goodBatteryWeight: '', // 电池重量(kg)
        goodChargingTime: '', // 充电时间(h)
        goodQuickCharge: '', // 快充时间(h)
        goodStandardCharging: '', // 标准充电电流(A)
        goodCycleCharge: '', // 循环充电次数
        goodTemperatureRange: '', // 放电耐温范围(℃)
        goodMotorBrand: '', // 电机品牌
        goodBiggestClimbing: '', // 最大爬坡(°)
        goodMotorControl: '', // 电机控制方式
        goodElectricEfficiency: '', // 电机效率
        goodDriveGear: '', // 动力挡位
        goodMotorWaterproof: '', // 电机防水
        goodKilometersElectricity: '', // 百公里电耗(kW·h/km)
        goodBatterySize: '', // 电池包尺寸(mm)
        goodChargerSize: '', // 充电器尺寸(mm)
        goodCBS: '', // CBS
        goodProductType: '', // 生产方式
        productPlace: '', // 产地
        standard: '', // 款型标准化
        goodTemperatureRangeRight: '', // 耐温

        drivingLicenseName: '', // 行驶证名称
        goodMotorMaxHorse: '', // 电机最大马力
        discount: '', // 活动价
        manualName: '',
        manualUrl: '',

        labelChange: 0, // 标签是否更改
        modeSelection: '', // 模式选择标签
        labelsPack: '', // 包装标签
        labelsConfig: '', // 其他配置标签
        factoryName: '', // 厂家
        // vehicleWarranty: '', // 整车质保

        brandModel: '', // 品牌/型号
        steeringControlSystem: '', // 转向阻尼器
        quickShiftSystem: '', // 快速换挡系统
        quickShiftSystemtype: '', // 快速换挡系统

        electronicConnection: '', // 手机连接功能(蓝牙/WIFI等)
        navigationSystem: '', // 导航投屏功能
        windshieldModel: '', // 风挡
        windshieldType: '', // 风挡
        windshieldChecke: '', // 风挡
        goodTurnlight: '', // 转向灯
        goodAssistlight: '', // 辅助灯
        goodWarninglight: '', // 危险警示灯(双闪)
        newCarOnsaleLable: '', // 是否是新车上市
        tricycle: '', // 三轮车
        footBoard: '', // 踏板
        newCarOnsaleEndTime: '', // 新车上市截止时间
        needDriversLicense: '', // 是否需要驾照
        driversType: '', // 驾照类型
        batteryWarrantyMonth: '', // 动力电池质保
        frameWarrantyMonth: '', // 车架质保
        motorWarrantyMonth: '', // 电机质保
        goodMotorMaxPowerSpeed: '', // 电机最大功率转速(rpm)
        goodMotorAcrotOrque: '', // 电机最大转矩(N·m)
        goodMotorMaxTorqueSpeed: '', // 电机最大转矩转速(rpm)
        realGoodKilometersElectricity: '', // 实测百公里电耗(kW·h/km)
        goodMotorControlMode: '', // 电机控制方式
        goodMotorControlType: '', // 电机控制方式
        goodMotorPower: '', // 电机额定功率(kW)
        goodBatteryEnergy: '', // 电池能量(kWh)
        goodBatteryNum: '', // 电池数量
        goodBatteryTearOpen: '', // 电池是否可拆
        goodBatteryCompatible: '', // 可兼容充电
        goodFrontSuspensionType: '', // 前悬挂系统
        goodFrontSuspensionAirBag: '', // 前悬挂气囊
        goodFrontSuspensionBrand: '', // 前悬挂品牌
        goodFrontSuspensionMode: '', // 调节方式
        goodFrontSuspensionCompress: '', // 压缩阻尼
        goodFrontSuspensionResilience: '', // 回弹阻尼
        goodFrontSuspensionDiameter: '', // 减震器内管直径
        goodFrontSuspensionTrip: '', // 行程(mm)

        goodBackSuspensionType: '', // 后悬挂系统
        goodBackSuspensionSide: '', // 后悬挂边置式
        goodBackSuspensionAirBag: '', // 后悬挂气囊
        goodBackSuspensionBrand: '', // 后悬挂品牌
        goodBackSuspensionMode: '', // 调节方式
        goodBackSuspensionCompress: '', // 压缩阻尼
        goodBackSuspensionResilience: '', // 回弹阻尼
        goodBackSuspensionTrip: '', // 行程(mm)
        tractionControlSystemSwitch: '', // 牵引力-可关闭
        dynamicESASwitch: '', // 电子悬挂-可关闭
        dynamicOTA: '', // OTA升级
        goodBatteryChargingPort: '', // 电池充电口
        goodBatterySelfHeating: '', // 电池自加热系统
        goodExternalDischarge: '', // 外放电系统
        aBSOnOffSwitch: '', // ABS后轮可关闭
        powerMode: '', // 动力模式
        powerModeGear: '', // 动力模式挡位
        kineticEnergyRecovery: '', // 动能回收
        kineticEnergyRecoveryRegulation: '', // 动能回收调节
        millimeterWaveRadar: '', // 毫米波雷达
        millimeterWaveRadarDirectional: '', // 毫米波雷达方向
        millimeterWaveRadarConfiguration: '', // 毫米波雷达配置
        // mobileAppRemoteControl: '', // 手机APP远程控制
        mobileAppRemoteControlStandardOptional: '', // 手机APP远程控制 标配 选配
        mobileAppRemoteControlFeatures: '', // 手机APP远程控制 功能
        // keylessStart: '', // 无钥匙启动
        keylessGo: '', // 无钥匙启动 标配 选配
        keylessStartFeatures: '', // 无钥匙启动 功能
        goodSeatCushionLength: '', // 座垫长度
        goodRatedLoad: '', // 额定负载
        goodChargerFunction: '', // 充电器功能
        goodBms: '', // BMS
        goodDrivingMode: '', // 驾驶模式
        goodTCS: '', // TCS
        goodCurveABS: '', // 弯道ABS(ABS PRO/ABS EVO等)
        goodSteeringDamper: '', // 转向阻尼器
        goodSOS: '', // SOS
        activeSafetyWarningSystem: '', // 主动安全预警系统
        hillStartAssistControl: '', // HHC坡道驻车
        singleHandleMode: '', // OTD单转把模式
        cruiseSystem: '', // 巡航系统
        reverseAssist: '', // 倒车辅助
        assistedPushing: '', // 助推
        edgeSupportSensing: '', // 边缘支撑感应
        energyRecovery: '', // 能量回收
        energyRecoveryGear: '', // 能量回收档位
        turnSignalAutoCorrection: '', // 转向灯自动校正
        dashboardSize: '', // 仪表盘尺寸
        dashboardBrightness: '', // 仪表盘亮度
        dashboardMaterial: '', // 仪表盘材质
        intelligentDrivingSystem: '', // 智能驾驶系统
        phoneMapping: '', // 手机互联映射
        voiceControl: '', // 语音控制
        faceRecognition: '', // 面部识别
        smartApp: '', // 智能APP
        speakers: '', // 扬声器
        keylessUnlock: '', // 无钥匙解锁
        antiTheft: '', // 防盗
        usbCount: '', // USB数量
        typeCCount: '', // Type-C数量
        bucketVolume: '', // 桶型容积
        bucketSize: '', // 桶型尺寸
        pedalSize: '', // 踏板尺寸
        frontBasket: '', // 前置物篮
        frontStorage: '', // 前置储物格
        goodHeadlightsLength: '', // 前大灯光照距离长度
        goodHeadlightsWidth: '', // 前大灯光照距离宽度
        goodHeadlightsNear: '', // 近光灯
        goodHeadlightsFar: '', // 远光灯
        goodAmbientlight: '', //氛围灯
        goodAmbientlightStandard: '' // 环境灯 标配 选配
      }, // 款型属性详情数据
      goodSpeedMode: [], // 变速器型式中转字段
      rules: {},
      isModifyStatus: false, // 是否有修改
      isEditStatus: false, // 是否是编辑状态
      loading: false, // 加载状态
      isPostDataStatus: false, // 是否在发送数据状态
      checkedCategory: [], // 被选中的车型
      carCategoryList: [
        '踏板',
        '跨骑',
        '街车',
        '跑车',
        '旅行',
        '拉力',
        '越野',
        '巡航太子',
        '复古',
        '弯梁',
        'MINI',
        '三轮',
        '其他'
      ],
      videoList: [], // 视频列表
      showVideoList: [], // 展示的视频列表
      disableSubmit: false, // 是否禁用发送请求
      isAddType: false, // 是否是款型新增
      copyGarage: '', // 是否复制款型，值： ''、true、false
      buttonType1: 'primary', // 推送button按钮类型
      buttonType2: 'primary', // button按钮类型
      checkList: [],
      manualName: '',
      manualUrl: '',
      modelHeight: '', // 模特身高
      // sittingHeight: '', // 座高
      factoryNotProvide: 0, // 厂家暂未提供参数配置
      updateData: {}, // 更新数据
      groupIds: '', //组别ids
      needDriversLicenseType: ['无需驾照', '需要'], // 是否需要驾驶证
      driversTypeList: ['D证', 'E证', 'F证'],
      goodMotorLayoutType: ['侧挂电机', '中置电机', '轮毂电机'], // 电机布局类型
      goodMotorControlModeList: ['风冷', '水冷'], // 电机控制方式
      goodMotorControlTypeList: ['带风扇'],
      goodBatteryTearOpenList: ['无', '标配'],
      goodBatteryCompatibleList: [
        '兼容直流汽车充电桩',
        '兼容交流汽车充电桩',
        '兼容直流/交流汽车充电桩'
      ],
      goodFrontSuspensionTypeList: [
        '正置式',
        '倒置式',
        '摇臂式',
        '双A臂',
        '双叉臂',
        '独立悬挂'
      ],
      goodFrontSuspensionAirBagList: ['外置式气囊', '内置式气囊'],
      goodFrontSuspensionModeList: [
        '2段可调',
        '3段可调',
        '4段可调',
        '5段可调',
        '多段可调'
      ],

      goodBackSuspensionTypeList: ['单减震', '多连杆减震'],
      goodBackSuspensionSideList: ['单减震', '双减震'],
      goodbackSuspensionModeList: [
        '2段可调',
        '3段可调',
        '4段可调',
        '5段可调',
        '多段可调'
      ],
      switchList: ['可关闭'],
      goodBatterySelfHeatingList: ['无', '标配', '选配'], // 电池自加热系统
      aBSOnOffSwitchList: ['后轮可关闭'],
      dynamicESAswitchList: ['标配自适应电子悬挂']
    }
  },
  computed: {
    ...mapGetters(['uid']),
    goodMotorMaxHorse() {
      return (
        this.ruleForm.goodMotorMaxPower &&
        (Number(this.ruleForm.goodMotorMaxPower) * 1.3596216).toFixed(1)
      )
    }
  },
  watch: {
    isModifyStatus(value) {
      $emit(this, 'quit', value)
      $emit(this, 'getData', this.ruleForm)
    },
    'ruleForm.goodVoltage': function (value) {
      const v1 = this.ruleForm.goodBatteryCapacity || 0
      this.ruleForm.goodBatteryEnergy = ((v1 * value) / 1000).toFixed(2)
    },
    'ruleForm.goodBatteryCapacity': function (value) {
      const v1 = this.ruleForm.goodVoltage || 0
      this.ruleForm.goodBatteryEnergy = ((v1 * value) / 1000).toFixed(2)
    }
  },
  activated() {
    this.copyGarage = ''
    this.imageTabList = null
    this.buttonType1 = 'primary'
    this.buttonType2 = 'primary'
    if (!window.jQuery) {
      window.loadJs('/static/js/jquery.min.js')
    }
  },
  methods: {
    changeSwitchOptions(key, key2, key3) {
      this.isModifyStatus = true
      if (!this.ruleForm[key] || this.ruleForm[key] === key3) {
        this.ruleForm[key2] = ''
      }
    },
    changeisOnSale() {
      this.isModifyStatus = true
      this.ruleForm.newCarOnsaleLable = ''
      this.ruleForm.newCarOnsaleEndTime
    },
    handleCheckedWheelChange(val) {
      if (val.length) {
        this.checkedWheel = [val[val.length - 1]]
        this.checkedCategory = ['三轮']
        this.checkedPedal = []
      }
    },
    handlecheckedPedalChange(val) {
      if (val.length) {
        this.checkedPedal = [val[val.length - 1]]
        this.checkedCategory = ['踏板']
        this.checkedWheel = []
      }
    },
    // 实测数据
    initRealData() {
      this.$refs['RealData'].init({
        realForm: this.ruleForm.carMeasuredInfoEnt || '',
        oilNum:
          Number(this.ruleForm.goodOilBox) +
          Number(this.ruleForm.viceGoodOilBox)
      })
    },
    // 获取实测数据
    getRealData(info) {
      console.log(info, 'info====')
      this.ruleForm.carMeasuredInfoEnt = JSON.stringify(info) || ''
    },

    async httpRequest(option) {
      option.imageType = 'nowater'
      option.quality = 1
      option.imggeCarType = 'carport_goods_car_img'
      option.notCompress = true
      this.$oss.ossUploadImage(option)
    },
    // ======模式选择======
    // 增加标签
    addLabel(data, type) {
      const me = this
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
      this.$refs.label.addLable(data)
      setTimeout(() => {
        me.$refs.label.getAllData()
      }, 800)
    },
    // 更新关联标签状态
    deleteLabel(item, type) {
      const me = this
      me.labels &&
        me.labels.map(function (value) {
          if (value.labelId === item.labelId || value.labelId === item.id) {
            value.selected = false
          }
        })
      setTimeout(() => {
        me.$refs.label.getAllData()
      }, 800)
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
    },
    // 删除标签
    delLabel(data) {
      this.ruleForm.labelChange = 1
      this.isModifyStatus = true
      this.labels = data
    },
    // 修改快捷标签
    updataLabelAllData(data) {
      this.labels = []
      this.labels = data
    },

    updateDataConfig(tag, type) {
      if (type === this.TagType.CONfIG) {
        this.labelsConfig.push({
          labelName: tag,
          labelType: this.tagModelType
        })
      }
      if (type === this.TagType.PACKAGE) {
        this.labelsPack.push({
          labelName: tag,
          labelType: this.tagModelType
        })
      }
      this.isModifyStatus = true
      this.ruleForm.labelChange = 1
    },

    setTagConfig(type, selectTagtype) {
      this.tagModelType = type
      this.selectTagtype = selectTagtype
      this.selectTagVisiable = true
    },

    deleteLabelConfig(index) {
      this.isModifyStatus = true
      this.labelsConfig.splice(index, 1)
      this.ruleForm.labelChange = 1
    },

    deleteLabelPack(index) {
      this.isModifyStatus = true
      this.labelsPack.splice(index, 1)
      this.ruleForm.labelChange = 1
    },

    handleDelete(item) {
      const me = this
      // me.ruleForm.motorCarOtherListedInfoENTList = me.motorCarOtherListedInfoENTList
      // const index = me.ruleForm.motorCarOtherListedInfoENTList.findIndex((obj) => {
      //   if (obj.listedCountry === item.listedCountry && obj.listedPrice === item.listedPrice) {
      //     return true;
      //   }
      // });
      const index = me.motorCarOtherListedInfoENTList.findIndex((obj) => {
        if (
          obj.listedCountry === item.listedCountry &&
          obj.listedPrice === item.listedPrice
        ) {
          return true
        }
      })
      me.motorCarOtherListedInfoENTList.splice(index, 1)
      // me.ruleForm.motorCarOtherListedInfoENTList.splice(index, 1);
    },
    // 上传文件
    async httpRequest1(option) {
      option.isDocument = true // 是文件
      option.businessType = 4
      this.$oss.ossUploadFile(option)
    },
    onSuccess1(res, file, fileList) {
      console.log(res, file, fileList)
      if (!res) return
      this.manualName = res.fileName
      this.manualUrl = res.url
      this.ruleForm.manualUrl = res.url
      this.ruleForm.manualName = res.fileName
    },
    removeFile() {
      this.manualName = ''
      this.manualUrl = ''
      this.ruleForm.manualUrl = ''
      this.ruleForm.manualName = ''
    },
    handlePictureCardPreview(file) {
      // 直接跳转
      window.open(this.manualUrl)
    },
    increaseListing() {
      const target = { listedCountry: '', listedPrice: '' }
      this.motorCarOtherListedInfoENTList.push(target)
    },
    resetData() {
      resetData(this)
    },
    // 标签处理
    dealLable() {
      const me = this
      // 清除标签
      // me.$refs.label.deleteAllLabel()
      // 模式标签
      if (me.ruleForm.modeSelection) {
        const labels = setLabel(JSON.parse(me.ruleForm.modeSelection))
        me.labels = labels
        setTimeout(() => {
          me.$refs.label.addLable(labels)
        }, 500)
      }
      // 包装标签
      if (me.ruleForm.labelsPack) {
        me.labelsPack = JSON.parse(me.ruleForm.labelsPack || '[]')
      }
      // 配置标签
      if (me.ruleForm.labelsConfig) {
        me.labelsConfig = JSON.parse(me.ruleForm.labelsConfig || '[]')
      }
      function setLabel(label) {
        label.map(function (value) {
          value.labelId = value.id
        })
        return label
      }
    },
    // 变速器形式处理
    dealSpeedMode(str) {
      const me = this
      if (str) {
        me.goodSpeedModeList.map((item) => {
          item.children.map((it) => {
            if (it.value === str) {
              me.goodSpeedMode = [item.value, str]
            }
          })
        })
      } else {
        me.goodSpeedMode = []
      }
    },
    // 新建时，获取本地存储数据
    obtainData(data) {
      const me = this
      me.resetData()
      me.isAddType = me.$route.query.type === 'addType'
      me.pullTypeCarId = ''
      me.videoList = []
      me.labels = []
      me.labelsPack = []
      me.labelsConfig = []
      me.ruleForm.modeSelection = ''
      me.ruleForm.labelsPack = ''
      me.ruleForm.labelsConfig = ''
      me.ruleForm.carYear = me.$route.query.carYear || data.carYear || ''
      me.isCarYear = !!me.$route.query.carYear
      Object.assign(me.ruleForm, data)
      // 车型
      me.checkedCategory =
        (me.ruleForm.goodType && me.ruleForm.goodType.split(',')) ||
        me.checkedCategory
      // 颜色
      const imgArr = []
      if (me.ruleForm.colorExps) {
        me.ruleForm.colorExps.map((item) => {
          let obj = {}
          if (item.image) {
            item.image.split(',').map((e, index) => {
              if (index === 0) {
                obj.image = e
              } else {
                obj['image' + index] = e
              }
            })
          }
          obj = { ...item, ...obj }
          imgArr.push(obj)
        })
      }
      me.colorExpsList = imgArr
      // 标签处理
      me.dealLable()
      // 变速器形式处理
      me.dealSpeedMode(me.ruleForm.goodSpeedMode)

      // const updataValueList = ['keylessGo', 'uSBCharging', 'cruiseControl', 'dynamicESA', 'automaticHeadlamp', 'electricHeatingHandle', 'electricHeatingSeatCushion', 'tirePressureMonitoring',
      //  'electronicThrottle', 'aBSOnOff', 'curveABS', 'tractionControlSystem', 'quickShiftSystem']
      const updataValueList = [
        'keylessGo',
        'uSBCharging',
        'cruiseControl',
        'dynamicESA',
        'automaticHeadlamp',
        'electricHeatingHandle',
        'electricHeatingSeatCushion',
        'tirePressureMonitoring',
        'aBSOnOff',
        'curveABS',
        'tractionControlSystem',
        'quickShiftSystem'
      ]
      updataValueList.map((_) => {
        me.ruleForm[_] = me.ruleForm[_] ? me.ruleForm[_] : '无'
      })
      if (me.$route.query.brandId) {
        // 如果新建款型的时候有品牌id 获取这个品牌的生产方式
        getBrandDetail(me.$route.query.brandId)
          .then((response) => {
            me.ruleForm.goodProductType = response.data.data.productSource
          })
          .catch(() => {})
      }
      me.getAgeList()
    },
    // 编辑时，插入数据
    init(data) {
      const me = this
      me.isAddType = me.$route.query.type === 'addType'
      me.labels = []
      me.labelsPack = []
      me.labelsConfig = []
      me.ruleForm.modeSelection = ''

      me.ruleForm.labelsPack = ''
      me.ruleForm.labelsConfig = ''
      me.ruleForm.long = ''
      me.ruleForm.width = ''
      me.ruleForm.high = ''
      me.ruleForm.carYear = me.$route.query.carYear || data.carYear || ''
      me.isCarYear = !!me.$route.query.carYear
      me.isEditStatus = true
      me.modelHeight = data.modelHeight // 模特身高
      // me.sittingHeight = data.goodSaddleHigh // 座高
      data.goodVoltage = data.goodVoltage
        ? data.goodVoltage.replace(/V/, '')
        : ''
      // 电池额定容量
      data.goodBatteryCapacity = data.goodBatteryCapacity
        ? data.goodBatteryCapacity.replace(/Ah/, '')
        : ''
      data.newEnergyTypes = data.newEnergyTypes || ''
      me.ruleForm = {
        ...me.ruleForm,
        ...data
      }
      // 使用说明书
      me.manualName = data.manualName ? data.manualName : ''
      me.manualUrl = data.manualUrl ? data.manualUrl : ''
      me.ruleForm.manualName = data.manualName ? data.manualName : ''
      me.ruleForm.manualUrl = data.manualUrl ? data.manualUrl : ''
      me.motorCarOtherListedInfoENTList =
        me.ruleForm.motorCarOtherListedInfoENTList
      // 车型
      me.checkedCategory =
        (me.ruleForm.goodType && me.ruleForm.goodType.split(',')) ||
        me.checkedCategory
      // 三轮车
      me.checkedWheel = me.ruleForm.tricycle ? [me.ruleForm.tricycle] : []
      // 踏板
      me.checkedPedal = me.checkedPedal ? [me.ruleForm.footBoard] : []
      // me.ruleForm.batteryWarrantyMonth =
      //   me.ruleForm.batteryWarrantyMonth.replace(/个月/, '')
      // me.ruleForm.frameWarrantyMonth =
      //  me.ruleForm.frameWarrantyMonth.replace(/个月/, '')
      // me.ruleForm.motorWarrantyMonth =
      //  me.ruleForm.motorWarrantyMonth.replace(/个月/, '')
      // 颜色
      const imgArr = []
      if (me.ruleForm.colorExps) {
        me.ruleForm.colorExps.map((item) => {
          let obj = {}
          if (item.image) {
            item.image.split(',').map((e, index) => {
              if (index === 0) {
                obj.image = e
              } else {
                obj['image' + index] = e
              }
            })
          }
          obj = { ...item, ...obj }
          imgArr.push(obj)
        })
      }
      me.colorExpsList = imgArr

      // me.colorExpsList = me.ruleForm.colorExps
      // 是否
      me.checkList = me.ruleForm.standard
      me.factoryNotProvide = me.ruleForm.factoryNotProvide || 0
      // 标签处理
      me.dealLable()
      // 变速器形式处理
      me.dealSpeedMode(me.ruleForm.goodSpeedMode)
      // 放电耐温范围
      me.ruleForm.goodTemperatureRangeRight =
        me.ruleForm.goodTemperatureRange.indexOf('~') > -1
          ? me.ruleForm.goodTemperatureRange.split('~')[1]
          : ''

      me.ruleForm.goodTemperatureRange =
        me.ruleForm.goodTemperatureRange.indexOf('~') > -1
          ? me.ruleForm.goodTemperatureRange.split('~')[0]
          : me.ruleForm.goodTemperatureRange

      // 模式
      me.modeSelectionList = me.ruleForm.modeSelection.length
        ? me.ruleForm.modeSelection.split(',')
        : []

      const splitArr = [
        'goodChargerFunction',
        'cruiseSystem',
        'activeSafetyWarningSystem',
        'smartApp',
        'antiTheft',
        'keylessUnlock'
      ]
      splitArr.map((_) => {
        me.ruleForm[_] = me.ruleForm[_] ? me.ruleForm[_].split(',') : []
      })

      const updataValueList = [
        'keylessGo',
        'uSBCharging',
        'cruiseControl',
        'dynamicESA',
        'automaticHeadlamp',
        'electricHeatingHandle',
        'electricHeatingSeatCushion',
        'tirePressureMonitoring',

        'aBSOnOff',
        'curveABS',
        'tractionControlSystem',
        'quickShiftSystem'
      ]
      updataValueList.map((_) => {
        me.ruleForm[_] = me.ruleForm[_] ? me.ruleForm[_] : '无'
      })
      if (!me.modeSelectionList.length) me.updataModeList()
      me.videoList = []
      me.showVideoList = me.ruleForm.videoList || []
      this.ruleForm.content = me.ruleForm.videoList.length
        ? JSON.stringify(me.ruleForm.videoList)
        : ''
      me.getAgeList()
    },
    // 获取年代款列表
    getAgeList() {
      // 获取年代款列表
      if (
        !this.ruleForm.goodsId &&
        !this.$route.query &&
        this.$route.query.id
      ) {
        return
      }
      searchAgePattern({
        goodsId:
          this.ruleForm.goodsId || (this.$route.query && this.$route.query.id)
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.yearList = response.data.data
            if (!this.ruleForm.carName) {
              this.ruleForm.carName = this.yearList.length
                ? this.yearList[0].carName
                : this.ruleForm.carName || ''
            }
          }
        })
        .finally((_) => {})
    },
    // 远程搜索车型名称
    remoteMethod(data) {
      if (!data || data === true) {
        return
      }
      const me = this
      me.goodsList = []
      searchCarList({
        name: data,
        limit: 20,
        page: 1
      }).then((response) => {
        if (response.data.code === 0) {
          const res = response.data.data
          res.list.map(function (value) {
            const newObj = {
              labelName: value.goodName,
              labelId: value.goodId,
              brandId: value.brandId,
              energyType: value.energyType
            }
            me.goodsList.push(newObj)
          })
        }
      })
    },
    // 变更车型名称
    addBrand(item) {
      const me = this
      me.ruleForm.carName = ''
      me.yearList = []
      me.ruleForm.goodsName = item || ''
      me.ruleForm.goodsId = ''
      me.ruleForm.brandName = ''

      if (me.ruleForm.goodsName) {
        me.ruleForm.goodsId = me.goodsList.filter(
          (_) => _.labelName === me.ruleForm.goodsName
        )[0].labelId
        me.ruleForm.brandId = me.goodsList.filter(
          (_) => _.labelName === me.ruleForm.goodsName
        )[0].brandId
        me.ruleForm.brandName = me.goodsList.filter(
          (_) => _.labelName === me.ruleForm.goodsName
        )[0].brandName

        me.getAgeList()
      }
    },
    getDetail(type) {
      const me = this
      if (!me.pullTypeCarId) {
        me.copyGarage = ''
        return
      }

      me.copyGarage = type
      me.loadingGarage = true
      getAgeDetail({
        carId: me.pullTypeCarId
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            if (data && data.attr) {
              Object.keys(data.attr).map((name) => {
                data[name] = data.attr[name] === 'null' ? '' : data.attr[name]
              })
              delete data.attr
            }
            if (me.copyGarage) {
              delete data.carId
              // delete data.carName
              delete data.goodsName
              delete data.goodImgs
              delete data.goodThumb
              delete data.brandId
              delete data.brandName
              delete data.modifyTime
              delete data.goodsThumb
              delete data.pv
              delete data.videoList
              delete data.carMeasuredInfoEnt
            }
            data.goodsId = me.$route.query.id || data.goodsId

            data.standard = (data.standard && data.standard.split(',')) || []
            if (data.goodSize) {
              const goodSizeArr =
                data.goodSize.indexOf('x') > -1
                  ? data.goodSize.split('x')
                  : data.goodSize.split('×') // ×为乘号 不要输入x
              data.long = goodSizeArr[0]
              data.width = goodSizeArr[1]
              data.high = goodSizeArr[2]
            }
            me.motorCarOtherListedInfoENTList =
              data.motorCarOtherListedInfoENTList
            me.init(data)
          }
        })
        .finally(() => {
          me.loadingGarage = false
        })
    },
    // 保存
    goSaveData() {
      this.$message.error('纯电车型请前往电摩范后台更改')
    },
    // 新增或编辑时，发送数据
    postData(data, type) {
      const me = this
      me.deleteObject(me.updateData)
      if (me.isPostDataStatus) {
        const toastTitle = type
          ? '编辑正在处理中，请稍后'
          : '正在发送中，请稍后'
        return me.$message(toastTitle)
      }
      me.isPostDataStatus = true
      const loadingInstance = Loading.service({
        fullscreen: true,
        text: '处理中'
      })
      // me.updateData.content = me.videoList.length ? JSON.stringify(me.videoList) : ''
      me.ruleForm.motorCarOtherListedInfoENTList =
        me.motorCarOtherListedInfoENTList
      me.updateData.contentJson =
        me.ruleForm.motorCarOtherListedInfoENTList &&
        me.ruleForm.motorCarOtherListedInfoENTList.length
          ? JSON.stringify(me.ruleForm.motorCarOtherListedInfoENTList)
          : ''
      // console.log(me.updateData.goodsImgs)
      let postData = {
        ...me.updateData,
        goodVoltage: me.updateData.goodVoltage || '',
        standard: Array.isArray(me.updateData.standard)
          ? me.updateData.standard.join('')
          : me.updateData.standard,
        newEnergyTypes: data.newEnergyTypes || 0
      }
      delete postData.addTime
      delete data.updateTime
      updatePatternDetail(postData)
        .then((response) => {
          loadingInstance.close()
          me.isPostDataStatus = false
          if (response.data.code === 0) {
            const toastTitle = postData.carId ? '编辑成功' : '创建成功'
            me.$message.success(toastTitle)
            me.isModifyStatus = false
            !postData.labelChange ? delete postData.labelChange : null
            !postData.colorChange ? delete postData.colorChange : null
            me.saveLog(
              postData,
              postData.carId ? '编辑' : '新增',
              response.data.data
            )
            setTimeout(() => {
              me.$router.go(-1)
              this.ruleForm = {}
            }, 500)
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          me.isPostDataStatus = false
          loadingInstance.close()
        })
    },
    // 款型展图
    updategoodsThumb(res, imgType) {
      if (!res) return
      console.log(res)
      if (res.name) {
        const data = {
          imgOrgUrl: res.imgOrgUrl,
          imgUrl: res.imgUrl
        }
        this.isModifyStatus = true
        this.key = ''
        this.ruleForm.goodsThumb = data.imgOrgUrl
        this.ruleForm['thumbType'] = imgType
        console.log(data)
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    handleCheckedCategoryChange(val) {
      this.isModifyStatus = true
      if (val.length) {
        this.checkedCategory = [val[val.length - 1]]
        this.checkedPedal = []
        this.checkedWheel = []
      }
      if (this.checkedCategory && this.checkedCategory.length > 0) {
        this.ruleForm.goodType =
          this.checkedCategory && this.checkedCategory.join(',')
      }
    },

    // 获取可选颜色值
    // getColorData(data) {
    //   this.ruleForm.colorChange = 1
    //   // this.isModifyStatus = true
    //   this.colorExpsList = data
    //   const arr = []
    //   data &&
    //     data.map(function (value) {
    //       arr.push(value.id)
    //     })
    //   this.ruleForm.goodColors = arr.join(',')
    // },
    // 获取可选颜色值
    chooseNewSendData(data) {
      this.ruleForm.colorChange = 1
      // this.isModifyStatus = true
      this.colorExpsList = [...data]
      // const arr = []
      // data &&
      //   data.map(function (value) {
      //     arr.push(value.id)
      //   })
      // this.ruleForm.goodColors = arr.join(',')
    },
    // 获取删除图片颜色绑定关系
    getDeleteColorId(id) {
      if (!this.imageTabList) {
        return
      }
      const keyArr = this.imageTabList && Object.keys(this.imageTabList)
      keyArr.map((colorId) => {
        if (this.imageTabList[colorId].length) {
          this.imageTabList[colorId].map((item) => {
            if (item.color === id) {
              item.color = ''
              item.colorName = ''
            }
          })
        }
      })
      this.imageTabList = { ...this.imageTabList }
    },
    // 删除颜色
    deleteColor(index, item) {
      this.ruleForm.colorChange = 1
      // this.isModifyStatus = true
      this.colorExpsList.splice(index, 1)
      // const arr = []
      // this.colorExpsList &&
      //   this.colorExpsList.map(function (value) {
      //     arr.push(value.id)
      //   })
      // this.ruleForm.goodColors = arr.join(',')
    },
    // 初始化图片
    initImage() {
      this.$refs['ImageDialog'].init({
        tabs: this.imageTabList, // tabs 图片列表
        labels: this.colorExpsList,
        goodsId: this.$route.query.id || '',
        carId: this.copyGarage
          ? 'none'
          : this.$route.query.carId || this.ruleForm.carId || 'none', // none 不需要请求接口；复制款型，不需要图片
        watermark: 1, // 是否需要传水印
        systemType: 'car_detail', // car_detail 水印类型
        quality: '0.8', // 图片压缩比例
        showOtherAction: true,
        shopId: '',
        shopName: '',
        modelHeight: this.modelHeight || '',
        goodSaddleHigh: this.ruleForm.goodSaddleHigh || '',
        isModelType: true,
        goodsCarName: this.ruleForm.goodsCarName || ''
      })
    },
    // 复制后刷新图片接口
    refreshImage() {
      this.$refs['ImageDialog'].init({
        tabs: [], // tabs 图片列表
        goodsId: this.$route.query.id || '',
        carId: this.copyGarage
          ? 'none'
          : this.$route.query.carId || this.ruleForm.carId || 'none', // none 不需要请求接口；复制款型，不需要图片
        isRefresh: true
      })
    },
    // 接受款型图片更新
    getImageData(data) {
      this.isModifyStatus = true
      Object.keys(data).map((name) => {
        if (name === 'tabs') {
          // 图片
          this.imageTabList = data[name]
        } else if (name === 'labels') {
          // 颜色
          this.colorExpsList = data[name]
        } else if (name === 'modelHeight') {
          this.modelHeight = data[name]
          this.ruleForm.modelHeight = data[name]
        }
        // else if (name === 'goodSaddleHigh') {
        //   // this.goodSaddleHigh = data[name]
        //   this.ruleForm.goodSaddleHigh = data[name]
        // }
      })
      // 图片处理
      let imageArr = []
      Object.keys(this.imageTabList).map((name) => {
        imageArr = [...imageArr, ...this.imageTabList[name]]
      })
      this.ruleForm.goodsImgs = JSON.stringify(imageArr)
      // 颜色处理
      const arr = []
      if (this.colorExpsList && this.colorExpsList.length > 0) {
        this.colorExpsList.map(function (value) {
          arr.push(value.id)
        })
        this.ruleForm.goodColors = arr.join(',')
      }
      this.ruleForm.groupIds = data.groupIds || ''
    },

    // 获取视频
    getVideoData(data) {
      let arr = []
      this.isModifyStatus = true
      Object.values(data.tabs).map((_) => {
        arr = arr.concat(_)
      })
      this.videoList = arr
      this.ruleForm.content = arr.length ? JSON.stringify(arr) : ''
      this.showVideoList = arr
    },
    // 初始化视频
    initVideo() {
      this.$refs['VideoDialog'].init({
        goodsId: this.$route.query.id || '',
        carId: this.copyGarage
          ? ''
          : this.$route.query.carId || this.ruleForm.carId || '', // none 不需要请求接口；复制款型，不需要图片
        tabs:
          this.videoList && this.videoList[0] && this.videoList[0].imgOrgUrl
            ? this.videoList
            : []
      })
    },
    // 准备看视频
    seeVideoPlay(video) {
      if (!video.videoUrl) {
        return this.$message.error('暂无视频播放地址')
      }
      this.$refs.ChooseVideo.init(video.videoUrl)
    },
    // 取消
    goBack() {
      const me = this
      if (me.isModifyStatus) {
        me.$confirm('当页面有修改数据，是否离开', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning'
        })
          .then(() => {
            me.isModifyStatus = false
            setTimeout(() => {
              me.$router.go(-1)
              me.ruleForm = {}
            }, 500)
          })
          .catch((_) => {})
      } else {
        me.$router.go(-1)
        me.ruleForm = {}
      }
    },
    // 变更模式
    updataModeList() {
      const me = this
      if (!me.modeSelectionList.length) {
        me.modeSelectionList.push('无')
      } else {
        const index = me.modeSelectionList.findIndex((_) => {
          return _ === '无'
        })
        index !== -1 ? me.modeSelectionList.splice(index, 1) : ''
      }
    },
    // 设置日志
    setLog(data) {
      let brandData =
        JSON.parse(sessionStorage.getItem(`editGarage${data.carId}`)) || {}
      const splitValue = ['isOnStatus', 'factoryNotProvide', 'labelChange']
      const splitNumberValue = [
        'brandId',
        'goodsId',
        'goodsPrice',
        'carYearStatus'
      ]
      const produceWay = convertKeyValueEnum(this.produceWayList)
      const showSimple = ['carMeasuredInfoEnt', 'goodsImgs', 'content']
      brandData = {
        ...setValue(brandData, splitValue),
        isOnSale: carOnSaleEnum[brandData.isOnSale],
        goodProductType: produceWay[brandData.goodProductType]
      }
      // 配置选项
      brandData.labelsConfig = setLabelName(brandData.labelsConfig)
      brandData.labelsPack = setLabelName(brandData.labelsPack)
      data.isOnStatus = !!data.isOnStatus
      let newData = {
        ...setValue(data, splitValue),
        isOnSale: carOnSaleEnum[data.isOnSale],
        goodProductType: produceWay[data.goodProductType],
        goodVoltage: data.goodVoltage || '',
        standard: data.standard.split(','),
        goodBatteryCapacity: data.goodBatteryCapacity
          ? data.goodBatteryCapacity.replace(/Ah/, '')
          : ''
      }
      // 配置选项
      newData.labelsConfig = setLabelName(data.labelsConfig)
      newData.labelsPack = setLabelName(data.labelsPack)
      if (newData.labelChange === '否') {
        delete newData.labelChange
      }
      delete newData.goodColors
      brandData = {
        ...setValueNumber(brandData, splitNumberValue)
      }
      newData = {
        ...setValueNumber(newData, splitNumberValue)
      }
      const showImgList = ['goodsThumb']
      const booleanList = []
      const delList = []
      this.$refs.seeLog.init(
        brandData,
        newData,
        carLog,
        showImgList,
        booleanList,
        delList,
        showSimple
      )
      function setLabelName(data) {
        const mapData = JSON.parse(data || '[]')
        const labelsName = mapData.map((label) => {
          return label.labelName
        })
        return JSON.stringify(labelsName)
      }
    },
    postReadyData() {
      this.postData(this.ruleForm, this.isEditStatus)
    },
    // 款型日志
    saveLog(afterData, operateType, carId) {
      let postData = {
        ...deepCopy(afterData)
      }
      ;['isOnStatus'].map((item) => {
        postData[item] = !!postData[item]
      })
      ;['standard', 'colorExps', 'videoList'].map((item) => {
        postData[item] = !(postData[item] && postData[item].length)
          ? []
          : postData[item]
      })
      ;['goodsId', 'brandId', 'goodsPrice'].map((item) => {
        postData[item] = postData[item]
          ? Number(postData[item])
          : postData[item]
      })
      let beforeData = JSON.parse(
        sessionStorage.getItem(`editGarage${postData.carId}`) || '{}'
      )
      const isEdit = !!postData.carId
      postLog(
        77,
        postData.carId || carId || '',
        operateType,
        '',
        isEdit ? JSON.stringify(beforeData) : '{}',
        JSON.stringify(postData)
      )
    },

    initDesign() {
      const name = this.ruleForm.goodsCarName
      const { id, carId } = this.$route.query
      this.$router.push({
        name: 'VehicleDesign',
        query: { id, name: name, carId, isFreeze: true }
      })
    },

    deleteObject(data) {
      delete data.tags
      delete data.colorExps
      delete data.videoList

      delete data.newCarOnsaleEndTime
    },
    getextensionName(url) {
      let extensionName = 'png'
      try {
        const str = url.split('!')[0]
        extensionName = `.${str.split('.').reverse()[0]}` // 文件扩展名
      } catch (e) {
        extensionName = 'png'
      }
      return extensionName
    }
  }
}
</script>

<style lang="scss">
.custom-disabled_style {
  .el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: white !important;
  }
  .el-checkbox__input.is-disabled .el-checkbox__inner::after {
    border-color: white !important;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: rgb(64, 158, 255) !important;
    border-color: rgb(64, 158, 255) !important;
  }
}
.c-new-garage {
  .new-garage-header {
    margin: 20px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .new-garage-form {
    min-height: calc(100vh - 124px);
    margin: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    overflow: hidden;
    .modules {
      width: 100%;
      padding: 5px;
      border-bottom: 1px solid #dcdfe6;
    }
    .title {
      padding: 10px;
      .tip {
        font-size: 13px;
        color: #999;
      }
    }
    .add-video-content {
      position: relative;
      margin: 10px;
      .imageFile {
        opacity: 0;
        position: absolute;
        width: 100%;
        height: 100%;
      }
    }
    .video-content {
      margin: 10px;
      min-height: 100px;
    }
    .video-show-content {
      display: inline-block;
      width: 200px;
      margin-right: 10px;
      position: relative;
      .video-img {
        display: inline-block;
        width: 200px;
        height: 150px;
        object-fit: cover;
      }
      .video-delete {
        position: absolute;
        right: 0;
        top: 0;
      }
      .play-icon {
        position: absolute;
        left: 55px;
        top: 30px;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 5px;
      }
    }
  }
  .lable-red .el-form-item__label {
    color: red;
  }
}
</style>
