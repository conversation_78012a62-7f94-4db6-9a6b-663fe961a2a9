/** * 编辑款型 */
<template>
  <div v-loading="loadingGarage" class="c-new-garage">
    <div class="new-garage-header">
      <el-button type="primary" @click="goSaveData()">保存</el-button>
      <el-button @click="goBack()">取消</el-button>
      <span class="wd-mr-10px" style="margin-left: 20px">款型</span>
      <el-input
        v-model="pullTypeCarId"
        :clearable="true"
        type="number"
        min="0"
        style="width: 160px; margin-right: 10px"
      />
      <el-button
        v-if="!isEdit"
        :type="copyGarage === false ? 'success' : 'primary'"
        @click="getDetail(false)"
        >拉取</el-button
      >
      <el-button
        :type="copyGarage ? 'success' : 'primary'"
        @click="getDetail(true)"
        >复制</el-button
      >
      <div
        style="
          display: flex;
          margin-left: 20px;
          margin-right: 50px;
          align-items: center;
        "
      >
        <span style="margin-right: 20px">款型标准化</span>
        <el-checkbox-group v-model="checkList">
          <el-checkbox label="1">外观</el-checkbox>
          <el-checkbox label="2">细节</el-checkbox>
          <el-checkbox label="3">参数</el-checkbox>
        </el-checkbox-group>
      </div>
      <el-checkbox
        v-model="factoryNotProvide"
        :true-label="1"
        :false-label="0"
        @change="isModifyStatus = true"
      >
        厂家暂未提供参数配置</el-checkbox
      >
      <div style="margin-top: 14px">
        <el-form-item label="选择年代款" required label-width="120px">
          <el-select
            placeholder="请选择"
            v-model="ruleForm.carYear"
            filterable
            :disabled="isCarYear"
          >
            <el-option
              v-for="(item, index) in yearList"
              :key="index"
              :label="item.carName"
              :value="item.carName"
            />
          </el-select>
        </el-form-item>
      </div>
      <div
        style="
          display: flex;
          margin-left: 20px;
          margin-right: 50px;
          align-items: center;
        "
      >
        <span style="margin-right: 20px">新能源类型</span>
        <el-select
          placeholder="请选择"
          v-model="ruleForm.newEnergyTypes"
          clearable
        >
          <el-option
            v-for="(item, index) in energyType"
            :key="index"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      :inline="true"
      label-width="100px"
      class="new-garage-form"
    >
      <div class="modules">
        <el-form-item label="款型名称" required label-width="80px">
          <el-input
            v-model="ruleForm.goodsCarNameNew"
            :clearable="true"
            style="width: 240px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="参考价"
          prop="goodsPrice"
          label-width="90px"
        >
          <el-input
            v-model="ruleForm.goodsPrice"
            type="text"
            style="width: 140px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox
            v-model="ruleForm.isOnStatus"
            @change="isModifyStatus = true"
            >是否上架</el-checkbox
          >
        </el-form-item>
        <el-form-item
          class="lable-red"
          v-if="isAddType"
          label="车型名称"
          required
          label-width="80px"
        >
          <el-select
            v-model="ruleForm.goodsName"
            :filter-method="remoteMethod"
            :loading="false"
            placeholder="请输入车型名称"
            filterable
            remote
            clearable
            @change="addBrand"
          >
            <el-option
              v-for="(item, index) in goodsList"
              :key="index"
              :label="item.labelName"
              :value="item.labelName"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="isAddType"
          label="年代款"
          required
          label-width="80px"
        >
          <el-select v-model="ruleForm.carName">
            <el-option
              v-for="(item, index) in yearList"
              :key="index"
              :label="item.carName"
              :value="item.carName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="在售状态" required label-width="80px">
          <el-select
            v-model="ruleForm.isOnSale"
            style="width: 120px"
            @change="changeisOnSale"
          >
            <el-option
              v-for="(value, index) in isOnSaleList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="行驶证名称" label-width="100px">
          <el-input
            v-model="ruleForm.drivingLicenseName"
            :clearable="true"
            style="width: 240px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="活动价" label-width="100px">
          <el-input
            v-model="ruleForm.discount"
            :clearable="true"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item v-if="ruleForm.isOnSale === 1" label="">
          <el-checkbox
            :true-label="1"
            :false-label="0"
            v-model="ruleForm.newCarOnsaleLable"
            >新车上市</el-checkbox
          >
          <span
            v-if="
              ruleForm.newCarOnsaleLable === 1 && ruleForm.newCarOnsaleEndTime
            "
            style="margin-left: 10px; color: red"
            >截止时间：
            {{
              $dayjs(ruleForm.newCarOnsaleEndTime).format('YYYY年MM月DD日')
            }}</span
          >
        </el-form-item>
      </div>
      <div class="modules" style="margin-bottom: 10px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="款型展图" required label-width="80px">
              <div>
                <div v-if="ruleForm.goodsThumb" style="text-align: center">
                  <div>
                    <img :src="ruleForm.goodsThumb" alt style="height: 100px" />
                  </div>
                  <span style="color: #409eff"
                    >{{ ruleForm.goodsName
                    }}{{ getextensionName(ruleForm.goodsThumb) }}</span
                  >
                </div>

                <div style="display: flex">
                  <el-upload
                    :show-file-list="false"
                    :http-request="httpRequest"
                    :on-success="(res) => updategoodsThumb(res, 0)"
                    name="upfile"
                    class="avatar-uploader"
                    action
                  >
                    <el-button type="primary" size="small">
                      添加图片
                    </el-button>
                    <!-- <img v-else src="@/assets/image/<EMAIL>" alt /> -->
                  </el-upload>
                  <el-upload
                    :show-file-list="false"
                    :http-request="httpRequest"
                    :on-success="(res) => updategoodsThumb(res, 1)"
                    name="upfile"
                    class="avatar-uploader"
                    style="margin-left: 5px"
                    action
                  >
                    <el-button type="primary" size="small">
                      添加透明背景图片
                    </el-button>
                    <!-- <img v-else src="@/assets/image/<EMAIL>" alt /> -->
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="视频首图" label-width="80px">
              <div
                v-if="showVideoList && showVideoList.length"
                class="video-content"
              >
                <div
                  v-for="(video, index) in showVideoList"
                  :key="index"
                  class="video-show-content"
                >
                  <template v-if="index === 0">
                    <img
                      v-if="video.imgOrgUrl"
                      :src="video.imgOrgUrl"
                      alt
                      class="video-img"
                    />
                    <img
                      v-else
                      src="@/assets/image/video-default.jpg"
                      alt
                      class="video-img"
                    />
                    <img
                      class="icon play-icon"
                      src="@/assets/image/<EMAIL>"
                      alt
                      @click="seeVideoPlay(video)"
                    />
                  </template>
                </div>
              </div>
              <div v-else class="video-content" @click="initVideo()">
                暂无视频
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="使用说明书" label-width="85px">
              <div v-if="manualName" class="video-content">
                <div>{{ manualName }}</div>
                <div>
                  <el-button
                    type="primary"
                    size="small"
                    circle
                    @click="handlePictureCardPreview"
                  >
                    <template #icon>
                      <IconZoomIn />
                    </template>
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="removeFile"
                    ><template #icon> <IconDelete /> </template
                  ></el-button>
                </div>
              </div>
              <div v-else class="video-content">暂无说明书</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="modules">
        <!-- <el-row>
          <el-col :span="8">
            <el-button type="primary" style="margin: 10px" @click="initImage()"
              >款型图片</el-button
            >
          </el-col>
          <el-col :span="8">
            <el-button type="primary" style="margin: 10px" @click="initVideo()"
              >视频说明书</el-button
            >
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-upload
                :show-file-list="false"
                :http-request="ossUploadFile"
                accept=".pdf"
                name="upfile"
                action
                style="width: 150px"
              >
                <el-button type="primary">使用说明书</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row> -->
        <div style="display: flex; justify-content: space-between">
          <el-button type="primary" style="margin: 10px" @click="initImage()"
            >款型图片</el-button
          >
          <!-- <el-button type="primary" style="margin: 10px" @click="initRealData()"
            >实测数据</el-button
          > -->
          <el-button type="primary" style="margin: 10px" @click="initDesign()"
            >款型亮点</el-button
          >
          <el-button type="primary" style="margin: 10px" @click="initVideo()"
            >视频说明书</el-button
          >
          <el-form-item>
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest1"
              :on-success="onSuccess1"
              accept=".pdf"
              name="upfile"
              action
              style="width: 150px"
            >
              <el-button type="primary">使用说明书</el-button>
            </el-upload>
          </el-form-item>
        </div>
      </div>
      <div class="title modules">参数配置</div>
      <div class="modules">
        <p class="modules-name">基础参数</p>
        <el-form-item
          class="lable-red"
          label="车辆类型"
          required
          label-width="80px"
        >
          <el-checkbox-group
            v-model="checkedCategory"
            @change="handleCheckedCategoryChange"
          >
            <div class="wd-flex wd-h-120px">
              <div
                v-for="carCategory in carCategoryList"
                :key="carCategory"
                style="margin-right: 5px"
              >
                <el-checkbox
                  :disabled="['三轮', '踏板'].includes(carCategory)"
                  :class="[
                    ['三轮', '踏板'].includes(carCategory)
                      ? 'custom-disabled_style'
                      : ''
                  ]"
                  :label="carCategory"
                  :key="carCategory"
                  >{{ carCategory }}</el-checkbox
                >
                <div class="wd-absolute">
                  <el-checkbox-group
                    v-if="carCategory === '三轮'"
                    v-model="checkedWheel"
                    @change="handleCheckedWheelChange"
                  >
                    <div class="wd-flex wd-flex-col">
                      <el-checkbox
                        v-for="(child, index) in childWheelOptions"
                        :key="index"
                        :label="child"
                        >{{ child }}</el-checkbox
                      >
                    </div>
                  </el-checkbox-group>
                </div>
                <div class="wd-absolute">
                  <el-checkbox-group
                    v-if="carCategory === '踏板'"
                    v-model="checkedPedal"
                    @change="handleCheckedPedalChange"
                  >
                    <div class="wd-flex wd-flex-col">
                      <el-checkbox
                        v-for="(child, index) in pedalChildOptions"
                        :key="index"
                        :label="child"
                        >{{ child }}</el-checkbox
                      >
                    </div>
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </el-checkbox-group> </el-form-item
        ><br />
        <el-form-item label="厂家" prop="factoryName" label-width="50px">
          <el-input
            v-model="ruleForm.factoryName"
            placeholder="请填写厂家名称"
            clearable
            style="width: 330px"
          ></el-input>
        </el-form-item>
        <el-form-item label="品牌" prop="brand" label-width="50px">
          <el-input
            v-model="ruleForm.brandName"
            disabled
            clearable
            style="width: 330px"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="生产方式"
          required
          label-width="80px"
        >
          <el-select
            v-model="ruleForm.goodProductType"
            style="width: 120px"
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(value, index) in produceWayList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="官方续航里程(km)" label-width="140px">
          <span>工况续航</span>
          <el-input
            v-model="ruleForm.goodEndurance"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          />
          <span>等速续航</span>
          <el-input
            v-model="ruleForm.goodConstantSpeedEndurance"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
            style="width: 150px; margin-left: 4px"
          />
        </el-form-item>
        <el-form-item label="重要部件质保">
          <span>动力电池质保</span>
          <el-input
            v-model="ruleForm.batteryWarrantyMonth"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          >
          </el-input>
          <span>车架质保</span>
          <el-input
            v-model="ruleForm.frameWarrantyMonth"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          >
          </el-input>
          <span>电机质保</span>
          <el-input
            v-model="ruleForm.motorWarrantyMonth"
            @change="isModifyStatus = true"
            placeholder="请输入"
            style="width: 150px; margin-left: 4px; margin-right: 10px"
          >
          </el-input>
        </el-form-item>
        <el-form-item
          label="官方0-100km/h加速(s)"
          prop="goodBreakMetre"
          label-width="160px"
        >
          <el-input
            v-model="ruleForm.goodBreakMetre"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="实测0-100km/h加速(s)"
          prop="realGoodBreakMetre"
          label-width="160px"
        >
          <el-input
            disabled
            v-model="ruleForm.realGoodBreakMetre"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <!-- <el-form-item label="续航里程(km)" prop="goodEndurance">
          <el-input
            v-model="ruleForm.goodEndurance"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item> -->
        <el-form-item label="整车质保" prop="vehicleWarranty">
          <el-input
            v-model="ruleForm.vehicleWarranty"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="驾考政策" prop="needDriversLicense">
          <el-select
            v-model="ruleForm.needDriversLicense"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.driversType = ''
              }
            "
            @clear="ruleForm.needDriversLicense = ''"
          >
            <el-option
              v-for="(item, index) in needDriversLicenseList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.needDriversLicense = item"
            />
          </el-select>
          <el-select
            v-if="ruleForm.needDriversLicense === '需要'"
            v-model="ruleForm.driversType"
            clearable
            @change="isModifyStatus = true"
            @clear="ruleForm.driversType = ''"
          >
            <el-option
              v-for="(item, index) in drivingLicenseList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.driversType = item"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">发动机参数</p>
        <el-form-item label="发动机型号" prop="engineType">
          <el-input
            v-model="ruleForm.engineType"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="排量(cc)" prop="goodVolume">
          <el-input
            v-model="ruleForm.goodVolume"
            @change="isModifyStatus = true"
            placeholder="示例: 123"
          />
        </el-form-item>
        <el-form-item label="精确排量(cc)" prop="goodExactVolume">
          <el-input
            v-model="ruleForm.goodExactVolume"
            @change="isModifyStatus = true"
            placeholder="示例: 123"
          />
        </el-form-item>
        <el-form-item
          label="进气形式"
          prop="goodEngineStructure"
          class="lable-border"
        >
          <el-select
            v-model="ruleForm.intakeType"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in intakeTypeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.intakeType = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="发动机结构"
          prop="goodEngineStructure"
        >
          <el-select
            v-model="ruleForm.goodEngineStructure"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodEngineStructureList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodEngineStructure = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="发动机气缸数"
          prop="goodCylinder"
        >
          <el-select
            v-model="ruleForm.goodCylinder"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodCylinderList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodCylinder = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="配气结构"
          prop="goodGasStructureNew"
          label-width="85px"
        >
          <el-select
            v-model="ruleForm.goodGasStructureNew"
            clearable
            @change="isModifyStatus = true"
            @clear="ruleForm.goodGasStructureNew = ''"
          >
            <el-option
              v-for="(item, index) in goodGasStructureList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodGasStructureNew = item"
            />
          </el-select>
          <el-select
            v-model="ruleForm.goodGasStructureNum"
            clearable
            @change="isModifyStatus = true"
            @clear="ruleForm.goodGasStructureNum = ''"
          >
            <el-option
              v-for="(item, index) in goodGasStructureNumList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodGasStructureNum = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="缸径x行程(mm)"
          prop="goodCylinderStroke"
          label-width="140px"
        >
          <el-input
            v-model="ruleForm.goodBore"
            style="width: 100px"
            @change="isModifyStatus = true"
          /><span>x</span>
          <el-input
            v-model="ruleForm.goodStroke"
            style="width: 100px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="发动机冲程" prop="goodTravel">
          <el-select
            v-model="ruleForm.goodTravel"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodTravelList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodTravel = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="压缩比" prop="goodReductRatio">
          <el-input
            v-model="ruleForm.goodReductRatio"
            style="width: 150px"
            @change="isModifyStatus = true"
          /><span>:1</span>
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="冷却方式"
          prop="goodCoolDown"
          label-width="85px"
        >
          <el-select
            v-model="ruleForm.goodCoolDown"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodCoolDownList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodCoolDown = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="最大功率(kw)"
          prop="goodMaxPower"
        >
          <el-input
            v-model="ruleForm.goodMaxPower"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <el-form-item
          label="最大马力(Ps)"
          prop="goodMaxHorse"
          class="lable-border"
        >
          <el-input v-model="goodMaxHorse" @change="isModifyStatus = true" />
        </el-form-item>
        <el-form-item
          label="最大功率转速(rpm)"
          prop="goodMaxPowerSpeed"
          label-width="150px"
        >
          <el-input
            v-model="ruleForm.goodMaxPowerSpeed"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="最大扭矩(N.m)"
          prop="goodAcrotOrque"
          class="lable-border"
          label-width="120px"
        >
          <el-input
            v-model="ruleForm.goodAcrotOrque"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="最大扭矩转速(rpm)"
          prop="goodMaxTorqueSpeed"
          label-width="150px"
        >
          <el-input
            v-model="ruleForm.goodMaxTorqueSpeed"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="燃油形式" prop="fuelForm">
          <el-input
            v-model="ruleForm.fuelForm"
            disabled
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="燃油标号" prop="goodFuel">
          <el-select
            v-model="ruleForm.goodFuel"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodFuelList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodFuel = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="供油方式" prop="goodOilMode" label-width="85px">
          <el-input
            v-model="ruleForm.goodOilBrand"
            style="width: 150px"
            placeholder="请输入品牌"
            @change="isModifyStatus = true"
          />
          <el-select
            v-model="ruleForm.goodOilMode"
            clearable
            placeholder="请选择供油方式"
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodOilModeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodOilMode = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="环保标准"
          prop="goodEnvStandard"
          label-width="85px"
        >
          <el-select
            v-model="ruleForm.goodEnvStandard"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodEnvStandardList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodEnvStandard = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="官方平均油耗(L/100km)"
          prop="goodOilWear"
          label-width="165px"
        >
          <el-input
            v-model="ruleForm.goodOilWear"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="实测平均油耗(L/100km)"
          prop="realGoodOilWear"
          label-width="165px"
        >
          <el-input
            disabled
            v-model="ruleForm.realGoodOilWear"
            @change="isModifyStatus = true"
          />
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">电动动力参数</p>
        <el-form-item label="电机型号" prop="goodMotorBrand" label-width="85px">
          <el-input
            v-model="ruleForm.goodMotorBrand"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电机布局"
          prop="goodMotorLayout"
          label-width="85px"
        >
          <el-select
            v-model="ruleForm.goodMotorLayout"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodMotorLayoutList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodMotorLayout = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="控制器" prop="goodMotorControl">
          <el-input
            v-model="ruleForm.goodMotorControl"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="控制器冷却方式"
          prop="goodMotorControlMode"
          label-width="110px"
        >
          <el-select
            v-model="ruleForm.goodMotorControlMode"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.goodMotorControlType = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in goodMotorControlModeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodMotorControlMode = item"
            />
          </el-select>
          <el-select
            v-if="
              ruleForm.goodMotorControlMode &&
              ruleForm.goodMotorControlMode === '风冷'
            "
            v-model="ruleForm.goodMotorControlType"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodMotorControlTypeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodMotorControlType = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="电机最大马力(ps)"
          prop="goodMotorMaxHorse"
          label-width="125px"
        >
          <el-input
            v-model="goodMotorMaxHorse"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电机最大(峰值)功率(kW)"
          prop="goodMotorMaxPower"
          label-width="180px"
        >
          <el-input
            v-model="ruleForm.goodMotorMaxPower"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电机最大功率转速(rpm)"
          prop="goodMotorMaxPowerSpeed"
          label-width="180px"
        >
          <el-input
            v-model="ruleForm.goodMotorMaxPowerSpeed"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电机最大扭矩(N·m)"
          prop="goodMotorAcrotOrque"
          label-width="135px"
        >
          <el-input
            v-model="ruleForm.goodMotorAcrotOrque"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电机最大扭矩转速(rpm)"
          prop="goodMotorMaxTorqueSpeed"
          label-width="160px"
        >
          <el-input
            v-model="ruleForm.goodMotorMaxTorqueSpeed"
            @change="isModifyStatus = true"
          />
        </el-form-item>

        <el-form-item label="能源类型" prop="energyType" label-width="85px">
          <el-input
            :value="fuelFormList[ruleForm.energyType - 1]"
            disabled
            @change="isModifyStatus = true"
          />
        </el-form-item>

        <el-form-item label="电池重量(kg)" prop="goodBatteryWeight">
          <el-input
            v-model="ruleForm.goodBatteryWeight"
            @change="isModifyStatus = true"
          />
        </el-form-item>

        <el-form-item
          label="标准充电电流(A)"
          prop="goodStandardCharging"
          label-width="120px"
        >
          <el-input
            v-model="ruleForm.goodStandardCharging"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="循环充电次数" prop="goodCycleCharge">
          <el-input
            v-model="ruleForm.goodCycleCharge"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="最高车速(km/h)"
          prop="goodMaxSpeed"
          label-width="110px"
        >
          <el-input
            v-model="ruleForm.goodMaxSpeed"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <!-- <el-form-item
          label="官方百公里电耗(kW·h/km)"
          prop="goodKilometersElectricity"
          label-width="190px"
        >
          <el-input
            v-model="ruleForm.goodKilometersElectricity"
            @change="isModifyStatus = true"
          />
        </el-form-item> -->
        <el-form-item
          label="官方0-50km/h加速(s)"
          prop="officialRealGoodBreak50Metre"
          label-width="150px"
        >
          <el-input
            v-model="ruleForm.officialRealGoodBreak50Metre"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <el-form-item label="实测0-50km/h加速(s)" label-width="160px">
          <el-input
            v-model="ruleForm.realGoodBreak50Metre"
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请输入"
            disabled
          />
          <!-- <span style="margin-left: 10px; color: #999">同步实测数据</span> -->
        </el-form-item>
        <el-form-item label="实测百公里电耗(kW·h/km)" label-width="190px">
          <el-input
            v-model="ruleForm.realGoodKilometersElectricity"
            @change="isModifyStatus = true"
            style="width: 150px"
            placeholder="请输入"
            disabled
          />
          <!-- <span style="margin-left: 10px; color: #999">同步实测数据</span> -->
        </el-form-item>
        <!-- <el-form-item
          label="实测百公里电耗(kW·h/km)"
          prop="realGoodKilometersElectricity"
          label-width="190px"
        >
          <el-input
            v-model="ruleForm.realGoodKilometersElectricity"
            @change="isModifyStatus = true"
          />
        </el-form-item> -->
        <el-form-item
          label="电动机冷却方式"
          prop="goodElectricCoolDown"
          label-width="110px"
        >
          <el-select
            v-model="ruleForm.goodElectricCoolDown"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodElectricCoolDownList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodElectricCoolDown = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="电机额定功率(kW)"
          prop="goodMotorPower"
          label-width="190px"
        >
          <el-input
            v-model="ruleForm.goodMotorPower"
            @change="isModifyStatus = true"
          />
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">电池/补能</p>
        <el-form-item
          label="电池类型"
          prop="goodBatteryType"
          class="lable-border"
          label-width="85px"
        >
          <el-select
            v-model="ruleForm.goodBatteryType"
            clearable
            @change="isModifyStatus = true"
            @clear="ruleForm.goodBatteryType = ''"
          >
            <el-option
              v-for="(item, index) in batteryList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodBatteryType = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="电芯类型"
          prop="goodBatteriesType"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodBatteriesType"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="额定电压(V)" prop="goodVoltage">
          <!-- <el-select v-model="ruleForm.goodVoltage" clearable @change="isModifyStatus = true">
            <el-option
              v-for="(item, index) in goodVoltageList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodVoltage = item"
            />
          </el-select> -->
          <el-input
            v-model="ruleForm.goodVoltage"
            placeholder="示例：12, 12.5"
            style="width: 180px"
          >
            <template #append>V</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="电池额定容量(Ah)"
          prop="goodBatteryCapacity"
          label-width="130px"
        >
          <el-input
            v-model="ruleForm.goodBatteryCapacity"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电池能量(kWh/度)"
          prop="goodBatteryEnergy"
          label-width="130px"
        >
          <el-input
            v-model="ruleForm.goodBatteryEnergy"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电池组数量"
          prop="goodBatteryNum"
          label-width="130px"
        >
          <el-input
            v-model="ruleForm.goodBatteryNum"
            @change="isModifyStatus = true"
          />
        </el-form-item>

        <el-form-item
          label="快拆电池组"
          prop="goodBatteryTearOpen"
          label-width="110px"
        >
          <el-select
            v-model="ruleForm.goodBatteryTearOpen"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in breakGoodBatteryGroupList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodBatteryTearOpen = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="充电时间(h)" prop="goodChargingTime">
          <el-input
            v-model="ruleForm.goodChargingTime"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="快充时间(h)" prop="goodQuickCharge">
          <el-input
            v-model="ruleForm.goodQuickCharge"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="可兼容充电"
          prop="goodBatteryCompatible"
          label-width="110px"
        >
          <el-select
            v-model="ruleForm.goodBatteryCompatible"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in compatibleWithChargingList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodBatteryCompatible = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="充放电耐温范围(℃)"
          prop="goodTemperatureRange"
          label-width="150px"
        >
          <div class="flex">
            <el-input
              v-model="ruleForm.goodTemperatureRange"
              @change="isModifyStatus = true"
            />~
            <el-input
              v-model="ruleForm.goodTemperatureRangeRight"
              @change="isModifyStatus = true"
            />
          </div>
        </el-form-item>
        <el-form-item label="车辆充电口" prop="goodBatteryChargingPort">
          <el-input
            v-model="ruleForm.goodBatteryChargingPort"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="电池自加热系统"
          prop="goodBatterySelfHeating"
          label-width="110px"
        >
          <el-select
            v-model="ruleForm.goodBatterySelfHeating"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodBatterySelfHeatingList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodBatterySelfHeating = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="外放电功能"
          prop="goodExternalDischarge"
          label-width="110px"
        >
          <el-select
            v-model="ruleForm.goodExternalDischarge"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodExternalDischargeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodExternalDischarge = item"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">动力传输</p>
        <el-form-item class="lable-red" label="变速器型式" prop="goodSpeedMode">
          <!-- <el-select v-model="ruleForm.goodSpeedMode" clearable @change="isModifyStatus = true">
            <el-option
              v-for="(item, index) in goodSpeedModeList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select> -->
          <el-cascader
            v-model="goodSpeedMode"
            clearable
            @change="isModifyStatus = true"
            :options="goodSpeedModeList"
            :show-all-levels="false"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="滑动离合器" prop="goodClutchModeStandard">
          <el-select
            v-model="ruleForm.goodClutchModeStandard"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in goodClutchModeStandardList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="lable-red" label="离合器" prop="goodClutchMode">
          <el-select
            v-model="ruleForm.goodClutchMode"
            clearable
            @change="isModifyStatus = true"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in goodClutchModeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodClutchMode = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="lable-red" label="传动方式" prop="goodDriveType">
          <el-select
            v-model="ruleForm.goodDriveType"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodDriveTypeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodDriveType = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="动力档位"
          prop="goodDriveGear"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodDriveGear"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="电子离合" prop="electronicClutch">
          <el-select
            v-model="ruleForm.electronicClutch"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in electronicClutchList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.electronicClutch = item"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">车体参数</p>
        <el-form-item
          label="长x宽x高(mm)"
          prop="goodSize"
          class="lable-border"
          label-width="110px"
        >
          <el-input
            v-model="ruleForm.long"
            style="width: 100px"
            @change="isModifyStatus = true"
          /><span>x</span>
          <el-input
            v-model="ruleForm.width"
            style="width: 100px"
            @change="isModifyStatus = true"
          /><span>x</span>
          <el-input
            v-model="ruleForm.high"
            style="width: 100px"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="座高(mm)" prop="goodSaddleHigh">
          <el-input
            v-model="ruleForm.goodSaddleHigh"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="轴距(mm)"
          prop="goodWheelBase"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodWheelBase"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="最小离地间隙(mm)"
          prop="goodClearance"
          label-width="135px"
        >
          <el-input
            v-model="ruleForm.goodClearance"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="车架型式" prop="goodFrame" class="lable-border">
          <el-input
            v-model="ruleForm.goodFrame"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="后摇臂" prop="goodRockerArm">
          <el-input
            v-model="ruleForm.goodRockerArm"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <br />
        <el-form-item
          label="前悬挂系统(旧):"
          prop="goodFrontSuspension"
          label-width="128px"
        >
          {{ ruleForm.goodFrontSuspension }}
          <!-- <el-input
            readonly
            v-model="ruleForm.goodFrontSuspension"
            @change="isModifyStatus = true"
          /> -->
        </el-form-item>
        <br />
        <el-form-item label="前悬挂系统">
          <el-form-item label="结构类型" prop="goodFrontSuspensionType ">
            <el-select
              v-model="ruleForm.goodFrontSuspensionType"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in goodFrontSuspensionTypeList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodFrontSuspensionType = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="前悬挂气囊" prop="goodFrontSuspensionAirBag">
            <el-select
              v-model="ruleForm.goodFrontSuspensionAirBag"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAirbagList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodFrontSuspensionAirBag = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="品牌型号" prop="goodFrontSuspensionBrand">
            <el-input
              v-model="ruleForm.goodFrontSuspensionBrand"
              @change="isModifyStatus = true"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item label="前悬挂调节" prop="goodFrontSuspensionAdjust">
          <el-form-item label="预载" prop="goodFrontSuspensionMode">
            <el-select
              v-model="ruleForm.goodFrontSuspensionMode"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAdjustList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodFrontSuspensionMode = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="压缩阻尼" prop="goodFrontSuspensionCompress">
            <el-select
              v-model="ruleForm.goodFrontSuspensionCompress"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAdjustList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodFrontSuspensionCompress = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="回弹阻尼" prop="goodFrontSuspensionResilience">
            <el-select
              v-model="ruleForm.goodFrontSuspensionResilience"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAdjustList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodFrontSuspensionResilience = item"
              />
            </el-select>
          </el-form-item>
        </el-form-item>
        <el-form-item label="前悬挂直径/行程" label-width="130px">
          <el-form-item
            label="减震器内管直径 φ（mm）"
            prop="goodFrontSuspensionDiameter"
            label-width="180px"
          >
            <el-input
              v-model="ruleForm.goodFrontSuspensionDiameter"
              @change="isModifyStatus = true"
            />
          </el-form-item>
          <el-form-item
            label="前悬挂行程：行程（mm）"
            prop="goodFrontSuspensionTrip"
            label-width="180px"
          >
            <el-input
              v-model="ruleForm.goodFrontSuspensionTrip"
              @change="isModifyStatus = true"
            />
          </el-form-item>
        </el-form-item>
        <br />
        <el-form-item
          label="后悬挂系统(旧):"
          prop="goodRearSuspension"
          label-width="128px"
        >
          {{ ruleForm.goodRearSuspension }}
          <!-- <el-input
            readonly
            v-model="ruleForm.goodRearSuspension"
            @change="isModifyStatus = true"
          /> -->
        </el-form-item>
        <br />

        <el-form-item label="后悬挂系统">
          结构类型
          <el-form-item label="中置式" prop="goodBackSuspensionType">
            <el-select
              v-model="ruleForm.goodBackSuspensionType"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in structureMidList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodBackSuspensionType = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="边置式" prop="goodBackSuspensionSide">
            <el-select
              v-model="ruleForm.goodBackSuspensionSide"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in structureSideList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodBackSuspensionSide = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="后悬挂气囊 " prop="goodBackSuspensionAirBag">
            <el-select
              v-model="ruleForm.goodBackSuspensionAirBag"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAirbagList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodBackSuspensionAirBag = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="品牌型号" prop="goodBackSuspensionBrand">
            <el-input
              v-model="ruleForm.goodBackSuspensionBrand"
              @change="isModifyStatus = true"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item label="后悬挂调节" prop="goodRearSuspensionAdjust">
          <el-form-item label="预载" prop="goodBackSuspensionMode">
            <el-select
              v-model="ruleForm.goodBackSuspensionMode"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAdjustList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodBackSuspensionMode = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="压缩阻尼" prop="goodBackSuspensionCompress">
            <el-select
              v-model="ruleForm.goodBackSuspensionCompress"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAdjustList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodBackSuspensionCompress = item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="回弹阻尼" prop="goodBackSuspensionResilience">
            <el-select
              v-model="ruleForm.goodBackSuspensionResilience"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in suspensionAdjustList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.goodBackSuspensionResilience = item"
              />
            </el-select>
          </el-form-item>
        </el-form-item>
        <br />
        <el-form-item label="后悬挂行程">
          <el-form-item label="行程（mm）" prop="goodBackSuspensionTrip">
            <el-input
              v-model="ruleForm.goodBackSuspensionTrip"
              @change="isModifyStatus = true"
            />
          </el-form-item>
        </el-form-item>
        <br />
        <el-form-item class="lable-red" label="主油箱容量(L)" prop="goodOilBox">
          <el-input
            v-model="ruleForm.goodOilBox"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <el-form-item
          label="副油箱容量(L)"
          prop="viceGoodOilBox"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.viceGoodOilBox"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <el-form-item label="干重(kg)" prop="goodEmptyWeight">
          <el-input
            v-model="ruleForm.goodEmptyWeight"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          class="lable-red"
          label="整备质量(kg)"
          prop="goodAllWeight"
        >
          <el-input
            v-model="ruleForm.goodAllWeight"
            @change="isModifyStatus = true"
            placeholder="示例: 12，12.3"
          />
        </el-form-item>
        <el-form-item label="前倾角度(°)" prop="goodForwardAngle">
          <el-input
            v-model="ruleForm.goodForwardAngle"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="拖曳距(mm)" prop="goodDragDistance">
          <el-input
            v-model="ruleForm.goodDragDistance"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="最小转弯半径(m)"
          prop="goodMinTurn"
          label-width="120px"
        >
          <el-input
            v-model="ruleForm.goodMinTurn"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="最大有效载荷(kg)"
          prop="goodMaxLoad"
          label-width="130px"
        >
          <el-input
            v-model="ruleForm.goodMaxLoad"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <!-- <el-form-item label="仪表盘">
          <el-input
            v-model="ruleForm.goodDashboard"
            clearable
            @change="isModifyStatus = true"
          />
        </el-form-item> -->
        <br />
        <el-form-item label="可选颜色" prop="colorExps">
          <el-tooltip
            v-for="(item, index) in colorExpsList"
            :key="index"
            :content="item.name"
            :disabled="true"
            effect="dark"
            placement="top-start"
            style="margin: 5px"
          >
            <el-tag>{{ $filters.subString(item.name, 20) }}</el-tag>
          </el-tooltip>
        </el-form-item>
      </div>
      <div class="modules">
        <p class="modules-name">车轮制动</p>
        <el-form-item label="轮胎品牌" prop="brandModel" class="lable-border">
          <el-input
            v-model="ruleForm.brandModel"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="前轮规格" prop="goodFrontWheel">
          <el-input
            v-model="ruleForm.goodFrontWheel"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item class="lable-red" label="后轮规格" prop="goodBackWheel">
          <el-input
            v-model="ruleForm.goodBackWheel"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="轮胎形式" prop="goodTyreType">
          <el-select
            v-model="ruleForm.goodTyreType"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(value, index) in goodTyreTypeList"
              :key="index"
              :label="value"
              :value="value"
              @click="ruleForm.goodTyreType = value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="轮辋" prop="goodWheelRim">
          <el-select
            v-model="ruleForm.goodWheelRim"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(value, index) in goodWheelRimList"
              :key="index"
              :label="value"
              :value="value"
              @click="ruleForm.goodWheelRim = value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="前制动系统"
          prop="goodFrontBrake"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodFrontBrake"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item
          label="后制动系统"
          prop="goodRearBrake"
          class="lable-border"
        >
          <el-input
            v-model="ruleForm.goodRearBrake"
            @change="isModifyStatus = true"
          />
        </el-form-item>
      </div>
      <!-- 主/被动安全配置 -->
      <div class="modules">
        <p class="modules-name">主/被动安全配置</p>
        <el-form-item class="lable-red" label="ABS防抱死" label-width="120px">
          <el-select
            v-model="ruleForm.goodAbs"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.goodAbsStandard = ''
              }
            "
          >
            <el-option
              v-for="(value, index) in goodNewAbsConfigurationList"
              :key="index"
              :label="value"
              :value="value"
              @click="ruleForm.goodAbs = value"
            />
          </el-select>
          <el-select
            v-if="ruleForm.goodAbs === '标配'"
            v-model="ruleForm.goodAbsStandard"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(value, index) in goodAbsStandardList"
              :key="index"
              :label="value"
              :value="value"
              @click="ruleForm.goodAbsStandard = value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="lable-red" label="CBS联动刹车" label-width="130px">
          <el-select
            v-model="ruleForm.goodCBS"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(value, index) in goodAbsList"
              :key="index"
              :label="value"
              :value="value"
              @click="ruleForm.goodCBS = value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="ABS关闭功能" label-width="130px">
          <el-select
            v-model="ruleForm.aBSOnOff"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.aBSOnOffStandard = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.aBSOnOff = item"
            />
          </el-select>
          <el-select
            v-if="ruleForm.aBSOnOff && ruleForm.aBSOnOff == '标配'"
            v-model="ruleForm.aBSOnOffStandard"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in ['后轮可关闭']"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.aBSOnOffStandard = item
                  ruleForm.aBSOnOffOptional = ''
                }
              "
            />
          </el-select>
          <el-select
            v-if="ruleForm.aBSOnOff && ruleForm.aBSOnOff == '选配'"
            v-model="ruleForm.aBSOnOffOptional"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in ['后轮可关闭']"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.aBSOnOffOptional = item
                  ruleForm.aBSOnOffStandard = ''
                }
              "
            />
          </el-select>
        </el-form-item>
        <el-form-item label="弯道ABS(ABS PRO/ABS EVO等)" label-width="230px">
          <el-select
            v-model="ruleForm.curveABS"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.curveABS = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="胎压监测">
          <el-select
            v-model="ruleForm.tirePressureMonitoring"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.tirePressureMonitoring = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="牵引力控制" label-width="120px">
          <el-select
            v-model="ruleForm.tractionControlSystem"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.tractionControlSystemStandard = ''
                ruleForm.tractionControlSystemOptional = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.tractionControlSystem = item"
            />
          </el-select>
          <el-select
            v-if="
              ruleForm.tractionControlSystem &&
              ruleForm.tractionControlSystem == '标配'
            "
            v-model="ruleForm.tractionControlSystemStandard"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in ['可关闭']"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.tractionControlSystemStandard = item
                  ruleForm.tractionControlSystemOptional = ''
                }
              "
            />
          </el-select>
          <el-select
            v-if="
              ruleForm.tractionControlSystem &&
              ruleForm.tractionControlSystem == '选配'
            "
            v-model="ruleForm.tractionControlSystemOptional"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in ['可关闭']"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.tractionControlSystemOptional = item
                  ruleForm.tractionControlSystemStandard = ''
                }
              "
            />
          </el-select>
        </el-form-item>
        <el-form-item label="转向阻尼器" label-width="120px">
          <el-select
            v-model="ruleForm.steeringControlSystem"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.steeringControlSystem = item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="毫米波雷达">
          <el-select
            v-model="ruleForm.millimeterWaveRadar"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.millimeterWaveRadarDirectional = ''
                ruleForm.millimeterWaveRadarConfiguration = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.millimeterWaveRadar = item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.millimeterWaveRadar)"
            v-model="ruleForm.millimeterWaveRadarDirectional"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.millimeterWaveRadarConfiguration = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarDirectionalList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.millimeterWaveRadarDirectional = item"
            />
          </el-select>
          <!-- <el-select
            v-if="ruleForm.millimeterWaveRadarDirectional === '前向'"
            v-model="ruleForm.millimeterWaveRadarConfiguration"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarConfigurationPreList"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.millimeterWaveRadarConfiguration = item
                }
              "
            />
          </el-select>
          <el-select
            v-if="ruleForm.millimeterWaveRadarDirectional === '后向'"
            v-model="ruleForm.millimeterWaveRadarConfiguration"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in millimeterWaveRadarConfigurationAftList"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.millimeterWaveRadarConfiguration = item
                }
              "
            />
          </el-select> -->
        </el-form-item>
      </div>
      <!-- 辅助/操控配置 -->

      <div class="modules">
        <p class="modules-name">辅助/操控配置</p>
        <el-form-item label="模式选择">
          <search-label
            :type="'label'"
            class="search-label-content"
            @addLabel="addLabel"
          />
          <label-content
            ref="label"
            :labels="labels"
            :type="'label'"
            @deleteLabel="deleteLabel"
            @labelAllData="updataLabelAllData"
            @delLabel="delLabel"
          />
        </el-form-item>
        <el-form-item label="油门配置">
          <el-select
            v-model="ruleForm.electronicThrottle"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in electronicThrottleList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.electronicThrottle = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="快速换挡系统" label-width="100px">
          <el-select
            v-model="ruleForm.quickShiftSystem"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.quickShiftSystem = item"
            />
          </el-select>
          <el-select
            v-model="ruleForm.quickShiftSystemtype"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in quickShiftSystemList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.quickShiftSystemtype = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="电子悬挂">
          <el-select
            v-model="ruleForm.dynamicESA"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.dynamicESAStandard = ''
                ruleForm.dynamicESAOptional = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.dynamicESA = item"
            />
          </el-select>
          <el-select
            v-if="ruleForm.dynamicESA && ruleForm.dynamicESA == '标配'"
            v-model="ruleForm.dynamicESAStandard"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in ['标配自适应电子悬挂']"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.dynamicESAStandard = item
                  ruleForm.dynamicESAOptional = ''
                }
              "
            />
          </el-select>
          <el-select
            v-if="ruleForm.dynamicESA && ruleForm.dynamicESA == '选配'"
            v-model="ruleForm.dynamicESAOptional"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in ['标配自适应电子悬挂']"
              :key="index"
              :label="item"
              :value="item"
              @click="
                () => {
                  ruleForm.dynamicESAOptional = item
                  ruleForm.dynamicESAStandard = ''
                }
              "
            />
          </el-select>
        </el-form-item>
        <el-form-item label="定速巡航">
          <el-select
            v-model="ruleForm.cruiseControl"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.cruiseControl = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="液压离合器">
          <el-select
            v-model="ruleForm.fluidClutch"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.fluidClutch = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发动机启停">
          <el-select
            v-model="ruleForm.engineStartOrStop"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.engineStartOrStop = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="脉冲点火系统">
          <el-select
            v-model="ruleForm.pulseIgnitionSystem"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.pulseIgnitionSystem = item"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- 多媒体以及舒适性配置 -->
      <div class="modules">
        <p class="modules-name">多媒体以及舒适性配置</p>
        <el-form-item label="仪表盘">
          <el-input
            v-model="ruleForm.goodDashboard"
            clearable
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="手机连接功能(蓝牙/WIFI等)" label-width="190px">
          <div>
            <el-select
              v-model="ruleForm.electronicConnection"
              clearable
              @change="isModifyStatus = true"
            >
              <el-option
                v-for="(item, index) in keylessGoList"
                :key="index"
                :label="item"
                :value="item"
                @click="ruleForm.electronicConnection = item"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="导航投屏功能">
          <el-select
            v-model="ruleForm.navigationSystem"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList.concat(['简易导航'])"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.navigationSystem = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="挡位显示" class="lable-border">
          <el-select
            v-model="ruleForm.goodDriveGearShow"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(value, index) in keylessGoList"
              :key="index"
              :label="value"
              :value="value"
              @click="ruleForm.goodDriveGearShow = value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="USB充电口">
          <el-select
            v-model="ruleForm.uSBCharging"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in uSBChargingList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.uSBCharging = item"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="无钥匙启动">
          <el-select
            v-model="ruleForm.keylessGo"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.keylessGo = item"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="电加热手把">
          <el-select
            v-model="ruleForm.electricHeatingHandle"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.electricHeatingHandle = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="电加热座垫">
          <el-select
            v-model="ruleForm.electricHeatingSeatCushion"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in electricHeatingSeatCushionList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.electricHeatingSeatCushion = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="风挡" label-width="100px">
          <el-select
            v-model="ruleForm.windshieldModel"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.windshieldModel = item"
            />
          </el-select>
          <el-select
            v-model="ruleForm.windshieldType"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in windshieldTypeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.windshieldType = item"
            />
          </el-select>
          <el-select
            v-model="ruleForm.windshieldChecke"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in windshieldCheckeList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.windshieldChecke = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="OTA升级">
          <el-select
            v-model="ruleForm.dynamicOTA"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.dynamicOTA = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手机App远程控制" label-width="140px">
          <el-select
            v-model="ruleForm.mobileAppRemoteControlStandardOptional"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.mobileAppRemoteControlFeatures = ''
              }
            "
          >
            <el-option
              v-for="(
                item, index
              ) in mobileAppRemoteControlStandardOptionalList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.mobileAppRemoteControlStandardOptional = item"
            />
          </el-select>
          <el-select
            v-if="
              ['标配', '选配'].includes(
                ruleForm.mobileAppRemoteControlStandardOptional
              )
            "
            v-model="ruleForm.mobileAppRemoteControlFeatures"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in mobileAppRemoteControlFeaturesList"
              :key="index"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="无钥匙启动">
          <el-select
            v-model="ruleForm.keylessGo"
            clearable
            @change="
              () => {
                isModifyStatus = true
                ruleForm.keylessStartFeatures = ''
              }
            "
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.keylessGo = item"
            />
          </el-select>
          <el-select
            v-if="['标配', '选配'].includes(ruleForm.keylessGo)"
            v-model="ruleForm.keylessStartFeatures"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessStartFeaturesList"
              :key="index"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <!-- 灯光配置 -->
      <div class="modules">
        <p class="modules-name">灯光配置</p>
        <el-form-item class="lable-red" label="前灯" prop="goodHeadlights">
          <el-select
            v-model="ruleForm.goodHeadlights"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in lightsList.concat(['氙气'])"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodHeadlights = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="后灯" prop="goodTaillight">
          <el-select
            v-model="ruleForm.goodTaillight"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in lightsList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodTaillight = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="转向灯" prop="goodTurnlight">
          <el-select
            v-model="ruleForm.goodTurnlight"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in lightsList.slice(0, 2)"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodTurnlight = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="辅助灯" prop="goodAssistlight">
          <el-select
            v-model="ruleForm.goodAssistlight"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodAssistlight = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="危险警示灯(双闪)"
          prop="goodWarninglight"
          label-width="130px"
        >
          <el-select
            v-model="ruleForm.goodWarninglight"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodWarninglight = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="自动大灯">
          <el-select
            v-model="ruleForm.automaticHeadlamp"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in keylessGoList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.automaticHeadlamp = item"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- 选装包 -->
      <div class="modules">
        <p class="modules-name">选装包</p>
        <el-form-item label="选装包">
          <search-label
            :type="'label'"
            class="search-label-content"
            @addLabel="addLabelPack"
          />
          <label-content
            ref="labelsPack"
            :labels="labelsPack"
            :type="'label'"
            @deleteLabel="deleteLabelPack"
            @labelAllData="updataLabelAllDataPack"
            @delLabel="delLabelPack"
          />
        </el-form-item>
      </div>
      <!-- 其他配置 -->
      <div class="modules">
        <p class="modules-name">其他配置</p>
        <el-form-item label="上市时间" prop="goodTime" class="lable-border">
          <el-date-picker
            v-model="ruleForm.goodTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择日期时间"
            @change="isModifyStatus = true"
          />
        </el-form-item>
        <el-form-item label="生产状态" class="lable-border">
          <el-select
            v-model="ruleForm.goodSale"
            clearable
            @change="isModifyStatus = true"
          >
            <el-option
              v-for="(item, index) in goodSaleList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.goodSale = item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置/选项">
          <search-label
            :type="'label'"
            class="search-label-content"
            @addLabel="addLabelConfig"
          />
          <label-content
            ref="labelsConfig"
            :labels="labelsConfig"
            :type="'label'"
            @deleteLabel="deleteLabelConfig"
            @labelAllData="updataLabelAllDataConfig"
            @delLabel="delLabelConfig"
          />
        </el-form-item>
        <el-form-item label="产地" label-width="50px">
          <el-select
            v-model="ruleForm.productPlace"
            clearable
            @change="isModifyStatus = true"
            @clear="ruleForm.productPlace = ''"
          >
            <el-option
              v-for="(item, index) in productPlaceList"
              :key="index"
              :label="item"
              :value="item"
              @click="ruleForm.productPlace = item"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <image-dialog
      ref="ImageDialog"
      :more-fun-status="true"
      @sendData="getImageData"
      @refreshImage="refreshImage"
    />
    <video-dialog ref="VideoDialog" @sendData="getVideoData" />
    <!-- <choose-dialog ref="ChooseDialog" @sendData="getColorData" /> -->
    <choose-video ref="ChooseVideo" />

    <RealData ref="RealData" @sendData="getRealData"></RealData>
    <SeeLog ref="seeLog" @success="postReadyData" />
  </div>
</template>

<script>
import {
  ZoomIn as IconZoomIn,
  Delete as IconDelete
} from '@element-plus/icons-vue'
import { mapGetters } from 'vuex'
import { $emit } from '../../../../../utils/gogocodeTransfer'

import { ElLoading as Loading } from 'element-plus'
import ImageDialog from '@/components/Dialog/AliImageDialog.vue'
import VideoDialog from '@/components/Dialog/VideoDialog.vue'
import ChooseVideo from '@/components/Dialog/ChooseVideo.vue'
import searchLabel from '@/components/label/searchLabel.vue'
import labelContent from '@/components/label/labelContent.vue'
import RealData from './realData.vue'
import { deepCopy } from '@/utils'
import {
  updatePatternDetail,
  getAgeDetail,
  searchAgePattern,
  searchCarList
} from '@/api/garage'
import { getBrandDetail } from '@/api/brand'
import {
  onSaleEnum,
  appearMarket,
  produceWay,
  carOnSaleEnum
} from '@/utils/enum'
import { resetData } from '@/utils'
import {
  validatePositiveNumber,
  validatePositiveNumberOne,
  alidatePositiveInteger
} from '@/utils/validate'
import SeeLog from '../../components/SeeLog.vue'
import { carLog, setValue, setValueNumber } from '../../components/log.js'
import { convertKeyValueEnum } from '@/utils/convert'
import { postLog } from '@/components/seeLog/SaveLog.js'

export default {
  name: 'CNewGarage',
  components: {
    ImageDialog,
    VideoDialog,
    ChooseVideo,
    searchLabel,
    labelContent,
    RealData,
    IconZoomIn,
    IconDelete,
    SeeLog
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  emits: ['quit', 'getData'],
  data() {
    return {
      checkedWheel: [],
      checkedPedal: [],
      childWheelOptions: ['正三轮', '倒三轮', '边三轮'],
      pedalChildOptions: ['平踏', '非平踏'],
      labels: [], // 模式选择标签
      labelsPack: [], // 包装标签
      labelsConfig: [], // 其他配置标签
      motorCarOtherListedInfoENTList: [],
      checked: true,
      isCarYear: false, // 年代款是否传递
      loadingGarage: false,
      isOnSaleList: onSaleEnum,
      produceWayList: produceWay,
      appearMarket: appearMarket,
      goodAbsList: ['无', '标配', '选配'],
      goodNewAbsList: ['单前', '单后', '前后', '无'],
      goodAbsStandardList: ['标配前后', '标配单前', '标配单后'],
      goodNewAbsConfigurationList: ['无', '标配'], // ABS防抱死系统
      goodGasStructureList: ['DOHC', 'SOHC', 'OHC', 'OHV'],
      goodGasStructureNumList: [
        '每缸两气门',
        '每缸三气门',
        '每缸四气门',
        '每缸五气门',
        '每缸六气门'
      ],
      goodClutchModeStandardList: ['标配', '选配'], // 离合器标准列表
      needDriversLicenseList: ['需要', '无需驾照'],
      drivingLicenseList: ['D证', 'E证', 'F证'],
      goodEngineStructureList: ['直列', 'V型', 'L型', '水平对置'],
      intakeTypeList: ['自然吸气', '冲压进气', '机械增压', '涡轮增压'],
      // goodSpeedModeList: ['国际6挡', '国际5挡', 'DCT6速自动挡', 'DCT7速自动挡', 'CVT无级变速自动挡', '循环5挡', '循环4挡', '国际4挡', '前进4挡+1倒挡', '前进5挡+1倒挡', '前进6挡+1倒挡','往复式3挡','往复式4挡','往复式5挡','往复式6挡'],
      goodSpeedModeList: [
        {
          value: '国际挡',
          label: '国际挡',
          children: [
            {
              value: '国际6挡',
              label: '国际6挡'
            },
            {
              value: '国际5挡',
              label: '国际5挡'
            },
            {
              value: '国际4挡',
              label: '国际4挡'
            }
          ]
        },
        {
          value: 'CVT自动挡',
          label: 'CVT自动挡',
          children: [
            {
              value: 'CVT无级变速自动挡',
              label: 'CVT无级变速自动挡'
            }
          ]
        },
        {
          value: 'DCT自动挡',
          label: 'DCT自动挡',
          children: [
            {
              value: 'DCT3速自动挡',
              label: 'DCT3速自动挡'
            },
            {
              value: 'DCT4速自动挡',
              label: 'DCT4速自动挡'
            },
            {
              value: 'DCT5速自动挡',
              label: 'DCT5速自动挡'
            },
            {
              value: 'DCT6速自动挡',
              label: 'DCT6速自动挡'
            },
            {
              value: 'DCT7速自动挡',
              label: 'DCT7速自动挡'
            },
            {
              value: 'DCT8速自动挡',
              label: 'DCT8速自动挡'
            }
          ]
        },
        {
          value: 'AMT自动挡',
          label: 'AMT自动挡',
          children: [
            {
              value: 'AMT3速自动挡',
              label: 'AMT3速自动挡'
            },
            {
              value: 'AMT4速自动挡',
              label: 'AMT4速自动挡'
            },
            {
              value: 'AMT5速自动挡',
              label: 'AMT5速自动挡'
            },
            {
              value: 'AMT6速自动挡',
              label: 'AMT6速自动挡'
            },
            {
              value: 'AMT7速自动挡',
              label: 'AMT7速自动挡'
            }
          ]
        },
        {
          value: '循环挡',
          label: '循环挡',
          children: [
            {
              value: '循环5挡',
              label: '循环5挡'
            },
            {
              value: '循环4挡',
              label: '循环4挡'
            }
          ]
        },
        {
          value: '往复式挡',
          label: '往复式挡',
          children: [
            {
              value: '往复式6挡',
              label: '往复式6挡'
            },
            {
              value: '往复式5挡',
              label: '往复式5挡'
            },
            {
              value: '往复式4挡',
              label: '往复式4挡'
            },
            {
              value: '往复式3挡',
              label: '往复式3挡'
            }
          ]
        },
        {
          value: '含倒挡',
          label: '含倒挡',
          children: [
            {
              value: '前进6挡+1倒挡',
              label: '前进6挡+1倒挡'
            },
            {
              value: '前进5挡+1倒挡',
              label: '前进5挡+1倒挡'
            },
            {
              value: '前进4挡+1倒挡',
              label: '前进4挡+1倒挡'
            },
            {
              value: '前进3挡+1倒挡',
              label: '前进3挡+1倒挡'
            }
          ]
        },
        {
          value: '单速',
          label: '单速',
          children: [
            {
              value: '单速',
              label: '单速'
            }
          ]
        }
      ],

      goodMotorControlModeList: ['风冷', '水冷'], // 制动器冷却方式
      goodMotorControlTypeList: ['带风扇'], // 制动器冷却
      goodClutchModeList: [
        '湿式多片双离合',
        '湿式多片离合',
        '干式多片离合',
        '干式单片离合',
        '湿式多片滑动离合',
        '干式离心式离合',
        '湿式离心式离合',
        '干式多片滑动离合'
      ],
      lightsList: ['LED', '卤素', '激光'],
      goodTyreTypeList: ['真空胎', '非真空胎'],
      goodWheelRimList: [
        '整体式轮毂（铝合金铸造）',
        '整体式轮毂（铝合金锻造）',
        '整体式（钢制）',
        '辐条式轮毂（铝合金）',
        '辐条式轮毂（钢制）',
        '碳纤维'
      ],
      goodCoolDownList: ['风冷', '水冷', '油冷', '水冷+油冷'],
      goodElectricCoolDownList: ['风冷', '水冷'],
      goodDashboardList: ['机械', '电子', '组合屏'],
      goodCylinderList: [
        '单缸',
        '双缸',
        '三缸',
        '四缸',
        '五缸',
        '六缸',
        '八缸',
        '十缸',
        '十二缸',
        '多缸'
      ],
      goodSaleList: ['在产', '停产', '未知'],
      goodEnvStandardList: [
        '国Ⅱ',
        '国Ⅲ',
        '国Ⅳ',
        '国Ⅴ',
        '欧Ⅱ',
        '欧Ⅲ',
        '欧Ⅳ',
        '欧Ⅴ'
      ], // 环保标志
      goodMotorLayoutList: ['侧挂电机', '中置电机', '轮毂电机'], // 电机布局
      batteryList: [
        '三元锂电池',
        '铁锂电池',
        '锰锂电池',
        '铅酸电池',
        '燃料电池'
      ], // 电池类别
      goodTravelList: ['两冲程', '四冲程'], // 冲程列表
      goodOilModeList: ['电喷', '化油器'], // 供油方式列表
      goodOilBrand: '', // 供油品牌
      goodFuelList: ['92号', '95号', '98号'], // 燃油标号列表
      goodDriveTypeList: [
        '链条传动',
        '皮带传动',
        '轴传动',
        '电机传动',
        '齿轮传动'
      ], // 传动方式列表
      // slipClutchList:['无', '标配', '选配'], // 滑动离合器
      electronicClutchList: ['无', '标配', '选配'], // 动力传输-电子离合列表
      goodVoltageList: [
        '24V',
        '36V',
        '48V',
        '60V',
        '64V',
        '72V',
        '80V',
        '84V',
        '96V',
        '108V',
        '120V',
        '130V',
        '132V',
        '144V',
        '156V',
        '168V'
      ], // 电压列表
      fuelFormList: ['汽油', '纯电动', '柴油', '混动'], // 燃油形式
      // keylessGoList: ['无', '有'], // 无钥匙启动等
      keylessGoList: ['无', '标配', '选配'], // 无钥匙启动等
      electronicThrottleList: ['单拉线油门', '双拉线油门', '电子油门'], // 油门配置
      uSBChargingList: [
        '无',
        '标配 USB',
        '标配 DC12V',
        '标配 USB+DC12V',
        '选配 USB',
        '选配 DC12V',
        '选配 USB+DC12V'
      ], // USB充电口配置
      electricHeatingSeatCushionList: [
        '无',
        '标配 前后座',
        '标配 前座',
        '标配 后座',
        '选配 前后座',
        '选配 前座',
        '选配 后座'
      ],
      windshieldTypeList: ['不可调节', '手动', '电动'], // 风挡
      windshieldCheckeList: [
        '2段可调',
        '3段可调',
        '4段可调',
        '5段可调',
        '多段可调',
        '无级可调',
        '无级可调带记忆功能'
      ], // 风挡
      productPlaceList: [
        '中国',
        '中国台湾省',
        '日本',
        '韩国',
        '泰国',
        '菲律宾',
        '印度',
        '越南',
        '印尼',
        '马来西亚',
        '美国',
        '加拿大',
        '意大利',
        '德国',
        '法国',
        '英国',
        '捷克',
        '俄罗斯',
        '奥地利'
      ], //产地
      modeList: [
        '经济模式',
        '运动模式',
        '赛道模式',
        '雨天模式',
        '泥地模式',
        '雪地模式',
        '越野模式',
        '砂石路面'
      ], // 模式选择
      breakGoodBatteryGroupList: ['无', '标配'], //快拆电池组
      modeSelectionList: [], // 选择的模式
      quickShiftSystemList: ['双向', '仅升挡', '仅降挡'], // 快速换挡系统
      colorExpsList: [], // 颜色列表
      yearList: [], // 年代款信息列表
      energyType: [
        { value: 1, name: '电摩' },
        { value: 2, name: '电轻摩' },
        { value: 3, name: '电动自行车' }
      ], // 新能源类型

      compatibleWithChargingList: [
        '兼容直流汽车充电桩',
        '兼容交流汽车充电桩',
        '兼容直流/交流汽车充电桩'
      ], // 兼容充电
      goodBatterySelfHeatingList: ['无', '标配', '选配'], // 电池加热系统
      goodExternalDischargeList: ['无', '标配', '选配'], // 外放电功能
      suspensionAirbagList: ['外置悬挂气囊', '内置悬挂气囊'], // 悬挂气囊
      goodFrontSuspensionTypeList: [
        '正置式',
        '倒置式',
        '摇臂式',
        '双A臂',
        '双叉臂',
        '独立悬挂'
      ], // 前悬结构类型
      suspensionAdjustList: [
        '2段可调',
        '3段可调',
        '4段可调',
        '5段可调',
        '多段可调'
      ], // 悬挂调节
      structureSideList: ['单减震', '双减震'], // 结构类型 边置式
      structureMidList: ['单减震', '多连杆减震'], // 结构类型中置式
      goodsList: [], // 品牌列表
      imageTabList: null, // 分tab图片
      pullTypeCarId: '', // 拉取款型id
      millimeterWaveRadarList: ['无', '标配', '选配'],
      millimeterWaveRadarDirectionalList: ['前向', '后向', '前后向'],
      millimeterWaveRadarConfigurationPreList: [
        'ACC自适应巡航',
        'FCW前方碰撞预警'
      ],
      millimeterWaveRadarConfigurationAftList: [
        'BSD盲区监测',
        'RCW后方碰撞预警'
      ],
      mobileAppRemoteControlStandardOptionalList: ['无', '标配', '选配'],
      mobileAppRemoteControlFeaturesList: [
        '车辆监控',
        '远程控制',
        '充电管理',
        '服务预约',
        '智能寻车',
        '车辆分享'
      ],
      keylessStartFeaturesList: [
        'NFC/RFID钥匙',
        '手机蓝牙钥匙',
        '可穿戴钥匙',
        '智能遥控钥匙'
      ],
      ruleForm: {
        newEnergyTypes: '', // 新能源类型
        motorCarOtherListedInfoENTList: [],
        goodsCarNameNew: '', // 款型名称
        brandId: '', // 品牌Id
        brandName: '', // 品牌名称
        goodsPrice: '', // 价格
        isOnSale: '', // 在售状态
        goodsName: '', // 车型名称
        goodsId: '', // 车型id
        carName: '', // 年代款
        tags: [], // 标签列表
        colorChange: 0, // 颜色是否更改
        goodColors: '', // 颜色id，逗号分隔
        goodsImgs: '', // 图片json字符串
        goodsThumb: '', // 款型展图
        key: '', // 关键字输入框
        goodType: '', // 车型
        goodEngineStructure: '', // 发动机结构
        goodAbs: '', // ABS
        goodAbsStandard: '', // ABS标配
        goodAcrotOrque: '', // 最大扭矩
        goodMaxTorqueSpeed: '', // 最大扭矩转速
        goodAllWeight: '', // 整备质量
        goodBackWheel: '', // 后轮规格
        goodBreakMetre: '', // 破百时间
        goodClearance: '', // 最小离地间隙
        goodClutchMode: '', // 离合器型式
        goodCoolDown: '', // 油车冷却方式
        goodElectricCoolDown: '', // 电车冷却方式
        officialRealGoodBreak50Metre: '', // 官方0-50km/h加速
        realGoodBreak50Metre: '', // 实测0-50km/h加速
        goodMotorPower: '', // 电机额定功率
        goodCylinder: '', // 缸数
        goodCylinderStroke: '', // 缸径x行程
        goodBore: '', // 行程
        goodStroke: '', // 行程
        goodDashboard: '', // 仪表盘
        goodDragDistance: '', // 拖拽距
        goodDriveType: '', // 传动方式
        electronicClutch: '', // 电子离合
        goodEmptyWeight: '', // 干重
        goodEndurance: '', // 续航里程
        goodConstantSpeedEndurance: '', // 等速续航里程
        batteryWarrantyMonth: '', // 动力电池质保
        frameWarrantyMonth: '', // 车架质保
        motorWarrantyMonth: '', // 电机质保
        goodEnvStandard: '', // 环保标准
        goodExactVolume: '', // 精确排量
        goodForwardAngle: '', // 前倾角度
        goodFrame: '', // 车架型式
        goodFrontBrake: '', // 前制动系统
        goodFrontSuspension: '', // 前悬挂系统
        goodFrontSuspensionType: '', // 前悬结构类型
        goodFrontSuspensionAirBag: '', // 前悬挂气囊
        goodFrontSuspensionBrand: '', // 前悬挂品牌型号
        goodFrontSuspensionMode: '', // 前悬挂预载
        goodFrontSuspensionResilience: '', // 前悬挂回弹
        goodFrontSuspensionCompress: '', // 前悬挂压缩
        goodFrontSuspensionDiameter: '', // 前悬挂减震筒直径
        goodFrontSuspensionTrip: '', // 前悬挂行程
        goodBackSuspensionType: '', // 后悬结构中
        goodBackSuspensionSide: '', // 后悬结构侧
        goodBackSuspensionAirBag: '', // 后悬挂气囊
        goodBackSuspensionBrand: '', // 后悬挂品牌型号
        goodBackSuspensionMode: '', // 后悬挂预载
        goodBackSuspensionResilience: '', // 后悬挂回弹
        goodBackSuspensionCompress: '', // 后悬挂压缩
        goodBackSuspensionTrip: '', // 后悬挂行程
        dynamicESAStandard: '', // 电子悬挂标配 ---标配自适应电子悬挂
        dynamicESAOptional: '', // 电子悬挂选配 --标配自适应电子悬挂
        dynamicOTA: '', //多媒体以及舒适性配置
        goodRearWheel: '', // 前轮规格
        goodFuel: '', // 燃油标号
        goodGasStructure: '', // 配气结构
        goodGasStructureNum: '', // 配气门数
        goodHeadlights: '', // 前灯
        goodMaxHorse: '', // 最大马力
        goodMaxLoad: '', // 最大有效载荷
        goodMaxPower: '', // 最大功率
        goodMaxPowerSpeed: '', // 最大功率转速
        goodMaxSpeed: '', // 最高车速
        goodMinTurn: '', // 最小转弯半径
        goodOilBox: '', // 油箱容量
        goodOilMode: '', // 供油方式
        goodOilWear: '', // 平均油耗
        goodRearBrake: '', // 后制动系统
        goodRearSuspension: '', // 后悬挂系统
        goodReductRatio: '', // 压缩比
        goodRockerArm: '', // 后摇臂
        goodSaddleHigh: '', // 座高
        goodSale: '', // 生产状态
        goodSize: '', // 长x宽x高
        long: '',
        width: '',
        high: '',
        goodSpeedMode: '', // 变速器型式 CVT无极变速自动挡
        goodTaillight: '', // 后灯
        goodTechnology: '', // 技术
        goodTime: '', // 上市时间
        goodTravel: '', // 冲程
        goodTyreType: '', // 轮胎形式
        goodVolume: '', // 排量
        goodWheelBase: '', // 轴距
        goodWheelNums: '',
        goodWheelRim: '', // 轮辋
        goodBatteryType: '', // 电池类型
        goodBatteriesType: '', // 电芯类型
        goodBatterySpecification: '', // 电池规格
        goodVoltage: '', // 电压(V)
        goodBatteryCapacity: '', // 电池容量(Ah)
        goodBatteryEnergy: '', // 电池能量
        goodBatteryCompatible: '', // 兼容充电器
        goodBatteryTearOpen: '', // 快拆电池组数
        goodBatteryNum: '', // 电池组数
        goodBatteryWeight: '', // 电池重量(kg)
        goodChargingTime: '', // 充电时间(h)
        goodQuickCharge: '', // 快充时间(h)
        goodStandardCharging: '', // 标准充电电流(A)
        goodCycleCharge: '', // 循环充电次数
        goodTemperatureRange: '', // 放电耐温范围(℃)
        goodMotorBrand: '', // 电机品牌
        goodMotorLayout: '', // 电机布局
        goodBiggestClimbing: '', // 最大爬坡(°)
        goodMotorControl: '', // 电机控制方式
        goodMotorControlMode: '', // 控制器冷却方式
        goodMotorControlType: '', // 控制器类型 风风冷是 带风扇否
        goodBatterySelfHeating: '', // 电池自放电
        goodExternalDischarge: '', // 电池外部放电
        goodBatteryChargingPort: '', // 充电接口
        goodElectricEfficiency: '', // 电机效率
        goodDriveGear: '', // 动力挡位
        goodMotorWaterproof: '', // 电机防水
        goodKilometersElectricity: '', // 百公里电耗(kW·h/km)
        goodBatterySize: '', // 电池包尺寸(mm)
        goodChargerSize: '', // 充电器尺寸(mm)
        goodCBS: '', // CBS
        goodProductType: '', // 生产方式
        productPlace: '', // 产地
        standard: '', // 款型标准化
        goodTemperatureRangeRight: '', // 耐温
        engineType: '', // 发动机型号
        drivingLicenseName: '', // 行驶证名称
        goodMotorMaxHorse: '', // 电机最大马力
        discount: '', // 活动价
        manualName: '',
        manualUrl: '',

        labelChange: 0, // 标签是否更改
        modeSelection: '', // 模式选择标签
        labelsPack: '', // 包装标签
        labelsConfig: '', // 其他配置标签
        factoryName: '', // 厂家
        vehicleWarranty: '', //
        needDriversLicense: '', //驾考政策
        driversType: '', //需要证件类型
        viceGoodOilBox: '', // 副油箱容量(L)
        intakeType: '自然吸气', // 进气形式
        brandModel: '', // 品牌/型号
        steeringControlSystem: '', // 转向阻尼器
        quickShiftSystem: '', // 快速换挡系统
        quickShiftSystemtype: '', // 快速换挡系统
        engineStartOrStop: '', // 发动机启停
        pulseIgnitionSystem: '', // 脉冲点火系统
        electronicConnection: '', // 手机连接功能(蓝牙/WIFI等)
        navigationSystem: '', // 导航投屏功能
        windshieldModel: '', // 风挡
        windshieldType: '', // 风挡
        windshieldChecke: '', // 风挡
        goodTurnlight: '', // 转向灯
        goodAssistlight: '', // 辅助灯
        goodWarninglight: '', // 危险警示灯(双闪)
        newCarOnsaleLable: '', // 是否是新车上市
        newCarOnsaleEndTime: '', // 新车上市截止时间
        tractionControlSystem: '', // 牵引力控制系统
        tractionControlSystemStandard: '', // 牵引力控制系统 标配
        tractionControlSystemOptional: '', // 牵引力控制系统 选配
        millimeterWaveRadar: '', // 毫米波雷达
        millimeterWaveRadarDirectional: '', // 毫米波雷达方向
        millimeterWaveRadarConfiguration: '', // 毫米波雷达配置
        // mobileAppRemoteControl: '', // 手机APP远程控制
        mobileAppRemoteControlStandardOptional: '', // 手机APP远程控制 标配 选配
        mobileAppRemoteControlFeatures: '', // 手机APP远程控制 功能
        // keylessStart: '', // 无钥匙启动
        keylessGo: '', // 无钥匙启动 标配 选配
        keylessStartFeatures: '' // 无钥匙启动 功能
      }, // 款型属性详情数据
      goodSpeedMode: [], // 变速器型式中转字段
      rules: {},
      isModifyStatus: false, // 是否有修改
      isEditStatus: false, // 是否是编辑状态
      loading: false, // 加载状态
      isPostDataStatus: false, // 是否在发送数据状态
      checkedCategory: [], // 被选中的车型
      carCategoryList: [
        '跨骑',
        '街车',
        '跑车',
        '旅行',
        '拉力',
        '越野',
        '巡航太子',
        '复古',
        '弯梁',
        '踏板',
        'MINI',
        '三轮',
        '其他'
      ],
      videoList: [], // 视频列表
      showVideoList: [], // 展示的视频列表
      disableSubmit: false, // 是否禁用发送请求
      isAddType: false, // 是否是款型新增
      copyGarage: '', // 是否复制款型，值： ''、true、false
      buttonType1: 'primary', // 推送button按钮类型
      buttonType2: 'primary', // button按钮类型
      checkList: [],
      manualName: '',
      manualUrl: '',
      modelHeight: '', // 模特身高
      // sittingHeight: '', // 座高
      factoryNotProvide: 0, // 厂家暂未提供参数配置
      updateData: {}, // 更新数据
      groupIds: '' //组别ids
    }
  },
  computed: {
    ...mapGetters(['uid']),
    goodMaxHorse() {
      return (
        this.ruleForm.goodMaxPower &&
        (Number(this.ruleForm.goodMaxPower) * 1.3596216).toFixed(1)
      )
    },
    goodMotorMaxHorse() {
      return (
        this.ruleForm.goodMotorMaxPower &&
        (Number(this.ruleForm.goodMotorMaxPower) * 1.3596216).toFixed(1)
      )
    },
    // 纯电动
    isElectric() {
      return (
        this.ruleForm.energyType == 2 ||
        this.ruleForm.energyType == '纯电动' ||
        this.ruleForm.engineType == '纯电动'
      )
    }
  },
  watch: {
    isModifyStatus(value) {
      $emit(this, 'quit', value)
      $emit(this, 'getData', this.ruleForm)
    }

    // // 【变速器型式】选择：“CVT无极变速”时，【离合器型式】显示为：“干式离心式离合”
    // 'ruleForm.goodSpeedMode'() {
    //   // console.log(this.ruleForm.goodSpeedMode)
    //   if (this.ruleForm.goodSpeedMode === 'CVT无级变速') {
    //     this.ruleForm.goodClutchMode = '干式离心式离合'
    //   }
    // }
  },
  activated() {
    this.copyGarage = ''
    this.imageTabList = null
    this.buttonType1 = 'primary'
    this.buttonType2 = 'primary'
    if (!window.jQuery) {
      window.loadJs('/static/js/jquery.min.js')
    }
  },
  methods: {
    changeisOnSale() {
      this.isModifyStatus = true
      this.ruleForm.newCarOnsaleLable = ''
      this.ruleForm.newCarOnsaleEndTime
    },
    handleCheckedWheelChange(val) {
      if (val.length) {
        this.checkedWheel = [val[val.length - 1]]
        this.checkedCategory = ['三轮']
        this.checkedPedal = []
      }
    },
    handleCheckedPedalChange(val) {
      if (val.length) {
        this.checkedPedal = [val[val.length - 1]]
        this.checkedCategory = ['踏板']
        this.checkedWheel = []
      }
    },
    // 实测数据
    initRealData() {
      this.$refs['RealData'].init({
        realForm: this.ruleForm.carMeasuredInfoEnt || '',
        oilNum:
          Number(this.ruleForm.goodOilBox) +
          Number(this.ruleForm.viceGoodOilBox)
      })
    },
    // 获取实测数据
    getRealData(info) {
      console.log(info, 'info====')
      this.ruleForm.carMeasuredInfoEnt = JSON.stringify(info) || ''
    },

    async httpRequest(option) {
      option.imageType = 'nowater'
      option.quality = 1
      option.notCompress = true
      this.$oss.ossUploadImage(option)
    },
    // ======模式选择======
    // 增加标签
    addLabel(data, type) {
      const me = this
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
      this.$refs.label.addLable(data)
      setTimeout(() => {
        me.$refs.label.getAllData()
      }, 800)
    },
    // 更新关联标签状态
    deleteLabel(item, type) {
      const me = this
      me.labels &&
        me.labels?.map(function (value) {
          if (value.labelId === item.labelId || value.labelId === item.id) {
            value.selected = false
          }
        })
      setTimeout(() => {
        me.$refs.label.getAllData()
      }, 800)
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
    },
    // 删除标签
    delLabel(data) {
      this.ruleForm.labelChange = 1
      this.isModifyStatus = true
      this.labels = data
    },
    // 修改快捷标签
    updataLabelAllData(data) {
      this.labels = []
      this.labels = data
    },

    // ======包装======
    // 增加标签
    addLabelPack(data, type) {
      const me = this
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
      this.$refs.labelsPack.addLable(data)
      setTimeout(() => {
        me.$refs.labelsPack.getAllData()
      }, 800)
    },
    // 更新关联标签状态
    deleteLabelPack(item, type) {
      const me = this
      me.labelsPack &&
        me.labelsPack?.map(function (value) {
          if (value.labelId === item.labelId || value.labelId === item.id) {
            value.selected = false
          }
        })
      setTimeout(() => {
        me.$refs.labelsPack.getAllData()
      }, 800)
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
    },
    // 删除标签
    delLabelPack(data) {
      this.ruleForm.labelChange = 1
      this.isModifyStatus = true
      this.labelsPack = data
    },
    // 修改快捷标签
    updataLabelAllDataPack(data) {
      this.labelsPack = []
      this.labelsPack = data
    },

    // ======配置选项======
    // 增加标签
    addLabelConfig(data, type) {
      const me = this
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
      this.$refs.labelsConfig.addLable(data)
      setTimeout(() => {
        me.$refs.labelsConfig.getAllData()
      }, 800)
    },
    // 更新关联标签状态
    deleteLabelConfig(item, type) {
      const me = this
      me.labelsConfig &&
        me.labelsConfig?.map(function (value) {
          if (value.labelId === item.labelId || value.labelId === item.id) {
            value.selected = false
          }
        })
      setTimeout(() => {
        me.$refs.labelsConfig.getAllData()
      }, 800)
      me.ruleForm.labelChange = 1
      me.isModifyStatus = true
    },
    // 删除标签
    delLabelConfig(data) {
      this.ruleForm.labelChange = 1
      this.isModifyStatus = true
      this.labelsConfig = data
    },
    // 修改快捷标签
    updataLabelAllDataConfig(data) {
      this.labelsConfig = []
      this.labelsConfig = data
    },

    handleDelete(item) {
      const me = this
      // me.ruleForm.motorCarOtherListedInfoENTList = me.motorCarOtherListedInfoENTList
      // const index = me.ruleForm.motorCarOtherListedInfoENTList.findIndex((obj) => {
      //   if (obj.listedCountry === item.listedCountry && obj.listedPrice === item.listedPrice) {
      //     return true;
      //   }
      // });
      const index = me.motorCarOtherListedInfoENTList.findIndex((obj) => {
        if (
          obj.listedCountry === item.listedCountry &&
          obj.listedPrice === item.listedPrice
        ) {
          return true
        }
      })
      me.motorCarOtherListedInfoENTList.splice(index, 1)
      // me.ruleForm.motorCarOtherListedInfoENTList.splice(index, 1);
    },
    // 上传文件
    async httpRequest1(option) {
      option.isDocument = true // 是文件
      option.businessType = 4
      this.$oss.ossUploadFile(option)
    },
    onSuccess1(res, file, fileList) {
      console.log(res, file, fileList)
      if (!res) return
      this.manualName = res.fileName
      this.manualUrl = res.url
      this.ruleForm.manualUrl = res.url
      this.ruleForm.manualName = res.fileName
    },
    removeFile() {
      this.manualName = ''
      this.manualUrl = ''
      this.ruleForm.manualUrl = ''
      this.ruleForm.manualName = ''
    },
    handlePictureCardPreview(file) {
      // 直接跳转
      window.open(this.manualUrl)
    },
    increaseListing() {
      const target = { listedCountry: '', listedPrice: '' }
      this.motorCarOtherListedInfoENTList.push(target)
    },
    resetData() {
      resetData(this)
    },
    // 标签处理
    dealLable() {
      const me = this
      // 清除标签
      me.$refs.label.deleteAllLabel()
      me.$refs.labelsPack.deleteAllLabel()
      me.$refs.labelsConfig.deleteAllLabel()
      // 模式标签
      if (me.ruleForm.modeSelection) {
        const labels = setLabel(JSON.parse(me.ruleForm.modeSelection))
        me.labels = labels
        setTimeout(() => {
          me.$refs.label.addLable(labels)
        }, 500)
      }
      // 包装标签
      if (me.ruleForm.labelsPack) {
        const labelsPack = setLabel(JSON.parse(me.ruleForm.labelsPack))
        me.labelsPack = labelsPack
        setTimeout(() => {
          me.$refs.labelsPack.addLable(labelsPack)
        }, 500)
      }
      // 配置标签
      if (me.ruleForm.labelsConfig) {
        const labelsConfig = setLabel(JSON.parse(me.ruleForm.labelsConfig))
        me.labelsConfig = labelsConfig
        setTimeout(() => {
          me.$refs.labelsConfig.addLable(labelsConfig)
        }, 500)
      }
      function setLabel(label) {
        label?.map(function (value) {
          value.labelId = value.id
        })
        return label
      }
    },
    // 变速器形式处理
    dealSpeedMode(str) {
      const me = this
      if (str) {
        me.goodSpeedModeList?.map((item) => {
          if (!item.children || item.children.length === 0) {
            if (item.value === str) {
              me.goodSpeedMode = [item.value, str]
            }
          }
          item.children?.map((it) => {
            if (it.value === str) {
              me.goodSpeedMode = [item.value, str]
            }
          })
        })
      } else {
        me.goodSpeedMode = []
      }
    },
    // 新建时，获取本地存储数据
    obtainData(data) {
      const me = this
      me.resetData()
      me.isAddType = me.$route.query.type === 'addType'
      me.pullTypeCarId = ''
      me.videoList = []
      me.labels = []
      me.labelsPack = []
      me.labelsConfig = []
      me.ruleForm.modeSelection = ''
      me.ruleForm.labelsPack = ''
      me.ruleForm.labelsConfig = ''
      me.ruleForm.carYear = me.$route.query.carYear || data.carYear || ''
      me.isCarYear = !!me.$route.query.carYear
      Object.assign(me.ruleForm, data)
      // 车型
      me.checkedCategory =
        (me.ruleForm.goodType && me.ruleForm.goodType.split(',')) ||
        me.checkedCategory
      // 颜色
      const imgArr = []
      if (me.ruleForm.colorExps) {
        me.ruleForm.colorExps?.map((item) => {
          let obj = {}
          if (item.image) {
            item.image.split(',')?.map((e, index) => {
              if (index === 0) {
                obj.image = e
              } else {
                obj['image' + index] = e
              }
            })
          }
          obj = { ...item, ...obj }
          imgArr.push(obj)
        })
      }
      me.colorExpsList = imgArr
      // 标签处理
      me.dealLable()
      // 变速器形式处理
      me.dealSpeedMode(me.ruleForm.goodSpeedMode)

      // const updataValueList = ['keylessGo', 'uSBCharging', 'cruiseControl', 'dynamicESA', 'automaticHeadlamp', 'electricHeatingHandle', 'electricHeatingSeatCushion', 'tirePressureMonitoring',
      //  'electronicThrottle', 'fluidClutch', 'aBSOnOff', 'curveABS', 'tractionControlSystem', 'quickShiftSystem']
      const updataValueList = [
        'keylessGo',
        'uSBCharging',
        'cruiseControl',
        'dynamicESA',
        'automaticHeadlamp',
        'electricHeatingHandle',
        'electricHeatingSeatCushion',
        'tirePressureMonitoring',
        'fluidClutch',
        'aBSOnOff',
        'curveABS',
        'tractionControlSystem',
        'quickShiftSystem'
      ]
      updataValueList?.map((_) => {
        me.ruleForm[_] = me.ruleForm[_] ? me.ruleForm[_] : '无'
      })
      if (me.$route.query.brandId) {
        // 如果新建款型的时候有品牌id 获取这个品牌的生产方式
        getBrandDetail(me.$route.query.brandId)
          .then((response) => {
            me.ruleForm.goodProductType = response.data.data.productSource
          })
          .catch(() => {})
        const energyType = me.$route.query && me.$route.query.energyType
        me.ruleForm.fuelForm = ['汽油', '柴油'].includes(energyType)
          ? energyType
          : ''
        me.ruleForm.engineType = ['纯电动', '柴油'].includes(energyType)
          ? energyType
          : ''
      }
      me.getAgeList()
    },
    // 编辑时，插入数据
    init(data) {
      const me = this
      me.isAddType = me.$route.query.type === 'addType'
      me.labels = []
      me.labelsPack = []
      me.labelsConfig = []
      me.ruleForm.modeSelection = ''

      me.ruleForm.labelsPack = ''
      me.ruleForm.labelsConfig = ''
      me.ruleForm.long = ''
      me.ruleForm.width = ''
      me.ruleForm.high = ''
      me.ruleForm.carYear = me.$route.query.carYear || data.carYear || ''
      me.isCarYear = !!me.$route.query.carYear
      me.isEditStatus = true
      me.modelHeight = data.modelHeight // 模特身高
      // me.sittingHeight = data.goodSaddleHigh // 座高
      data.goodVoltage = data.goodVoltage
        ? data.goodVoltage.replace(/V/, '')
        : ''
      // 电池额定容量
      data.goodBatteryCapacity = data.goodBatteryCapacity
        ? data.goodBatteryCapacity.replace(/Ah/, '')
        : ''
      data.newEnergyTypes = data.newEnergyTypes || ''
      me.ruleForm = {
        ...me.ruleForm,
        ...data
      }
      // 使用说明书
      me.manualName = data.manualName ? data.manualName : ''
      me.manualUrl = data.manualUrl ? data.manualUrl : ''
      me.ruleForm.manualName = data.manualName ? data.manualName : ''
      me.ruleForm.manualUrl = data.manualUrl ? data.manualUrl : ''
      me.motorCarOtherListedInfoENTList =
        me.ruleForm.motorCarOtherListedInfoENTList
      // 车型
      me.checkedCategory =
        (me.ruleForm.goodType && me.ruleForm.goodType.split(',')) ||
        me.checkedCategory
      me.checkedWheel = me.ruleForm.tricycle ? [me.ruleForm.tricycle] : []
      me.checkedPedal = me.ruleForm.footBoard ? [me.ruleForm.footBoard] : []
      // 颜色
      const imgArr = []
      if (me.ruleForm.colorExps) {
        me.ruleForm.colorExps?.map((item) => {
          let obj = {}
          if (item.image) {
            item.image.split(',')?.map((e, index) => {
              if (index === 0) {
                obj.image = e
              } else {
                obj['image' + index] = e
              }
            })
          }
          obj = { ...item, ...obj }
          imgArr.push(obj)
        })
      }
      me.colorExpsList = imgArr

      // me.colorExpsList = me.ruleForm.colorExps
      // 是否
      me.checkList = me.ruleForm.standard
      me.factoryNotProvide = me.ruleForm.factoryNotProvide || 0
      // 标签处理
      me.dealLable()
      // 变速器形式处理
      me.dealSpeedMode(me.ruleForm.goodSpeedMode)
      // 配气结构
      const findGoodGasStructure = me.goodGasStructureList.findIndex((_) => {
        return me.ruleForm.goodGasStructure.indexOf(_) > -1
      })
      const findGoodGasStructureNum = me.goodGasStructureNumList.findIndex(
        (_) => {
          return me.ruleForm.goodGasStructure.indexOf(_) > -1
        }
      )
      me.ruleForm.goodGasStructureNum =
        findGoodGasStructureNum > -1 && findGoodGasStructure > -1
          ? me.ruleForm.goodGasStructure.split(
              me.goodGasStructureList[findGoodGasStructure]
            )[1]
          : ''
      me.ruleForm.goodGasStructureNum =
        findGoodGasStructureNum > -1 && findGoodGasStructure === -1
          ? me.goodGasStructureNumList[findGoodGasStructureNum]
          : me.ruleForm.goodGasStructureNum // 只选后面气门
      me.ruleForm.goodGasStructureNew =
        findGoodGasStructure > -1
          ? me.goodGasStructureList[findGoodGasStructure]
          : ''
      me.ruleForm.goodTemperatureRange =
        me.ruleForm.goodTemperatureRange.indexOf('~') > -1
          ? me.ruleForm.goodTemperatureRange.split('~')[0]
          : me.ruleForm.goodTemperatureRange
      // 压缩比
      me.ruleForm.goodReductRatio = me.ruleForm.goodReductRatio.split(':1')[0]
      // 模式
      me.modeSelectionList = me.ruleForm.modeSelection.length
        ? me.ruleForm.modeSelection.split(',')
        : []
      const updataValueList = [
        'keylessGo',
        'uSBCharging',
        'cruiseControl',
        'dynamicESA',
        'automaticHeadlamp',
        'electricHeatingHandle',
        'electricHeatingSeatCushion',
        'tirePressureMonitoring',
        'fluidClutch',
        'aBSOnOff',
        'curveABS',
        'tractionControlSystem',
        'quickShiftSystem'
      ]
      updataValueList?.map((_) => {
        me.ruleForm[_] = me.ruleForm[_] ? me.ruleForm[_] : '无'
      })
      if (!me.modeSelectionList.length) me.updataModeList()
      me.videoList = []
      me.showVideoList = me.ruleForm.videoList || []
      this.ruleForm.content = me.ruleForm.videoList.length
        ? JSON.stringify(me.ruleForm.videoList)
        : ''
      me.getAgeList()
    },
    // 获取年代款列表
    getAgeList() {
      // 获取年代款列表
      if (
        !this.ruleForm.goodsId &&
        !this.$route.query &&
        this.$route.query.id
      ) {
        return
      }
      searchAgePattern({
        goodsId:
          this.ruleForm.goodsId || (this.$route.query && this.$route.query.id)
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.yearList = response.data.data
            if (!this.ruleForm.carName) {
              this.ruleForm.carName = this.yearList.length
                ? this.yearList[0].carName
                : this.ruleForm.carName || ''
            }
          }
        })
        .finally((_) => {})
    },
    // 远程搜索车型名称
    remoteMethod(data) {
      if (!data || data === true) {
        return
      }
      const me = this
      me.goodsList = []
      searchCarList({
        name: data,
        limit: 20,
        page: 1
      }).then((response) => {
        if (response.data.code === 0) {
          const res = response.data.data
          res.list?.map(function (value) {
            const newObj = {
              labelName: value.goodName,
              labelId: value.goodId,
              brandId: value.brandId,
              energyType: value.energyType
            }
            me.goodsList.push(newObj)
          })
        }
      })
    },
    // 变更车型名称
    addBrand(item) {
      const me = this
      me.ruleForm.carName = ''
      me.yearList = []
      me.ruleForm.goodsName = item || ''
      me.ruleForm.goodsId = ''
      me.ruleForm.brandName = ''
      me.ruleForm.energyType = ''
      me.ruleForm.fuelForm = ''
      me.ruleForm.engineType = ''
      if (me.ruleForm.goodsName) {
        me.ruleForm.goodsId = me.goodsList.filter(
          (_) => _.labelName === me.ruleForm.goodsName
        )[0].labelId
        me.ruleForm.brandId = me.goodsList.filter(
          (_) => _.labelName === me.ruleForm.goodsName
        )[0].brandId
        me.ruleForm.brandName = me.goodsList.filter(
          (_) => _.labelName === me.ruleForm.goodsName
        )[0].brandName
        me.ruleForm.energyType = me.goodsList.filter(
          (_) => _.labelName === me.ruleForm.goodsName
        )[0].energyType
        const energyType = me.fuelFormList[me.ruleForm.energyType - 1]
        me.ruleForm.fuelForm = ['汽油', '柴油'].includes(energyType)
          ? energyType
          : ''
        me.ruleForm.engineType = ['纯电动', '柴油'].includes(energyType)
          ? energyType
          : ''
        me.getAgeList()
      }
    },
    getDetail(type) {
      const me = this
      if (!me.pullTypeCarId) {
        me.copyGarage = ''
        return
      }

      me.copyGarage = type
      me.loadingGarage = true
      getAgeDetail({
        carId: me.pullTypeCarId
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            if (data && data.attr) {
              Object.keys(data.attr)?.map((name) => {
                data[name] = data.attr[name] === 'null' ? '' : data.attr[name]
              })
              delete data.attr
            }
            if (me.copyGarage) {
              delete data.carId
              // delete data.carName
              delete data.goodsName
              delete data.goodImgs
              delete data.goodThumb
              delete data.brandId
              delete data.brandName
              delete data.modifyTime
              delete data.goodsThumb
              delete data.pv
              delete data.videoList
              delete data.carMeasuredInfoEnt
            }
            data.goodsId = me.$route.query.id || data.goodsId
            const boreIndex =
              data.goodCylinderStroke && data.goodCylinderStroke.indexOf('x')
            data.goodBore =
              data.goodCylinderStroke &&
              data.goodCylinderStroke.slice(0, boreIndex)
            data.goodStroke =
              data.goodCylinderStroke &&
              data.goodCylinderStroke.slice(boreIndex + 1)
            const ReductRatioIndex =
              data.goodReductRatio && data.goodReductRatio.lastIndexOf(':')
            data.goodReductRatio =
              data.goodReductRatio &&
              data.goodReductRatio.slice(0, ReductRatioIndex)
            data.standard = (data.standard && data.standard.split(',')) || []
            if (data.goodSize) {
              const goodSizeArr =
                data.goodSize.indexOf('x') > -1
                  ? data.goodSize.split('x')
                  : data.goodSize.split('×') // ×为乘号 不要输入x
              data.long = goodSizeArr[0]
              data.width = goodSizeArr[1]
              data.high = goodSizeArr[2]
            }
            me.motorCarOtherListedInfoENTList =
              data.motorCarOtherListedInfoENTList
            me.init(data)
          }
        })
        .finally(() => {
          me.loadingGarage = false
        })
    },
    // 保存
    goSaveData() {
      const me = this
      let postData = deepCopy(me.ruleForm)
      if (!postData.goodsCarNameNew) {
        return me.$message.error('请输入款型名称')
      }
      if (postData.isOnSale === undefined) {
        return me.$message.error('请选择在售状态')
      }
      if (!postData.carYear) {
        return me.$message.error('请选择年代款名称')
      }
      if (!postData.goodsThumb) {
        return me.$message.error('请上传车型展图')
      }
      // 车型
      postData.goodType = me.checkedCategory && me.checkedCategory.join(',')
      if (!postData.goodType) {
        return me.$message.error('请选择车型')
      }
      if (me.isAddType && !postData.goodsName) {
        return me.$message.error('请选择车型名称')
      }
      if (!postData.goodProductType) {
        return me.$message.error('请选择生产方式')
      }
      // 处理拼接字符串
      if (/[\u4E00-\u9FA5]/g.test(postData.goodStroke) && postData.goodStroke) {
        return this.$message.error('请输入正确的行程')
      }
      if (/[\u4E00-\u9FA5]/g.test(postData.goodBore) && postData.goodBore) {
        return this.$message.error('请输入正确的缸径')
      }
      if (/[\u4E00-\u9FA5]/g.test(postData.long) && postData.long) {
        return this.$message.error('请输入正确的长')
      }
      if (/[\u4E00-\u9FA5]/g.test(postData.width) && postData.width) {
        return this.$message.error('请输入正确的宽')
      }
      if (/[\u4E00-\u9FA5]/g.test(postData.high) && postData.high) {
        return this.$message.error('请输入正确的高')
      }
      if (
        (!postData.goodStroke && postData.goodBore) ||
        (postData.goodStroke && !postData.goodBore)
      ) {
        return me.$message.error('请填写正确的缸径和行程')
      }
      if (
        (postData.long && !postData.width && postData.high) ||
        (postData.long && postData.width && !postData.high) ||
        (!postData.long && postData.width && postData.high) ||
        (postData.long && postData.width && !postData.high) ||
        (!postData.long && postData.width && postData.high) ||
        (postData.long && !postData.width && postData.high) ||
        (postData.long && !postData.width && !postData.high) ||
        (!postData.long && postData.width && !postData.high) ||
        (!postData.long && !postData.width && postData.high)
      ) {
        return me.$message.error('请输入正确的长x宽x高')
      }
      postData.goodCylinderStroke = postData.goodBore
        ? `${postData.goodBore}x${postData.goodStroke}`
        : ''
      postData.goodReductRatio = postData.goodReductRatio
        ? `${postData.goodReductRatio}:1`
        : ''
      postData.goodMaxHorse = me.goodMaxHorse
      postData.goodMotorMaxHorse = me.goodMotorMaxHorse
      postData.goodSize = postData.long
        ? `${postData.long}x${postData.width}x${postData.high}`
        : ''
      // 三个价格2个排量去除空格
      postData.goodsPrice =
        postData.goodsPrice && postData.goodsPrice.toString().trim()
      postData.goodVolume =
        postData.goodVolume && postData.goodVolume.toString().trim()
      // 是否上架
      postData.isOnStatus = postData.isOnStatus === true ? 1 : 0
      // 配气结构
      postData.goodGasStructure =
        postData.goodGasStructureNew +
        (postData.goodGasStructureNum ? `${postData.goodGasStructureNum}` : '')
      // 放电耐温范围
      postData.goodTemperatureRange =
        postData.goodTemperatureRange +
        (postData.goodTemperatureRangeRight
          ? `~${postData.goodTemperatureRangeRight}`
          : '')
      postData.standard =
        (me.checkList &&
          me.checkList.sort(function (a, b) {
            return a - b
          }) &&
          me.checkList.join(',')) ||
        ''
      postData.factoryNotProvide = me.factoryNotProvide
      // 模式
      postData.modeSelection = me.modeSelectionList.length
        ? me.modeSelectionList.join(',') || ''
        : ''
      // 电池容量
      postData.goodBatteryCapacity = postData.goodBatteryCapacity || ''
      // 变速器型式
      postData.goodSpeedMode =
        (me.goodSpeedMode &&
          me.goodSpeedMode.length > 0 &&
          me.goodSpeedMode[1]) ||
        ''
      // 颜色
      const newcolorExpsList = []
      me.colorExpsList &&
        me.colorExpsList?.map(function (value) {
          newcolorExpsList.push(value.id)
        })
      // postData.content = me.videoList.length
      //   ? JSON.stringify(me.videoList)
      //   : postData.videoList && postData.videoList.length
      //   ? JSON.stringify(postData.videoList)
      //   : ''
      // postData.content = me.videoList.length
      //   ? JSON.stringify(me.videoList)
      //   : ''
      postData.modeSelection =
        me.labels && me.labels.length > 0 ? JSON.stringify(me.labels) : ''
      postData.labelsPack =
        me.labelsPack && me.labelsPack.length > 0
          ? JSON.stringify(me.labelsPack)
          : ''
      postData.labelsConfig =
        me.labelsConfig && me.labelsConfig.length > 0
          ? JSON.stringify(me.labelsConfig)
          : ''

      postData.goodColors = newcolorExpsList && newcolorExpsList.join(',')
      postData.goodsId =
        (me.$route.query && me.$route.query.id) || postData.goodsId
      postData.brandId =
        (me.$route.query && me.$route.query.brandId) || postData.brandId
      postData.carYear =
        (me.$route.query && me.$route.query.carYear) || postData.carYear
      postData.carYearStatus =
        (me.$route.query && me.$route.query.carYearStatus) ||
        postData.carYearStatus
      postData.goodsName =
        (me.$route.query && me.$route.query.goodsName) || postData.goodsName

      // 正整数数据校验形式
      if (postData.goodVolume && !alidatePositiveInteger(postData.goodVolume)) {
        return me.$message.error('排量只允许填写正整数，示例：123，请修正！')
      }

      // 正数数据校验形式
      if (
        postData.goodVoltage &&
        !validatePositiveNumberOne(postData.goodVoltage)
      ) {
        return me.$message.error('电压只允许填写正数，示例：12，12.5，请修正！')
      }
      if (
        postData.goodExactVolume &&
        !validatePositiveNumber(postData.goodExactVolume)
      ) {
        return me.$message.error(
          '精确排量只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      if (
        postData.goodMaxPower &&
        !validatePositiveNumber(postData.goodMaxPower)
      ) {
        return me.$message.error(
          '最大功率只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      if (
        postData.goodEndurance &&
        !validatePositiveNumber(postData.goodEndurance)
      ) {
        return me.$message.error(
          '续航里程只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      if (
        postData.goodConstantSpeedEndurance &&
        !validatePositiveNumber(postData.goodConstantSpeedEndurance)
      ) {
        return me.$message.error(
          '等速续航里程只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      if (
        postData.goodMaxSpeed &&
        !validatePositiveNumber(postData.goodMaxSpeed)
      ) {
        return me.$message.error(
          '最高车速只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      if (
        postData.goodAllWeight &&
        !validatePositiveNumber(postData.goodAllWeight)
      ) {
        return me.$message.error(
          '整备质量只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      if (postData.goodOilBox && !validatePositiveNumber(postData.goodOilBox)) {
        return me.$message.error(
          '主油箱容量只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      if (
        postData.viceGoodOilBox &&
        !validatePositiveNumber(postData.viceGoodOilBox)
      ) {
        return me.$message.error(
          '副油箱容量只允许填写正数，示例：12，12.3，请修正！'
        )
      }
      me.deleteObject(postData)
      postData.tricycle = me.checkedWheel[0] || ''
      postData.footBoard = me.checkedPedal[0] || ''
      setTimeout(() => {
        me.updateData = postData
        me.isEditStatus && postData.carId
          ? me.setLog(deepCopy(postData))
          : me.postData(postData, me.isEditStatus)
      }, 10)
    },
    // 新增或编辑时，发送数据
    postData(data, type) {
      const me = this
      me.deleteObject(me.updateData)
      if (me.isPostDataStatus) {
        const toastTitle = type
          ? '编辑正在处理中，请稍后'
          : '正在发送中，请稍后'
        return me.$message(toastTitle)
      }
      me.isPostDataStatus = true
      const loadingInstance = Loading.service({
        fullscreen: true,
        text: '处理中'
      })
      // me.updateData.content = me.videoList.length ? JSON.stringify(me.videoList) : ''
      me.ruleForm.motorCarOtherListedInfoENTList =
        me.motorCarOtherListedInfoENTList
      me.updateData.contentJson =
        me.ruleForm.motorCarOtherListedInfoENTList &&
        me.ruleForm.motorCarOtherListedInfoENTList.length
          ? JSON.stringify(me.ruleForm.motorCarOtherListedInfoENTList)
          : ''
      // console.log(me.updateData.goodsImgs)
      let postData = {
        ...me.updateData,
        goodVoltage: me.updateData.goodVoltage || '',
        standard: Array.isArray(me.updateData.standard)
          ? me.updateData.standard.join('')
          : me.updateData.standard,
        newEnergyTypes: data.newEnergyTypes || 0
      }
      updatePatternDetail(postData)
        .then((response) => {
          loadingInstance.close()
          me.isPostDataStatus = false
          if (response.data.code === 0) {
            const toastTitle = postData.carId ? '编辑成功' : '创建成功'
            me.$message.success(toastTitle)
            me.isModifyStatus = false
            !postData.labelChange ? delete postData.labelChange : null
            !postData.colorChange ? delete postData.colorChange : null
            me.saveLog(
              postData,
              postData.carId ? '编辑' : '新增',
              response.data.data
            )
            setTimeout(() => {
              me.$router.go(-1)
              this.ruleForm = {}
            }, 500)
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          me.isPostDataStatus = false
          loadingInstance.close()
        })
    },
    // 款型展图
    updategoodsThumb(res, imgType) {
      if (!res) return
      if (res.name) {
        const data = {
          imgOrgUrl: res.imgOrgUrl,
          imgUrl: res.imgOrgUrl
        }
        this.isModifyStatus = true
        this.key = ''
        this.ruleForm['goodsThumb'] = data.imgOrgUrl
        this.ruleForm['thumbType'] = imgType
        console.log(data)
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    handleCheckedCategoryChange(val) {
      this.isModifyStatus = true
      if (val.length) {
        this.checkedCategory = [val[val.length - 1]]
        this.checkedPedal = []
        this.checkedWheel = []
      }
      if (this.checkedCategory && this.checkedCategory.length > 0) {
        this.ruleForm.goodType =
          this.checkedCategory && this.checkedCategory.join(',')
      }
    },

    // 获取可选颜色值
    // getColorData(data) {
    //   this.ruleForm.colorChange = 1
    //   // this.isModifyStatus = true
    //   this.colorExpsList = data
    //   const arr = []
    //   data &&
    //     data?.map(function (value) {
    //       arr.push(value.id)
    //     })
    //   this.ruleForm.goodColors = arr.join(',')
    // },
    // 获取可选颜色值
    chooseNewSendData(data) {
      this.ruleForm.colorChange = 1
      // this.isModifyStatus = true
      this.colorExpsList = [...data]
      // const arr = []
      // data &&
      //   data?.map(function (value) {
      //     arr.push(value.id)
      //   })
      // this.ruleForm.goodColors = arr.join(',')
    },
    // 获取删除图片颜色绑定关系
    getDeleteColorId(id) {
      if (!this.imageTabList) {
        return
      }
      const keyArr = this.imageTabList && Object.keys(this.imageTabList)
      keyArr?.map((colorId) => {
        if (this.imageTabList[colorId].length) {
          this.imageTabList[colorId]?.map((item) => {
            if (item.color === id) {
              item.color = ''
              item.colorName = ''
            }
          })
        }
      })
      this.imageTabList = { ...this.imageTabList }
    },
    // 删除颜色
    deleteColor(index, item) {
      this.ruleForm.colorChange = 1
      // this.isModifyStatus = true
      this.colorExpsList.splice(index, 1)
      // const arr = []
      // this.colorExpsList &&
      //   this.colorExpsList?.map(function (value) {
      //     arr.push(value.id)
      //   })
      // this.ruleForm.goodColors = arr.join(',')
    },
    // 初始化图片
    initImage() {
      this.$refs['ImageDialog'].init({
        tabs: this.imageTabList, // tabs 图片列表
        labels: this.colorExpsList,
        goodsId: this.$route.query.id || '',
        carId: this.copyGarage
          ? 'none'
          : this.$route.query.carId || this.ruleForm.carId || 'none', // none 不需要请求接口；复制款型，不需要图片
        watermark: 1, // 是否需要传水印
        systemType: 'car_detail', // car_detail 水印类型
        quality: '0.8', // 图片压缩比例
        showOtherAction: true,
        shopId: '',
        shopName: '',
        modelHeight: this.modelHeight || '',
        goodSaddleHigh: this.ruleForm.goodSaddleHigh || '',
        isModelType: true,
        goodsCarName: this.ruleForm.goodsCarName || ''
      })
    },
    // 复制后刷新图片接口
    refreshImage() {
      this.$refs['ImageDialog'].init({
        tabs: [], // tabs 图片列表
        goodsId: this.$route.query.id || '',
        carId: this.copyGarage
          ? 'none'
          : this.$route.query.carId || this.ruleForm.carId || 'none', // none 不需要请求接口；复制款型，不需要图片
        isRefresh: true
      })
    },
    // 接受款型图片更新
    getImageData(data) {
      this.isModifyStatus = true
      Object.keys(data)?.map((name) => {
        if (name === 'tabs') {
          // 图片
          this.imageTabList = data[name]
        } else if (name === 'labels') {
          // 颜色
          this.colorExpsList = data[name]
        } else if (name === 'modelHeight') {
          this.modelHeight = data[name]
          this.ruleForm.modelHeight = data[name]
        }
        // else if (name === 'goodSaddleHigh') {
        //   // this.goodSaddleHigh = data[name]
        //   this.ruleForm.goodSaddleHigh = data[name]
        // }
      })
      // 图片处理
      let imageArr = []
      Object.keys(this.imageTabList)?.map((name) => {
        imageArr = [...imageArr, ...this.imageTabList[name]]
      })
      this.ruleForm.goodsImgs = JSON.stringify(imageArr)
      // 颜色处理
      const arr = []
      if (this.colorExpsList && this.colorExpsList.length > 0) {
        this.colorExpsList?.map(function (value) {
          arr.push(value.id)
        })
        this.ruleForm.goodColors = arr.join(',')
      }
      this.ruleForm.groupIds = data.groupIds || ''
    },

    // 获取视频
    getVideoData(data) {
      let arr = []
      this.isModifyStatus = true
      Object.values(data.tabs)?.map((_) => {
        arr = arr.concat(_)
      })
      this.videoList = arr
      this.ruleForm.content = arr.length ? JSON.stringify(arr) : ''
      this.showVideoList = arr
    },
    // 初始化视频
    initVideo() {
      this.$refs['VideoDialog'].init({
        goodsId: this.$route.query.id || '',
        carId: this.copyGarage
          ? ''
          : this.$route.query.carId || this.ruleForm.carId || '', // none 不需要请求接口；复制款型，不需要图片
        tabs:
          this.videoList && this.videoList[0] && this.videoList[0].imgOrgUrl
            ? this.videoList
            : []
      })
    },
    // 准备看视频
    seeVideoPlay(video) {
      if (!video.videoUrl) {
        return this.$message.error('暂无视频播放地址')
      }
      this.$refs.ChooseVideo.init(video.videoUrl)
    },
    // 取消
    goBack() {
      const me = this
      if (me.isModifyStatus) {
        me.$confirm('当页面有修改数据，是否离开', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning'
        })
          .then(() => {
            me.isModifyStatus = false
            setTimeout(() => {
              me.$router.go(-1)
              me.ruleForm = {}
            }, 500)
          })
          .catch((_) => {})
      } else {
        me.$router.go(-1)
        me.ruleForm = {}
      }
    },
    // 变更模式
    updataModeList() {
      const me = this
      if (!me.modeSelectionList.length) {
        me.modeSelectionList.push('无')
      } else {
        const index = me.modeSelectionList.findIndex((_) => {
          return _ === '无'
        })
        index !== -1 ? me.modeSelectionList.splice(index, 1) : ''
      }
    },
    // 设置日志
    setLog(data) {
      let brandData =
        JSON.parse(sessionStorage.getItem(`editGarage${data.carId}`)) || {}
      const splitValue = ['isOnStatus', 'factoryNotProvide', 'labelChange']
      const splitNumberValue = [
        'brandId',
        'goodsId',
        'goodsPrice',
        'carYearStatus'
      ]
      const produceWay = convertKeyValueEnum(this.produceWayList)
      const showSimple = ['carMeasuredInfoEnt', 'goodsImgs', 'content']
      brandData = {
        ...setValue(brandData, splitValue),
        isOnSale: carOnSaleEnum[brandData.isOnSale],
        goodProductType: produceWay[brandData.goodProductType],
        goodGasStructure:
          brandData.goodGasStructure !== 'undefined'
            ? brandData.goodGasStructure || ''
            : '',
        goodReductRatio: brandData.goodReductRatio
          ? `${brandData.goodReductRatio}:1`
          : ''
      }
      data.isOnStatus = !!data.isOnStatus
      let newData = {
        ...setValue(data, splitValue),
        isOnSale: carOnSaleEnum[data.isOnSale],
        goodProductType: produceWay[data.goodProductType],
        goodVoltage: data.goodVoltage || '',
        standard: data.standard.split(','),
        goodBatteryCapacity: data.goodBatteryCapacity || ''
      }
      if (newData.labelChange === '否') {
        delete newData.labelChange
      }
      delete newData.goodColors
      brandData = {
        ...setValueNumber(brandData, splitNumberValue)
      }
      newData = {
        ...setValueNumber(newData, splitNumberValue)
      }
      const showImgList = ['goodsThumb']
      const booleanList = []
      const delList = []
      this.$refs.seeLog.init(
        brandData,
        newData,
        carLog,
        showImgList,
        booleanList,
        delList,
        showSimple
      )
    },
    postReadyData() {
      this.postData(this.ruleForm, this.isEditStatus)
    },
    // 款型日志
    saveLog(afterData, operateType, carId) {
      let postData = {
        ...deepCopy(afterData)
      }
      ;['isOnStatus']?.map((item) => {
        postData[item] = !!postData[item]
      })
      ;['standard', 'colorExps', 'videoList']?.map((item) => {
        postData[item] = !(postData[item] && postData[item].length)
          ? []
          : postData[item]
      })
      ;['goodsId', 'brandId', 'goodsPrice']?.map((item) => {
        postData[item] = postData[item]
          ? Number(postData[item])
          : postData[item]
      })
      let beforeData = JSON.parse(
        sessionStorage.getItem(`editGarage${postData.carId}`) || '{}'
      )
      const isEdit = !!postData.carId
      postLog(
        77,
        postData.carId || carId || '',
        operateType,
        '',
        isEdit ? JSON.stringify(beforeData) : '{}',
        JSON.stringify(postData)
      )
    },
    deleteObject(data) {
      delete data.tags
      delete data.colorExps
      delete data.videoList
      delete data.goodGasStructureNew
      delete data.goodGasStructureNum
      delete data.newCarOnsaleEndTime
    },
    getextensionName(url) {
      let extensionName = 'png'
      try {
        const str = url.split('!')[0]
        extensionName = `.${str.split('.').reverse()[0]}` // 文件扩展名
      } catch (e) {
        extensionName = 'png'
      }
      return extensionName
    },
    initDesign() {
      const name = this.ruleForm.goodsCarName
      const { id, carId } = this.$route.query
      this.$router.push({
        name: 'VehicleDesign',
        query: { id, name: name, carId }
      })
    }
  }
}
</script>

<style lang="scss">
.custom-disabled_style {
  .el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: white !important;
  }
  .el-checkbox__input.is-disabled .el-checkbox__inner::after {
    border-color: white !important;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: rgb(64, 158, 255) !important;
    border-color: rgb(64, 158, 255) !important;
  }
}
.c-new-garage {
  .new-garage-header {
    margin: 20px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .new-garage-form {
    min-height: calc(100vh - 124px);
    margin: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    overflow: hidden;
    .modules {
      width: 100%;
      padding: 5px;
      border-bottom: 1px solid #dcdfe6;
    }
    .title {
      padding: 10px;
      .tip {
        font-size: 13px;
        color: #999;
      }
    }
    .add-video-content {
      position: relative;
      margin: 10px;
      .imageFile {
        opacity: 0;
        position: absolute;
        width: 100%;
        height: 100%;
      }
    }
    .video-content {
      margin: 10px;
      min-height: 100px;
    }
    .video-show-content {
      display: inline-block;
      width: 200px;
      margin-right: 10px;
      position: relative;
      .video-img {
        display: inline-block;
        width: 200px;
        height: 150px;
        object-fit: cover;
      }
      .video-delete {
        position: absolute;
        right: 0;
        top: 0;
      }
      .play-icon {
        position: absolute;
        left: 55px;
        top: 30px;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 5px;
      }
    }
  }
  .lable-red .el-form-item__label {
    color: red;
  }
}
</style>
