<template>
  <div v-loading="loading" class="garage-approve">
    <el-row :gutter="10" style="min-width: 2200px">
      <el-col :span="12" style="border-right: 1px solid #ddd">
        <header class="action" style="margin-bottom: 10px">
          <el-form
            ref="activitySearch"
            :model="ruleForm"
            :inline="true"
            class="activitySearch"
          >
            <seleted-user
              ref="selectUser"
              :name="'用户名'"
              :placeholder="'请输入用户名'"
              @sendData="setUid"
            />
            <el-form-item label="用户ID">
              <el-input
                type="number"
                v-model="ruleForm.uid"
                placeholder="请输入用户ID"
                :max-length="15"
                @input="$refs.selectUser?.clearData()"
              />
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select v-model="ruleForm.status">
                <el-option
                  v-for="(value, index) in carAuditStatus"
                  :key="index"
                  :label="value.name"
                  :value="value.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="品牌车型">
              <BrandCar ref="brandCar" @select="handleBrandSelect">
                <template #default="{ brandCarName }">
                  <div class="brand-car_reference">{{ brandCarName }}</div>
                </template>
              </BrandCar>
            </el-form-item>
            <el-form-item label="审核人" prop="authUser">
              <el-input
                v-model="ruleForm.authUser"
                type="text"
                placeholder="请输入审核人"
                clearable
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="车牌号" prop="licensePlateNumber">
              <el-input
                v-model="ruleForm.licensePlateNumber"
                type="text"
                placeholder="请输入车牌号"
                clearable
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="申请时间">
              <el-date-picker
                :default-time="
                  ['00:00:00', '23:59:59'].map((d) =>
                    $dayjs(d, 'hh:mm:ss').toDate()
                  )
                "
                :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate"
                :cell-class-name="pickerOptions && pickerOptions.cellClassName"
                v-model="createDateRange"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button @click="resetForm()">重置</el-button>
              <el-button
                :loading="refreshLoading"
                type="primary"
                @click="refresh()"
                >刷新缓存</el-button
              >
            </el-form-item>
          </el-form>
        </header>
        <div class="main" style="min-height: 80vh">
          <el-table
            ref="garageApproveList"
            :data="garageApproveList"
            highlight-current-row
            row-key="garageApproveList"
            border
            style="width: 100%; height: 75vh; overflow-y: auto"
            @row-dblclick="rowDoubleClick"
          >
            <el-table-column
              prop="userId"
              label="用户ID"
              align="center"
              width="120"
            />
            <el-table-column
              prop="userName"
              label="用户名"
              align="center"
              width="120"
            />
            <el-table-column
              prop="goodName"
              label="车辆款型"
              align="center"
              width="140"
            />
            <el-table-column
              prop="isOnSale"
              label="在售状态"
              align="center"
              width="140"
            >
              <template v-slot="scope">
                {{ onSaleEnumList[scope.row.isOnSale] }}
              </template>
            </el-table-column>
            <el-table-column
              prop="brandName"
              label="品牌名称"
              align="center"
              width="130"
            />
            <el-table-column
              prop="source"
              label="数据来源"
              align="center"
              width="120"
            >
              <template v-slot="scope">{{
                getDataSources(scope.row.source)
              }}</template>
            </el-table-column>
            <el-table-column
              prop="status"
              label="审核状态"
              align="center"
              width="120"
            >
              <template v-slot="scope">
                <span
                  :class="{ 'is-text-examine': scope.row.status === '2' }"
                  >{{ getStatus(scope.row.status) }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="remarks"
              label="拒绝理由"
              align="center"
              width="280"
            />
            <el-table-column
              prop="auditTime"
              align="center"
              width="140"
              label="审核时间"
            >
              <template v-slot="scope">{{
                $filters.timeFullS(scope.row.auditTime)
              }}</template>
            </el-table-column>
            <el-table-column
              prop="licensePlateNumber"
              label="车牌号"
              align="center"
            />
            <el-table-column prop="authUser" label="审核人" align="center" />
            <el-table-column
              prop="updateTime"
              align="center"
              width="140"
              label="申请时间"
            >
              <template v-slot="scope">{{
                $filters.timeFullS(scope.row.createTime)
              }}</template>
            </el-table-column>
            <el-table-column align="center" width="140" label="操作">
              <template v-slot="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleLog(scope.row)"
                  >查看日志</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-model:current-page="ruleForm.page"
            :page-size="20"
            :page-sizes="[10, 20, 40, 60]"
            :total="total"
            background
            layout="total, prev, pager, next, jumper"
            style="text-align: center; margin-top: 10px"
            @size-change="currentChange"
            @current-change="currentChange"
          />
        </div>
      </el-col>
      <el-col v-if="isShowDetail" :span="12">
        <el-form
          ref="garageDetail"
          :model="garageDetail"
          label-width="100px"
          class="garage_detail"
        >
          <el-form-item label="行驶证" prop="certUrl">
            <img
              :src="$filters.replaceImgUrl(garageDetail.certUrl)"
              class="img-content"
              alt
              @click="seeBigImg($filters.replaceImgUrl(garageDetail.certUrl))"
            />
          </el-form-item>
          <el-form-item label="骑行图片" prop="imgUrl">
            <img
              :src="garageDetail.imgUrl"
              class="img-content"
              alt
              @click="seeBigImg(garageDetail.imgUrl)"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="garageDetail.status === '2'"
              type="primary"
              @click="changeStatus(1)"
              >审核通过</el-button
            >
            <el-button
              v-if="garageDetail.status === '2' || garageDetail.status === '1'"
              type="danger"
              @click="changeStatus(0)"
              >拒绝</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <SeeLog ref="seeLog" />
    <choose-show-image ref="showImage" />
    <CommonRejectNotice ref="rejectNotice" @confirmRejection="getRejectData" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { dataSources, auditStatus } from '@/utils/enum'
import { forwardPickerOptions } from '@/utils/configData'
import { convertKeyValueEnum } from '@/utils/convert'
import {
  getGarageApproveList, // 认证车辆列表
  updateApproveStatus, // 更新审核状态
  clearGarageApproveCache // 车辆认证刷新缓存
} from '@/api/garage'
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import CommonRejectNotice from '@/components/Notice/commonRejectNotice.vue'
import { onSaleEnum } from '@/utils/enum'
import BrandCar from '@/components/brandCar/index.vue'
import SeeLog from './see-log.vue'

export default {
  data() {
    return {
      onSaleEnumList: convertKeyValueEnum(onSaleEnum),
      loading: false,
      // 数据来源
      dataSources: dataSources,
      dataSourcesFrom: convertKeyValueEnum(dataSources),
      auditStatus: auditStatus,
      // 审核状态
      carAuditStatus: [
        {
          value: '',
          name: '全部'
        },
        {
          value: '0',
          name: '拒绝'
        },
        {
          value: '1',
          name: '通过'
        },
        {
          value: '2',
          name: '待审核'
        },
        {
          value: '3',
          name: '取消'
        }
      ],
      auditStatusEnum: convertKeyValueEnum(auditStatus),
      pickerOptions: forwardPickerOptions,
      ruleForm: {
        uid: '', // 用户Id
        status: '', // 状态
        authUser: '', // 审核人
        // brandName: '', // 品牌名称
        goodsId: '',
        brandId: '',
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        page: 1, // 页码
        limit: 20,
        licensePlateNumber: '' // 车牌号
      },
      total: 0,
      // 车辆认证列表数据
      garageApproveList: [],
      // 是否显示右侧详情
      isShowDetail: false,
      refreshLoading: false,
      // 车辆详情信息
      garageDetail: {},
      dayjs
    }
  },
  name: 'GarageApproveList',
  components: {
    SeletedUser,
    ChooseShowImage,
    CommonRejectNotice,
    BrandCar,
    SeeLog
  },
  computed: {
    createDateRange: {
      get() {
        const me = this
        if (me.ruleForm.beginTime && me.ruleForm.endTime) {
          return [me.ruleForm.beginTime, me.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        const me = this
        if (value) {
          me.ruleForm.beginTime = value[0]
          me.ruleForm.endTime = value[1]
        } else {
          me.ruleForm.beginTime = ''
          me.ruleForm.endTime = ''
        }
      }
    }
  },
  watch: {},
  activated() {
    const me = this
    me.ruleForm.authUser = me.$route.query.authUser || ''
  },
  mounted() {
    const garageApproveList = JSON.parse(
      sessionStorage['garageApproveList'] || '{}'
    )
    if (garageApproveList.limit) {
      this.ruleForm = garageApproveList
    }
    this.$nextTick((_) => {
      this.getGarageApproveList()
    })
  },
  methods: {
    // 获取数据
    getDataSources(sources) {
      const desc = this.dataSourcesFrom[sources]
      return desc
    },
    // 获取审核状态
    getStatus(status) {
      const desc = this.auditStatusEnum[status]
      return desc
    },
    // 获取列表数据
    getGarageApproveList() {
      const me = this
      const requestParams = {
        ...this.ruleForm
      }
      sessionStorage.setItem('garageApproveList', JSON.stringify(requestParams))
      me.loading = true
      getGarageApproveList(requestParams)
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            me.garageApproveList = response.data.data.list
            me.total = response.data.data.total
          }
        })
        .catch(() => {
          me.loading = false
        })
    },
    // 更新页码
    currentChange(page) {
      this.ruleForm.page = page
      this.getGarageApproveList()
    },
    // 重置search 系列
    resetForm() {
      this.ruleForm = Object.assign(this.ruleForm, {
        uid: '', // 用户Id
        authUser: '',
        status: '', // 状态
        brandName: '', // 品牌名称
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        page: 1, // 页码
        goodsId: '',
        brandId: '',
        limit: 20,
        licensePlateNumber: ''
      })
      this.$refs.selectUser.clearData()
      this.$refs.brandCar?.reset()
      this.getGarageApproveList()
    },
    // 设置返回uid
    setUid(id) {
      this.ruleForm.uid = id
    },
    // 查询
    search() {
      this.ruleForm.page = 1
      this.getGarageApproveList()
    },
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(link)
    },
    // 双击点中，进入详情
    rowDoubleClick(garage) {
      const me = this
      me.garageDetail = garage
      me.isShowDetail = true
    },
    // 取消认证
    Cancel() {
      const me = this
      me.$confirm('你确定取消认证吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const cancelStatus = 3
          me.updataStatusData({}, cancelStatus)
        })
        .catch()
    },
    // 变更状态
    changeStatus(status) {
      const me = this
      setTimeout(() => {
        if (status === 0) {
          return me.$refs.rejectNotice.init([
            '行驶证与认证车辆不符',
            '车辆照片务必保证车牌号清晰完整',
            '请按要求上传行驶证和车辆图片',
            '车架号必须保留首位和末四位清晰',
            '该车牌号近期认证次数超限'
          ])
        }
        me.updataStatusData({}, status)
      }, 100)
    },
    // 拒绝回来的数据
    getRejectData(data) {
      this.updataStatusData(data, 0)
    },
    updataStatusData(rejectData, status) {
      const me = this
      updateApproveStatus({
        authUid: me.uid,
        authUser: me.name,
        certId: me.garageDetail.certId,
        userId: me.garageDetail.userId,
        userName: me.garageDetail.userName,
        goodsId: me.garageDetail.goodsId,
        goodName: me.garageDetail.goodName,
        status: status,
        remarks: rejectData.auditFailReason || '',
        licensePlateNumber: me.garageDetail.licensePlateNumber || ''
      }).then((response) => {
        if (response.data.code === 0) {
          const cancelStatus = 3
          const tip = status === cancelStatus ? '取消认证成功' : '操作成功过'
          me.$message.success(tip)
          me.getGarageApproveList()
          me.isShowDetail = false
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    handleBrandSelect(val) {
      this.ruleForm.goodsId = val.goodId || ''
      this.ruleForm.brandId = val.brandId || ''
    },
    refresh() {
      const me = this
      me.$confirm('此操作将刷新车辆认证列表缓存, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        me.refreshLoading = true
        clearGarageApproveCache()
          .then((response) => {
            if (response.data.code === 0) {
              this.$message.success('刷新成功')
            }
          })
          .finally(() => {
            me.refreshLoading = false
          })
      })
    },
    handleLog(row) {
      this.$refs.seeLog.init(row.certId || '')
    }
  }
}
</script>

<style lang="scss" scoped>
.garage-approve {
  padding: 10px 20px;
  .garage_detail {
    margin-top: 20px;
    .img-content {
      margin-top: 10px;
      margin-left: 20px;
      max-height: 400px;
      max-width: 800px;
      object-fit: cover;
      cursor: pointer;
    }
  }
}
.brand-car_reference {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 246px;
  height: 36px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 15px;
  color: #606266;
  cursor: pointer;
}
</style>
