<template>
  <div v-loading="loading" class="contentreview">
    <div style="margin-bottom: 10px">
      <span class="pendingReview takeTenSel">
        待审核的认证车辆: {{ myData.pendingCarCertNum }} &ensp;&ensp;&ensp;
        <el-button class="choose" type="danger" @click="getList(1)"
          >正取50条</el-button
        >
        <!-- <el-button class="choose" type="warning" @click="getList(2)">倒取50条</el-button> -->
      </span>
      <p class="my-data-list">
        <span class="is-day-over num">{{ myData.myDailyCompleteCnt }}</span>
        <br />我的完成
      </p>
      <p class="my-data-list">
        <span class="is-day-over num">{{ myData.dailyCompleteCnt }}</span>
        <br />今日总完成
      </p>
      <p class="my-data-list">
        <span class="is-month-over num">{{ myData.monthlyCompleteCnt }}</span>
        <br />本月总完成
      </p>
      <p class="my-data-list">
        <el-button
          type="warning"
          class="choose takeTenResult"
          @click="takeMyJob()"
          >我的成果</el-button
        >
      </p>
    </div>
    <el-row :gutter="24" style="min-width: 1530px">
      <el-col :span="8">
        <div>
          <el-table
            ref="table"
            :data="carList"
            class="table"
            highlight-current-row
            row-key="carLists"
            border
            style="width: 100%; overflow-y: auto"
            @row-click="openDetails"
          >
            <el-table-column prop="userId" label="用户ID" align="center" />
            <el-table-column prop="userName" label="用户名" align="center" />
            <el-table-column prop="goodName" label="车辆款型" align="center">
              <template v-slot="scope">
                <span>{{ scope.row.goodName || scope.row.tmpGoodName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="drivingLicenseName"
              label="行驶证名称"
              align="center"
            />
            <el-table-column
              prop="goodName"
              label="指导价格（元）"
              align="center"
            >
              <template v-slot="scope">
                <span>{{
                  scope.row.goodsPrice ? scope.row.goodsPrice : '暂无价格'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="brandName" label="品牌名称" align="center">
              <template v-slot="scope">
                <span>{{ scope.row.brandName || scope.row.tmpBrandName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col v-show="showModule" :span="16">
        <div class="Configuration">
          <el-button type="primary" @click="changeStatus(1)"
            >审核通过</el-button
          >
          <el-button
            :disabled="!!garageDetail.goodsId"
            type="warning"
            @click="Modify()"
            >手动录入修改</el-button
          >
          <el-button
            style="margin-left: 150px"
            type="danger"
            @click="changeStatus(0)"
            >拒绝</el-button
          >
          <el-form
            ref="garageDetail"
            :model="garageDetail"
            label-width="100px"
            class="garage-detail"
          >
            <el-form-item label="行驶证" prop="certUrl">
              <img
                :src="$filters.replaceImgUrl(garageDetail.certUrl)"
                class="img-content"
                alt
                @click="seeBigImg(garageDetail.certUrl, true)"
              />
            </el-form-item>
            <el-form-item label="骑行图片" prop="imgUrl">
              <img
                :src="garageDetail.imgUrl"
                class="img-content"
                alt
                @click="seeBigImg(garageDetail.imgUrl)"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <CommonRejectNotice ref="rejectNotice" @confirmRejection="getRejectData" />
    <modify-approve
      ref="ModifyApprove"
      :garageform="garageDetail"
      @success="updateDetail"
    />
    <choose-show-image ref="showImage" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import CommonRejectNotice from '@/components/Notice/commonRejectNotice.vue'
import ModifyApprove from './components/index.vue'
import { replaceImgUrl } from '@/filters'
import {
  updateApproveStatus,
  getAuthList,
  addAuthList,
  getAuthInfo
} from '@/api/garage'
export default {
  name: 'UsedCarAudit',
  components: {
    ChooseShowImage,
    CommonRejectNotice,
    ModifyApprove
  },
  data() {
    return {
      carList: [],
      status: 0,
      loading: false, // 加载状态
      showModule: false, // 是否显示
      myData: {}, // 我的数据
      ruleForm: {},
      garageDetail: {},
      isModifyStatus: false, // 是否有修改
      isContinuous: true, // 是否连续点击
      submiting: false, // 是否提交中
      isfetch: true // 是否连续取十条
    }
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  watch: {},
  activated() {
    const me = this
    me.loading = true
    me.getAuthInfo()
    me.getAuditList()
  },
  methods: {
    takeMyJob() {
      // 进入我的工作
      const me = this
      me.$router.push({
        name: 'garageApproveList',
        query: {
          authUser: me.name
        }
      })
    },
    // 大图查看图片
    seeBigImg(link, flag) {
      this.$refs.showImage.init(flag ? replaceImgUrl(link) : link)
    },
    // 修改
    Modify() {
      const me = this
      me.garageDetail.brandName =
        me.garageDetail.brandName || me.garageDetail.tmpBrandName
      me.garageDetail.goodName =
        me.garageDetail.goodName || me.garageDetail.tmpGoodName
      me.$refs['ModifyApprove'].dialogVisible = true
    },
    // 更新详情数据
    updateDetail(data) {
      console.log(data)
    },
    // 实时更新总数居
    getAuthInfo() {
      const me = this
      getAuthInfo({
        source: 1, // 1表示车辆认证审核
        authUid: me.uid,
        authUser: me.name,
        ossOwnerName: me.name
      }).then((response) => {
        if (response.data.code === 0) {
          me.myData = response.data.data
        }
      })
    },
    // 拉取数据到池子
    getList(type) {
      const me = this
      if (me.carList && me.carList.length) {
        return me.$message.error('还有审核数据，不能操作')
      }
      me.loading = true
      addAuthList({
        source: 1, // 1表示车辆认证审核
        authUid: me.uid,
        authUser: me.name,
        limit: 50,
        orderType: type,
        operator: me.name
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.getAuditList()
            me.getAuthInfo()
            me.showModule = false
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 拉取数据到页面 待审核
    getAuditList() {
      const me = this
      me.loading = true
      getAuthList({
        source: 1, // 1表示车辆认证审核
        authUid: me.uid,
        authUser: me.name,
        limit: 50,
        page: 1,
        operator: me.name
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data || []
            me.carList = data
            me.openDetails(me.carList[0] || {})
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    openDetails(row) {
      const me = this
      me.garageDetail = row
      if (me.isContinuous) {
        me.isContinuous = false
        me.changeState()
        me.showModule = true
      }
    },
    changeState() {
      const me = this
      setTimeout(() => {
        me.isContinuous = true
      }, 1000)
    },
    changeStatus(status) {
      const me = this
      setTimeout(() => {
        if (status === 0) {
          return me.$refs.rejectNotice.init([
            '行驶证与认证车辆不符',
            '车辆照片务必保证车牌号清晰完整',
            '请按要求上传行驶证红章页',
            '车架号必须保留首位和末四位清晰',
            '该车牌号近期认证次数超限',
            '请上传实车照片',
            '请勿遮挡行驶证上的车牌号码'
          ])
        }
        me.updataStatusData({}, status)
      }, 50)
    },
    // 拒绝回来的数据
    getRejectData(data) {
      this.updataStatusData(data, 0)
    },
    updataStatusData(rejectData, status) {
      const me = this
      me.loading = true
      updateApproveStatus({
        source: 1, // 1表示车辆认证审核
        authUid: me.uid,
        authUser: me.name,
        certId: me.garageDetail.certId,
        userId: me.garageDetail.userId,
        userName: me.garageDetail.userName,
        goodsId: me.garageDetail.goodsId,
        goodName: me.garageDetail.goodName,
        status: status,
        remarks: rejectData.auditFailReason || ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            me.getAuditList()
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
        .finally((_) => {
          me.loading = true
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.contentreview {
  margin: 0px 20px 0;
  .my-data-list {
    display: inline-block;
    text-align: center;
    margin-left: 10px;
    .num {
      font-weight: bold;
      font-size: 30px;
    }
    .is-day-over {
      color: green;
    }
    .is-month-over {
      color: blue;
    }
    .is-achievements {
      color: green;
    }
    .is-day-ranking {
      color: blue;
    }
  }
  .garage-detail {
    margin-top: 20px;
    .img-content {
      width: 500px;
      cursor: pointer;
    }
  }
}
</style>
