<template>
  <div v-loading="loading" class="contentreview">
    <div style="margin-bottom: 10px">
      <span class="pendingReview takeTenSel">
        待审核的租赁车辆: {{ myData.unAuditCount }} &ensp;&ensp;&ensp;
        <el-button class="choose" type="danger" @click="getList(1)"
          >正取50条</el-button
        >
        <!-- <el-button class="choose" type="warning" @click="getList(2)">倒取50条</el-button> -->
      </span>
      <p class="my-data-list">
        <span class="is-day-over num">{{ myData.todayOperateCount }}</span
        ><br />
        今日完成
      </p>
      <p class="my-data-list">
        <span class="is-month-over num">{{
          myData.monthTotalOperateCount
        }}</span
        ><br />
        本月完成
      </p>
      <p class="my-data-list">
        <span class="is-achievements num">{{
          myData.todayTotalOperateCount
        }}</span
        ><br />
        我的成果
      </p>
      <p class="my-data-list">
        <span class="is-day-ranking num">{{ myData.todayRand }}</span
        ><br />
        今日排名
      </p>
      <el-button class="choose" @click="takeMyJob()">我的审核</el-button>
    </div>
    <el-row :gutter="24">
      <el-col :span="6">
        <div style="max-height: 80vh">
          <el-table
            ref="table"
            :data="carList"
            class="table"
            highlight-current-row
            row-key="carLists"
            border
            style="width: 100%; overflow-y: auto; height: 75vh"
            @row-click="openDetails"
          >
            <el-table-column prop="id" label="内容ID" align="center" />
            <el-table-column prop="shopId" label="商家ID" align="center" />
            <el-table-column prop="shopName" label="商家名称" align="center" />
            <el-table-column prop="title" label="车辆名称" align="center" />
          </el-table>
        </div>
      </el-col>
      <el-col v-show="showModule" :span="18">
        <div class="Configuration">
          <el-button type="danger" @click="changeStatus(2)">审核通过</el-button>
          <el-button type="warning" @click="changeStatus(1)"
            >审核不通过</el-button
          >
          <!-- 展示审核详情 -->
          <div style="margin-top: 20px">
            <el-row :gutter="24">
              <el-col :span="8">
                <p>
                  更新时间：{{
                    $date.format(ruleForm.auditTime, 'YYYY-MM-DD HH:mm:ss')
                  }}
                </p>
                <img
                  :src="ruleForm.certRedImage"
                  style="width: 100%"
                  @click="seeBigImg(ruleForm, ruleForm.certRedImage)"
                />
                <img
                  :src="ruleForm.certCarImage"
                  style="width: 100%"
                  @click="seeBigImg(ruleForm, ruleForm.certCarImage)"
                />
                <img
                  :src="ruleForm.carLicenseImage"
                  style="width: 100%"
                  @click="seeBigImg(ruleForm, ruleForm.carLicenseImage)"
                />
              </el-col>
              <el-col :span="16">
                <p v-if="ruleForm.auditFailReason" class="not-pass">
                  上次审核不通过原因：{{ ruleForm.auditFailReason }}
                </p>
                <p v-else>&nbsp;</p>
                <div class="vehicle-info">
                  <div class="info-title">车辆名称</div>
                  <div>{{ ruleForm.title }}</div>
                  <div class="info-title">车牌号</div>
                  <div>
                    {{ ruleForm.licenseNumber }}
                    <span v-if="ruleForm.placeTime"
                      >（{{
                        $date.format(ruleForm.placeTime, 'YYYY年M月')
                      }}上牌）</span
                    >
                  </div>
                </div>
                <div class="vehicle-description">
                  <p>车辆描述</p>
                  <el-input
                    v-model="ruleForm.vehicleCondition"
                    type="textarea"
                    :autosize="{ minRows: 4 }"
                    resize="none"
                    readonly
                  />
                </div>
                <div
                  v-if="
                    ruleForm.firstImage ||
                    (ruleForm.images && ruleForm.images.length)
                  "
                  class="vehicle-image"
                >
                  <div v-if="ruleForm.firstImage">
                    <img
                      :src="ruleForm.firstImage"
                      @click="seeBigImg(ruleForm, ruleForm.firstImage)"
                    />
                    <span>封面</span>
                  </div>
                  <div v-for="(image, index) in ruleForm.images" :key="index">
                    <img :src="image" @click="seeBigImg(ruleForm, image)" />
                  </div>
                </div>
                <!-- <el-form ref="ruleForm" :model="ruleForm" :inline="true" label-width="100px" label-position="left">
                                <p class="info-message-mes">
                                  <el-form-item label="审核状态" label-width="120px">
                                    <span :class="classList[ruleForm.status]">{{ ststusList[ruleForm.status] }}</span>
                                  </el-form-item>
                                </p>
                                <p class="info-message-mes">
                                  <el-form-item label="不通过原因" label-width="120px">
                                    <span>{{ ruleForm.auditFailReason }}</span>
                                  </el-form-item>
                                </p>
                                <p class="info-message-mes">
                                  <el-form-item label="品牌车型" label-width="120px">
                                    <span>{{ ruleForm.title }}</span>
                                  </el-form-item>
                                </p>
                                <p class="info-message-mes">
                                  <el-form-item label="车辆描述" label-width="120px">
                                    <span>{{ ruleForm.vehicleCondition }}</span>
                                  </el-form-item>
                                </p>
                                <p v-if="ruleForm.placeStatus !== 1 && ruleForm.placeStatus !== 2" class="info-message-mes">
                                  <el-form-item label="车牌号" label-width="120px">
                                    <span>{{ ruleForm.licenseNumber }}</span>
                                  </el-form-item>
                                </p>
                                <p v-if="ruleForm.placeStatus !== 1 && ruleForm.placeStatus !== 2" class="info-message-mes">
                                  <el-form-item label="上牌时间" label-width="120px">
                                    <span>{{ $date.format(ruleForm.placeTime, 'YYYY年M月') }}</span>
                                  </el-form-item>
                                </p>
                                <p class="info-message-mes">
                                  <el-form-item label="刹车辅助系统" label-width="120px">
                                    <span>{{ ruleForm.brake }}</span>
                                  </el-form-item>
                                </p>
                                <p class="info-message-mes">
                                  <el-form-item label="提供骑行装备" label-width="120px">
                                    <template v-for="(item, index) in ruleForm.equipment">
                                      <span :key="index" class="space-between">{{ equipmentList[item] }}</span>
                                    </template>
                                  </el-form-item>
                                </p>
                                <p class="info-message-mes">
                                  <el-form-item label="车辆保险情况" label-width="120px">
                                    <template v-for="(item, index) in ruleForm.insurance">
                                      <span :key="index" class="space-between">{{ insuranceList[item] }}</span>
                                    </template>
                                  </el-form-item>
                                </p>
                                <p class="info-message-mes">
                                  <el-form-item label="服务：" label-width="120px">
                                    <span v-if="ruleForm.quickConfirm === 1" class="service-name">闪电接单 </span>
                                    <span v-if="ruleForm.transportation === 1" class="service-name">支持送取 </span>
                                  </el-form-item>
                                </p>
                                <p v-if="ruleForm.images" class="info-message-mes">
                                  <el-form-item label="车辆图片" label-width="120px">
                                    <img
                                      v-for="(image, index) in ruleForm.images"
                                      :key="index"
                                      :src="image"
                                      style="width: 80px"
                                      @click="seeBigImg(ruleForm, image)"
                                    />
                                  </el-form-item>
                                </p>
                              </el-form> -->
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
    <reject-notice
      ref="rejectNotice"
      :show-radio="true"
      :radio-options="rejectOptions"
      @confirmRejection="getRejectData"
    />
    <choose-show-image ref="showImage" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import RejectNotice from '../components/rejectNotice.vue'
import rentalService from '@/api/rental'
import { rentalCarAudit } from '@/api/rental'

export default {
  name: 'RentCarAudit',
  components: {
    ChooseShowImage,
    RejectNotice
  },
  data() {
    return {
      id: '',
      carList: [],
      status: 0,
      loading: false, // 加载状态
      showModule: false, // 是否显示
      myData: {}, // 我的数据
      ruleForm: {},
      isModifyStatus: false, // 是否有修改
      isContinuous: true, // 是否连续点击
      submiting: false, // 是否提交中
      isfetch: true, // 是否连续取十条
      classList: {
        0: 'not-launched',
        1: 'not-pass',
        2: 'have-launched'
      },
      ststusList: {
        0: '审核中',
        1: '审核未通过',
        2: '审核通过'
      },
      rejectOptions: [
        {
          index: 1,
          content: '车牌号与图片不一致'
        },
        {
          index: 2,
          content: '车况描述有违规信息'
        },
        {
          index: 3,
          content: '标题包含违规信息'
        },
        {
          index: 4,
          content: '请勿使用带水印网图'
        }
      ],
      equipmentList: {
        1: '头盔',
        2: '骑行服',
        3: '护具'
      },
      insuranceList: {
        1: '三者险',
        2: '盗抢险',
        3: '车损险'
      }
    }
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  watch: {},
  activated() {
    const me = this
    me.loading = true
    me.getStatistics()
    me.getAuditList()
  },
  methods: {
    // 实时更新总数居
    getStatistics() {
      const me = this
      rentalService.getCarAuditStatistics({}).then((response) => {
        if (response.data.code === 0) {
          me.myData = response.data.data
        }
      })
    },
    // 拉取数据
    getList(type) {
      const me = this
      if (me.carList.length) {
        return me.$message.error('还有审核数据，不能操作')
      }
      rentalService
        .postCarAuditPull({
          orderType: type
        })
        .then((response) => {
          if (response.data.code === 0) {
            me.getAuditList()
            me.getStatistics()
            me.showModule = false
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 拉取待审核数据
    getAuditList() {
      const me = this
      rentalService
        .getCarAuditList({
          limit: 20,
          page: 1,
          orderType: 1
        })
        .then((response) => {
          if (response.data.code === 0) {
            me.carList = response.data.data
            me.setDetail(me.carList)
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 展示第一条数据
    setDetail(data) {
      const onlyData = (data && data[0]) || {}
      this.id = onlyData.id ? onlyData.id : ''
      this.showModule = !!this.id
      if (onlyData.id) {
        this.getDetail(this.id)
        this.showModule = true
      }
    },
    // Todo 获取详情
    getDetail(id) {
      rentalService
        .getCarDetail(id)
        .then((res) => {
          this.ruleForm = {
            ...res.data.data,
            images: res.data.data.images ? res.data.data.images.split(',') : [],
            equipment: res.data.data.equipment.split(','),
            insurance: res.data.data.insurance.split(',')
          }
        })
        .catch(() => {})
    },
    openDetails(row) {
      const me = this
      if (me.isContinuous) {
        me.id = row.id
        me.getDetail(me.id)
        me.isContinuous = false
        me.changeState()
        me.showModule = true
      }
    },
    changeState() {
      const me = this
      setTimeout(() => {
        me.isContinuous = true
      }, 1000)
    },
    changeStatus(status) {
      const me = this
      setTimeout(() => {
        if (status === 1) {
          me.$refs.rejectNotice.init()
          return
        }
        me.updataStatusData({}, '2')
      }, 50)
    },
    // 拒绝回来的数据
    getRejectData(data) {
      this.updataStatusData(data, '1')
    },
    updataStatusData(rejectData, status) {
      const me = this
      rentalCarAudit({
        rentalCarId: me.id,
        status,
        reason: rejectData.auditFailReason || ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
          } else {
            me.$message.error(response.data.msg)
          }
          me.getAuditList()
        })
        .catch((err) => {
          const code =
            err.response && err.response.data && err.response.data.code
          if (code === 111111 || code === 111112) {
            me.$message.success(
              err.message || err.response.data.msg || '操作成功'
            )
          } else {
            me.$message.error(err.message || '操作失败')
          }
          me.getAuditList()
        })
    },
    takeMyJob() {
      const me = this
      me.$router.push({
        name: 'MyRentCarAuditList'
      })
    },
    // 大图查看图片
    seeBigImg(data, img) {
      const title = data.licenseNumber
        ? `首次上牌：${this.$date.format(data.placeTime, 'YYYY')}, 车牌号：${
            data.licenseNumber
          }`
        : ''
      this.$refs.showImage.init(img, title)
    }
  }
}
</script>

<style lang="scss" scoped>
.contentreview {
  margin: 0px 20px 0;
  .my-data-list {
    display: inline-block;
    text-align: center;
    margin-left: 10px;
    .num {
      font-weight: bold;
      font-size: 30px;
    }
    .is-day-over {
      color: green;
    }
    .is-month-over {
      color: blue;
    }
    .is-achievements {
      color: green;
    }
    .is-day-ranking {
      color: blue;
    }
  }
  .not-launched {
    color: #999;
  }
  .have-launched {
    color: #00cc67;
  }
  .not-pass {
    color: red;
  }
  .space-between {
    margin-right: 10px;
  }
  .vehicle-info {
    display: flex;
    border: 1px solid #000000;
    > div {
      padding: 20px 10px;
      border-left: 1px solid #000000;
      &:nth-child(2n + 1) {
        width: 120px;
        text-align: center;
      }
      &:nth-child(2n) {
        width: 260px;
        flex: 1;
      }
      &:nth-child(1) {
        border-left: none;
      }
    }
    .info-title {
      font-weight: 600;
    }
  }
  .vehicle-image {
    > div {
      display: inline-block;
      width: 190px;
      height: 135px;
      position: relative;
      margin: 10px 10px 0 0;
      border: 1px solid #dedede;
      img {
        width: 188px;
        height: 133px;
        object-fit: cover;
      }
      span {
        position: absolute;
        top: -1px;
        left: -1px;
        font-size: 12px;
        display: inline-block;
        height: 22px;
        width: 56px;
        line-height: 22px;
        text-align: center;
        background-color: #fff;
        border: 1px solid #dedede;
      }
    }
  }
}
</style>
