<template>
  <div class="used-form">
    <el-form
      v-if="!isCarAudit"
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      :inline="true"
    >
      <div class="container">
        <div class="item-left">
          <div class="top-box">
            <div>
              <el-tooltip
                effect="dark"
                placement="bottom"
                :disabled="showDrivingLicenseName"
              >
                <template v-slot:content>
                  <div>
                    <div
                      v-for="(item, index) in ruleForm.drivingLicenseName || []"
                      :key="index"
                      :class="{ mt3: index }"
                    >
                      {{ item }}
                    </div>
                  </div>
                </template>
                <span class="label">{{ ruleForm.title }}</span>
              </el-tooltip>
              <span class="ml10">车源ID：{{ ruleForm.id }}</span>
              <el-popover placement="bottom-start" :width="300" trigger="click">
                <template #reference>
                  <img
                    src="/public/static/img/qrCode.png"
                    alt=""
                    class="qr-code"
                  />
                </template>
                <div ref="qrCodeBox"></div>
              </el-popover>
              <span class="ml10">浏览量：{{ ruleForm.viewCnt }}</span>
              <div class="mt10">
                <el-tooltip
                  v-if="ruleForm.goodsId === 0"
                  class="item"
                  effect="dark"
                  content="该车辆为自定义车辆"
                  placement="top-start"
                >
                  <el-tag type="info" color="#ffffff">自定义</el-tag>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="
                    ruleForm.shopId === 0
                      ? '该车辆通过主版本发布'
                      : '该车辆通过商家版发布'
                  "
                  placement="top-start"
                >
                  <el-tag :type="ruleForm.shopId === 0 ? 'warning' : ''">{{
                    ruleForm.shopId === 0 ? 'C端发布' : 'B端发布'
                  }}</el-tag>
                </el-tooltip>
                <el-tooltip
                  v-if="ruleForm.relatedMemberShop"
                  class="item"
                  effect="dark"
                  content="发布该车辆的用户关联了有效经销商"
                  placement="top-start"
                >
                  <el-tag type="info" color="#ffffff">关联经销商</el-tag>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="ruleForm.ifSell ? '该车辆已售出' : '该车辆未售出'"
                  placement="top-start"
                >
                  <el-tag
                    :type="ruleForm.ifSell ? 'danger' : 'info'"
                    :color="ruleForm.ifSell ? '' : '#ffffff'"
                    >{{ ruleForm.ifSell ? '已售出' : '未售出' }}</el-tag
                  >
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="ruleForm.isFreeze ? '该车辆已冻结' : '该车辆未冻结'"
                  placement="top-start"
                >
                  <el-tag
                    :type="ruleForm.isFreeze ? '' : 'info'"
                    :color="ruleForm.isFreeze ? '' : '#ffffff'"
                    >{{ ruleForm.isFreeze ? '已冻结' : '未冻结' }}</el-tag
                  >
                </el-tooltip>
              </div>
            </div>
            <div>
              <!-- <span class="label-status" v-if="ruleForm.issueCarNotHandle">申诉待处理</span>
                              <span class="label-status" v-else :class="classList[ruleForm.status]">{{
                                ruleForm.status !== 2 ? ststusList[ruleForm.status] : appealStatusList[ruleForm.appealStatus] || '待审核'
                              }}</span> -->
              <span
                class="label-status"
                v-if="ruleForm.scene !== 9"
                :class="sceneClassList[ruleForm.scene]"
                >{{ sceneList[ruleForm.scene] }}</span
              >
              <span
                class="label-status"
                v-else
                :class="classList[ruleForm.status]"
                >{{ ststusList[ruleForm.status] }}</span
              >
              <el-button
                class="mt10"
                v-if="ruleForm.status === 1 || ruleForm.status === 3"
                style="margin-left: 10px"
                @click="changeStatus(ruleForm.status)"
                >{{
                  ruleForm.status === 1 ? '变更为审核不通过' : '变更为审核通过'
                }}</el-button
              >
            </div>
          </div>
          <div class="modules">
            <p class="modules-title">发布信息</p>
            <el-table :data="basicTable" border>
              <el-table-column
                align="center"
                prop="userName"
                label="发布用户"
                width="183"
              >
                <template #default="scope">
                  <div>{{ scope.row.userName }}</div>
                  <el-button
                    style="user-select: auto"
                    @click="jumpById('user', scope.row.id)"
                    type="primary"
                    link
                    >{{ scope.row.id }}</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column align="center" label="经销商" width="183">
                <template #default="scope">
                  <div
                    v-if="
                      ruleForm.userShopInfoVos &&
                      ruleForm.userShopInfoVos.length &&
                      scope.row.shopId !== 0
                    "
                  >
                    <div>{{ scope.row.shopName }}</div>
                    <el-button
                      link
                      style="user-select: auto"
                      @click="jumpById('shop', scope.row.shopId)"
                      type="primary"
                      >{{ scope.row.shopId }}</el-button
                    >
                  </div>
                  <span v-else>/</span>
                </template>
              </el-table-column>
              <el-table-column
                width="183"
                align="center"
                prop="mobile"
                label="发布人电话"
              >
                <template v-slot="scope">
                  <span> {{ scope.row.mobile }}&ensp; </span>
                  <el-button
                    size="small"
                    v-if="ruleForm.mobile"
                    @click="seeMobile(ruleForm.mobile, 1, 12)"
                    >查看</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                prop="wechatName"
                label="发布人微信"
                width="183"
              >
                <template v-slot="scope">
                  <span>{{ scope.row.wechatName || '/' }}</span>
                  <el-button
                    size="small"
                    v-if="scope.row.wechatName"
                    @click="seeMobile(scope.row.wechatName, 2, 27)"
                    >查看</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column
                width="184"
                align="center"
                prop="mobile"
                label="经销商电话"
              >
                <template v-slot="scope">
                  <div
                    v-if="
                      ruleForm.userShopInfoVos &&
                      ruleForm.userShopInfoVos.length
                    "
                  >
                    <span> {{ scope.row.mobile }}&ensp; </span>
                    <el-button
                      size="small"
                      v-if="scope.row.mobile"
                      @click="seeMobile(scope.row.mobile, 1, 23)"
                      >查看</el-button
                    >
                  </div>
                  <span v-else>/</span>
                </template>
              </el-table-column>
              <el-table-column
                width="184"
                align="center"
                prop="mobile"
                label="所有人"
              >
                <template v-slot="scope">
                  <span v-if="scope.row.ocrOwnerName"
                    >{{ scope.row.ocrOwnerName }}&ensp;</span
                  >
                  <span v-else>/</span>
                  <el-button size="small" @click="seeOcrOwnerName()"
                    >查看</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <p class="modules-title">
              车辆信息
              <el-tooltip
                effect="dark"
                placement="bottom"
                v-if="
                  ruleForm.auditTips ||
                  (ruleForm.auditTipsImagesList &&
                    ruleForm.auditTipsImagesList.length)
                "
              >
                <template v-slot:content>
                  <div>
                    <pre
                      v-if="ruleForm.auditTips"
                      v-html="ruleForm.auditTips"
                    ></pre>
                    <div style="display: flex">
                      <img
                        v-for="(img, index) in ruleForm.auditTipsImagesList"
                        :key="index"
                        :src="img"
                        style="
                          width: 100px;
                          height: 100px;
                          margin-right: 5px;
                          object-fit: cover;
                        "
                        @click="
                          seeBigImg1(ruleForm.auditTipsImagesList, index, true)
                        "
                      />
                    </div>
                  </div>
                </template>
                <el-tag class="modules-title_tip" type="info" color="#ffffff"
                  >审核提示</el-tag
                >
              </el-tooltip>
            </p>
            <el-table :data="carInfo" border>
              <el-table-column
                width="220"
                prop="brandName"
                label="品牌"
                align="center"
              >
                <template #default="{ row }">
                  <span>{{ row.brandName }}</span>
                  <el-tag v-if="ruleForm.goodsId === 0" type="info"
                    >自定义</el-tag
                  >
                </template>
              </el-table-column>

              <el-table-column
                width="220"
                prop="goodsName"
                label="车型/款型"
                align="center"
              >
                <template #default="{ row }">
                  <span>{{ row.carName || row.goodsName }}</span>
                  <el-tag v-if="ruleForm.goodsId === 0" type="info"
                    >自定义</el-tag
                  >
                </template>
              </el-table-column>
              <el-table-column width="220" label="上牌类型" align="center">
                <span :style="{ color: colors[ruleForm.placeStatus] }">{{
                  cardType[ruleForm.placeStatus]
                }}</span>
                <span v-if="ruleForm.placeStatus === 1"
                  >（{{ ruleForm.billNum }}）</span
                >
                <div v-if="ruleForm.licensePlate && !ruleForm.placeStatus">
                  {{ ruleForm.licensePlateAddress }}（{{
                    ruleForm.licensePlate
                  }}）
                </div>
              </el-table-column>
              <el-table-column
                width="220"
                prop="price"
                label="转让价格"
                align="center"
              >
                <template v-slot="scope">
                  {{ scope.row.price
                  }}{{
                    ruleForm.placeStatus === 2 && ruleForm.carBalancePayment
                      ? `（尾款：${ruleForm.carBalancePayment}）`
                      : ''
                  }}
                </template>
              </el-table-column>
              <el-table-column
                width="220"
                prop="price"
                label="参考售价"
                align="center"
              >
                <template #default>
                  <span>{{
                    ruleForm.minPrice && ruleForm.maxPrice
                      ? `${ruleForm.minPrice} - ${ruleForm.maxPrice}`
                      : '/'
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
            <el-table
              v-if="relatedCarInfo.length"
              ref="articleList"
              :data="relatedCarInfo"
              row-key="articleList"
              border
              :style="{
                marginTop: '20px',
                width:
                  ruleForm.placeStatus !== 2
                    ? ruleForm.placeStatus !== 1
                      ? '1101px'
                      : '940px'
                    : '660px'
              }"
            >
              <el-table-column
                width="220"
                prop="address"
                :label="ruleForm.placeStatus === 2 ? '提车地点' : '看车地点'"
                align="center"
              />
              <el-table-column
                width="220"
                :label="titleList[ruleForm.placeStatus]"
                align="center"
              >
                <template #default>
                  <div v-if="ruleForm.placeStatus === 0">
                    <div v-if="ruleForm.placeTime">
                      注册：{{ $date.format(ruleForm.placeTime, 'YYYY-MM-DD') }}
                    </div>
                    <div v-if="ruleForm.discardTime">
                      报废：{{
                        $date.format(ruleForm.discardTime * 1000, 'YYYY-MM-DD')
                      }}
                    </div>
                  </div>
                  <span v-else>{{
                    $date.format(ruleForm.placeTime, 'YYYY-MM-DD')
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                width="220"
                label="交强险到期时间"
                align="center"
              >
                <template #default>
                  <div>{{ setInsuranceTime(ruleForm.insuranceTime) }}</div>
                </template>
              </el-table-column>
              <el-table-column
                width="120"
                prop="greenStandards"
                label="环保标准"
                align="center"
              >
                <template v-slot="scope">
                  {{
                    scope.row.greenStandards === 1
                      ? '国Ⅲ'
                      : scope.row.greenStandards === 2
                      ? '国Ⅳ'
                      : '/'
                  }}
                </template>
              </el-table-column>
              <template v-if="ruleForm.placeStatus !== 2">
                <el-table-column
                  width="160"
                  prop="mileage"
                  label="行驶里程"
                  align="center"
                />
                <el-table-column
                  v-if="ruleForm.placeStatus !== 1"
                  width="160"
                  prop="transferTimes"
                  label="过户次数"
                  align="center"
                />
              </template>
            </el-table>
            <p class="modules-title">车辆描述及服务</p>

            <pre class="pre-content">{{ ruleForm.vehicleCondition }}</pre>

            <el-form-item label="服务承诺" style="margin: 0">
              <template v-if="provideService.length">
                <el-tag
                  style="margin-right: 10px; background: white; color: black"
                  effect="plain"
                  type="info"
                  v-for="(l, i) in provideService"
                  :key="i"
                >
                  {{ l }}
                </el-tag>
              </template>
              <span v-else>无</span>
            </el-form-item>

            <p class="modules-title">日志信息</p>
            <div class="add-log">
              <div class="log-content">
                <el-input
                  v-model="logInfo"
                  type="textarea"
                  :autosize="{ minRows: 4 }"
                />
              </div>
              <el-button @click="addLog">添加日志</el-button>
            </div>
            <div style="width: 85%">
              <el-table :data="logList">
                <el-table-column label="日志" align="center">
                  <template #default="{ row }">
                    <div class="text-align-left" v-html="row.remark"></div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="operateDate"
                  label="时间"
                  align="center"
                  width="200px"
                >
                  <template #default="{ row }">{{
                    $date.format(row.operateDate, 'YYYY-MM-DD HH:mm:ss')
                  }}</template>
                </el-table-column>
                <el-table-column
                  prop="userName"
                  label="操作人"
                  align="center"
                  width="150px"
                />
              </el-table>
              <el-pagination
                v-if="logTotal > 10"
                layout="total, prev, pager, next"
                :total="logTotal"
                v-model:current-page="logPage"
                :page-size="10"
                style="justify-content: center; margin-top: 10px"
                @current-change="currentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
        <div class="img-container">
          <p class="modules-title">
            图片信息
            <el-button
              type="primary"
              link
              v-if="illeagelImageData.length"
              @click="showIlleagelImageData"
              >不通过·图片</el-button
            >
          </p>
          <div class="img-area">
            <!-- <div class="item">
              <div class="img-container_top">
                <div>
                  <div class="mb10">车辆审核图片</div>
                  <img
                    :src="ruleForm.drivingLicenseImage | replaceImgUrl"
                    alt
                    @click="seeBigImg(ruleForm.drivingLicenseImage, null, true)"
                  />
                </div>
                <div class="ml10" v-if="ruleForm.appealImages.length">
                  <div class="mb10">车辆申诉图片</div>
                  <img
                    v-for="(item, index) in ruleForm.appealImages"
                    :key="index"
                    :src="item"
                    alt
                    @click="seeBigImg(item)"
                  />
                </div>
                <div v-if="referenceCarImage" class="ml10">
                  <div class="mb10">车型参考图片</div>
                  <img
                    :src="referenceCarImage"
                    alt
                    @click="seeBigImg(referenceCarImage)"
                  />
                </div>
              </div>
            </div> -->
            <div class="item mt20">
              <span>车辆审核图片</span>
              <div class="img-box">
                <template v-if="ruleForm.appealImages.length">
                  <div
                    class="mr10 consult"
                    v-for="(item, index) in ruleForm.appealImages"
                    :key="index"
                    @click="seeBigImg(item)"
                  >
                    <img :src="item" alt />
                    <span class="tag-item">车辆申诉证明</span>
                  </div>
                </template>
                <div
                  class="mr10 consult"
                  @click="seeBigImg(ruleForm.drivingLicenseImage, null, true)"
                >
                  <img
                    :src="$filters.replaceImgUrl(ruleForm.drivingLicenseImage)"
                    alt
                  />
                  <span v-if="labelType[ruleForm.placeStatus]" class="tag-item">
                    {{ labelType[ruleForm.placeStatus] }}
                  </span>
                </div>
                <div
                  v-if="ruleForm.copyDrivingLicenseImages"
                  class="mr10 consult"
                  @click="
                    () => {
                      seeBigImg(ruleForm.copyDrivingLicenseImages, null, true)
                    }
                  "
                >
                  <img
                    :src="
                      $filters.replaceImgUrl(ruleForm.copyDrivingLicenseImages)
                    "
                    alt
                  />
                  <span class="tag-item">行驶证副本</span>
                </div>
                <div
                  v-if="ruleForm.realCarImages"
                  class="mr10 consult"
                  @click="seeBigImg(ruleForm.realCarImages, null, true)"
                >
                  <img
                    :src="$filters.replaceImgUrl(ruleForm.realCarImages)"
                    alt
                  />
                  <span class="tag-item">带牌实车照</span>
                </div>
              </div>
            </div>
            <div v-if="referenceCarImage.length" class="item mt20">
              <span>车型参考图片</span>
              <div class="img-box">
                <div
                  class="mr10 consult"
                  v-for="(item, index) in referenceCarImage"
                  :key="index"
                  @click="seeBigImg(item.goodsThumb)"
                >
                  <img :src="item.goodsThumb" alt />
                  <span v-if="item.goodsCarName" class="tag-item">
                    {{ item.goodsCarName }}
                  </span>
                </div>
              </div>
            </div>
            <div class="item mt20">
              <span>车辆视频/图片</span>
              <div v-if="ruleForm.videoImagesDetailVOS" class="img-box">
                <div
                  @click="seeVideoBigImg(image)"
                  class="mr10 consult"
                  v-for="(image, index) in ruleForm.videoImagesDetailVOS"
                  :key="index"
                >
                  <img
                    :src="
                      image.type === 1
                        ? image.imagesTypeUrl.imgUrl
                        : image.videoInfoDTO.coverUrl
                    "
                    alt
                  />
                  <div v-if="image.type === 2" class="video-icon">
                    <el-icon class="icon el-icon-caret-right"></el-icon>
                  </div>
                  <span v-if="index === 0" class="tag-item">
                    {{ image.type === 2 ? '封面 | 视频' : '封面' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <choose-show-image ref="showImage" />
    <ChooseCommonDecrypt ref="seePhone" />
    <ChooseOcrOwnerName ref="seeOcrOwnerName" @success="getDetail" />
    <RefusalCause ref="refusalCause" @confirmRejection="getRejectData" />
    <IlleagelImageModel ref="illeagelImageModel" />
  </div>
</template>

<script>
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import ChooseCommonDecrypt from '@/components/Dialog/ChooseCommonDecrypt.vue'
import ChooseOcrOwnerName from './ChooseOcrOwnerName.vue'
import RefusalCause from '../components/refusalCause.vue'
import IlleagelImageModel from './IlleagelImageData.vue'
import {
  getNewCarDetail,
  updateAuditStatus,
  addOperateLog,
  getOperationLog,
  getIlleagelImage
} from '@/api/garage'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
import { replaceImgUrl } from '@/filters'
import QRCode from 'qrcodejs2-fix'

export default {
  components: {
    ChooseShowImage,
    ChooseCommonDecrypt,
    RefusalCause,
    IlleagelImageModel,
    ChooseOcrOwnerName
  },
  props: {
    isDetail: {
      type: Boolean,
      default() {
        return false
      }
    },
    isCarAudit: {
      type: Boolean,
      default() {
        return false
      }
    } // 审核
  },
  data() {
    return {
      colors: {
        0: '#8DBF0C',
        1: '#F6A524',
        2: '#3EABE4'
      },
      videoCar: null,
      usedCarId: '', // 详情id
      loading: false,
      sceneList: {
        0: '删除',
        1: '申诉中',
        2: '审核中',
        3: '审核不通过',
        4: '已上架',
        5: '售出下架',
        6: '主动下架',
        7: '系统下架',
        8: '系统下架',
        9: '车辆冻结',
        10: '申诉待处理'
      },
      sceneClassList: {
        0: 'be-audited',
        3: 'have-launched',
        4: 'not-launched',
        5: 'not-launched',
        9: 'freeze'
      },
      classList: {
        0: 'be-audited',
        1: 'not-launched',
        2: 'not-pass',
        3: 'have-launched',
        4: 'be-audited',
        5: 'not-launched',
        9: 'freeze'
      },
      ststusList: {
        0: '系统下架',
        1: '正常',
        2: '待审核',
        3: '审核不通过',
        4: '个人删除',
        5: '已售出',
        6: '用户下架',
        7: '系统下架',
        8: '发票过期',
        9: '已冻结',
        10: '受限下架'
      },
      appealStatusList: {
        1: '申诉中',
        2: '申诉成功',
        3: '申诉失败'
      },
      // 接口文档对应 显示
      serviceList: [
        '随时看车',
        '车况描述真实',
        '支持验车',
        '赠送保养',
        '代办过户',
        '包过户',
        '定金可退',
        '7天可退',
        '一口价',
        '可小刀',
        '支持三方验车',
        '准新车',
        '订单车',
        '0次过户',
        '只出本地',
        '包运费'
      ], // 所有可选服务
      serviceListNew: {
        4: '赠送保养',
        19: '包上牌费',
        20: '包运费'
      },
      provideService: [], // 已有的服务
      ruleForm: {
        appealImages: []
      }, // 展示数据
      rules: {},
      carInfo: [], // 车辆信息
      relatedCarInfo: [], // 车辆相关信息
      cardType: {
        0: '已上牌',
        1: '未上牌',
        2: '订单车'
      },
      labelType: {
        0: '行驶证',
        1: '发票',
        2: '合同'
      },
      titleList: {
        0: '注册时间/报废时间',
        1: '开票时间',
        2: '提车时间'
      },
      reasonForFailure: {
        7: '连续30天未刷新车源',
        8: '发票已过期，请重新开票后发布'
      },
      logInfo: '',
      logList: [],
      illeagelImageData: [], // 违法违规不通过图片
      logPage: 1,
      logTotal: 0,
      showName: '',
      referenceCarImage: []
    }
  },
  computed: {
    basicTable() {
      return [
        {
          wechatName: this.ruleForm.wechatName,
          shopName: this.ruleForm.shopName,
          shopId: this.ruleForm.shopId,
          userName: this.ruleForm.userName,
          id: this.ruleForm.userId,
          mobile: this.ruleForm.mobile,
          ocrOwnerName: this.ruleForm.ocrOwnerName
        }
      ]
    },
    previewedImgs() {
      const drivingLicenseImage =
        replaceImgUrl(this.ruleForm.drivingLicenseImage) || ''
      const copyDrivingLicenseImages =
        replaceImgUrl(this.ruleForm.copyDrivingLicenseImages) || ''
      const realCarImages = replaceImgUrl(this.ruleForm.realCarImages) || ''
      let carImageVideo = []
      this.ruleForm.videoImagesDetailVOS &&
        this.ruleForm.videoImagesDetailVOS.map((item) => {
          if (item.type === 1) {
            carImageVideo.push(replaceImgUrl(item.imagesTypeUrl.imgUrl))
          }
        })
      const goodsAndUserImage =
        this.ruleForm.goodsAndUserImage &&
        this.ruleForm.goodsAndUserImage.length
          ? this.ruleForm.goodsAndUserImage.split(',')
          : []
      let imgs = [
        ...this.ruleForm.appealImages,
        drivingLicenseImage,
        copyDrivingLicenseImages,
        realCarImages,
        ...this.referenceCarImage.map((item) => {
          return item.goodsThumb
        }),
        ...goodsAndUserImage,
        ...carImageVideo
      ]
      imgs = imgs.filter((img) => img)
      return imgs
    },
    showDrivingLicenseName() {
      return this.ruleForm.drivingLicenseName &&
        this.ruleForm.drivingLicenseName.length
        ? false
        : true
    }
  },
  watch: {},
  activated() {},
  methods: {
    setInsuranceTime(time) {
      if (!time) return ''
      const t_now = new Date().getTime()
      const t = new Date(time).getTime()
      if (t_now > t) {
        return '已到期'
      }
      return this.$date.format(time, 'YYYY-MM')
    },
    jumpById(type, id) {
      this.$router.push({
        name:
          type === 'user'
            ? 'userAccountCorrelationDetail'
            : 'DistributorDetails',
        query: {
          id
        }
      })
    },
    init(data) {
      this.usedCarId = data.id
      this.getDetail()
      this.currentChange(1)
      this.getQrCode()
      this.getIlleagelImageData()
    },
    getQrCode() {
      if (this.usedCarId) {
        this.$refs.qrCodeBox.innerHTML = ''
        new QRCode(this.$refs.qrCodeBox, {
          text: `https://m.58moto.com/used-car/detail/${this.usedCarId}`,
          width: 274,
          height: 274,
          colorDark: '#333333', // 二维码颜色
          colorLight: '#ffffff', // 二维码背景色
          correctLevel: QRCode.CorrectLevel.L // 容错率，L/M/H
        })
      }
    },
    getDetail() {
      const me = this
      me.loading = true
      getNewCarDetail({
        id: me.usedCarId
      })
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data || {}
            if (!data.id) {
              return me.$message.error('没有查询到数据')
            }
            batchRecordBeforeAlter(data, me.usedCarId)
            data.imgList =
              data.images && data.images.length ? data.images.split(',') : []
            data.address = `${data.ownerProvince} ${data.ownerCity} ${
              data.ownerDistrict || ''
            }`
            me.carInfo = []
            const showName =
              data.carportName || data.carportGoodsName
                ? `${data.carportName || ''}${data.carportGoodsName || ''}`
                : `${data.brandName || ''}${data.goodsName || ''}`
            data.showName = showName
            me.carInfo.push({
              showName: showName,
              price: data.price,
              brandName: data.carportName || data.brandName,
              goodsName: data.goodsName || data.carportGoodsName,
              carName: data.carName
            })
            me.showName = showName
            me.relatedCarInfo = [
              {
                mileage: data.mileage,
                greenStandards: data.greenStandards,
                address: `${data.ownerProvince} ${data.ownerCity} ${
                  data.ownerDistrict || ''
                }`,
                transferTimes: data.transferTimes
              }
            ]
            me.provideService = []
            if (data.provideService) {
              data.provideService.split(',').map((_) => {
                if (me.serviceListNew[_]) {
                  me.provideService.push(me.serviceListNew[_])
                }
              })
            }
            data.showStatus = me.ststusList[data.status]
            data.auditTipsImagesList = []
            if (data.auditTipsImages && data.auditTipsImages.length) {
              data.auditTipsImages.map((item) => {
                const imgList = item.images.split(',')
                data.auditTipsImagesList =
                  data.auditTipsImagesList.concat(imgList)
              })
            }
            me.ruleForm = data
            me.ruleForm.appealImages = data.appealImages
              ? me.ruleForm.appealImages.split(',')
              : []
            me.referenceCarImage = data.carImage || []
            // if (data.hasReferenceCarImage) {
            //   let goodsAndUserImage = data.goodsAndUserImage || ''
            //   goodsAndUserImage = goodsAndUserImage
            //     ? goodsAndUserImage.split(',')
            //     : []
            //   data.goodsAndUserImage = goodsAndUserImage.join(',')
            //   me.referenceCarImage = me.referenceCarImage.length
            //     ? me.referenceCarImage
            //     : goodsAndUserImage.shift()
            // }
            // if (data.videoCar) {
            //   this.videoCar = data.videoData
            //   let goodsAndUserImage = data.goodsAndUserImage || ''
            //   goodsAndUserImage = goodsAndUserImage
            //     ? goodsAndUserImage.split(',')
            //     : []
            //   goodsAndUserImage.splice(0, 0, data.videoData.coverUrl)

            //   me.ruleForm.goodsAndUserImage = goodsAndUserImage.join(',')
            // }
          }
        })
        .catch((err) => {
          console.log(err)
          me.loading = false
        })
    },
    // 查看手机号码
    seeMobile(mobile, type, operateType) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({
          decryptContent: mobile,
          source: 'UsedDetail',
          type, // 1手机号解密，2微信号解密，3，身份证解密，4支付宝
          operateType
        })
    },
    // 大图查看图片
    seeBigImg(data, index, flag) {
      // if (this.videoCar && index === 0) {
      //   window.open(this.videoCar.sourceVideoUrl)
      //   return
      // }
      // const title1 = data.licensePlateAddress ? `首次上牌：${timeFullS(data.placeTime)}, 车牌号：${data.licensePlateAddress}` : ''
      // const title2 = data.billNum ? `发票时间：${timeFullS(data.placeTime)}, 发票号：${data.billNum}` : ''
      // const title = data && data.placeStatus === 0 ? title1 : data && data.placeStatus === 1 ? title2 : ''
      const img = data.drivingLicenseImage ? data.drivingLicenseImage : data
      const img1 = flag ? replaceImgUrl(img) : img
      const curIndex = this.previewedImgs.findIndex((item) => item === img1)
      this.$viewerApi({
        images: this.previewedImgs,
        options: {
          initialViewIndex: curIndex
        }
      })
    },
    // 车辆视频/图片查看大图
    seeVideoBigImg(data) {
      if (data.type === 2) {
        window.open(data.videoInfoDTO.sourceVideoUrl)
        return
      }
      const img = replaceImgUrl(data.imagesTypeUrl.imgUrl)
      const curIndex = this.previewedImgs.findIndex((item) => item === img)
      this.$viewerApi({
        images: this.previewedImgs,
        options: {
          initialViewIndex: curIndex
        }
      })
    },
    // 大图查看图片
    seeBigImg1(data, index) {
      this.$viewerApi({
        images: data,
        options: {
          initialViewIndex: index
        }
      })
    },
    // 变更状态
    changeStatus(status) {
      const me = this
      console.log(status)
      const errStatus = 3 // 异常状态，审核不通过
      const num = status === errStatus ? '1' : '3'
      const timeNum = 50
      setTimeout(() => {
        if (num === '3') {
          me.$refs.refusalCause.init(this.ruleForm.placeStatus)
          return
        }
        me.updataStatusData({}, num)
      }, timeNum)
    },
    // 拒绝回来的数据
    getRejectData(data) {
      this.updataStatusData(data, '3')
    },
    updataStatusData(rejectData, status) {
      const me = this
      updateAuditStatus({
        id: me.usedCarId,
        status: status,
        auditFailType: rejectData.auditFailType || '',
        subAuditFailReason: rejectData.subAuditFailReason || '',
        auditFailReason: rejectData.auditFailReason || '',
        auditFailRemark: rejectData.auditFailRemark || ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            setTimeout(() => {
              me.getDetail()
            }, 1000)
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    getLog() {
      getOperationLog({
        page: this.logPage,
        limit: 10,
        secondhandCarId: this.usedCarId
      }).then((res) => {
        if (res.data.code === 0) {
          this.logList = res.data.data.listData || []
          this.logTotal = res.data.data.total || 0
          this.logList.forEach((log) => {
            log.remark = log.remark.replaceAll('\\n', '<br />')
            log.remark = log.remark.replaceAll(
              '#r->',
              '<span style="color: red">-></span>'
            )
          })
        }
      })
    },
    currentChange(e) {
      this.logPage = e
      this.getLog()
    },
    addLog() {
      if (!this.logInfo) {
        return this.$message.error('请填写日志')
      }
      addOperateLog({
        remark: this.logInfo,
        secondhandCarId: this.usedCarId
      }).then((res) => {
        if (res.data.code === 0) {
          this.$message.success('操作成功')
          this.currentChange(1)
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    getIlleagelImageData() {
      const me = this
      getIlleagelImage({
        secondHandCarId: me.usedCarId
      })
        .then((response) => {
          const data = response.data.data || []
          me.illeagelImageData = data
        })
        .catch((err) => {
          console.log(err)
        })
    },
    showIlleagelImageData() {
      this.$refs.illeagelImageModel.init(this.illeagelImageData)
    },
    seeOcrOwnerName() {
      this.$refs.seeOcrOwnerName.init(this.usedCarId)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  .item-left {
    width: 1100px;
  }
  .img-container {
    margin-left: 10px;
    border-left: 1px solid gainsboro;
    padding-left: 10px;
    .img-container_top {
      display: flex;
      flex-wrap: wrap;
    }
  }
}
.img-box {
  display: flex;
  flex-wrap: wrap;
  .consult {
    display: inline-block;
    position: relative;
    .tag-item {
      position: absolute;
      top: 0;
      left: 0;
      display: inline-block;
      padding: 3px 5px;
      background-color: #ffffff;
      border: 1px solid #dedede;
      font-size: 12px;
    }
  }
  .video-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    width: 50px;
    height: 50px;
    border: 2px solid black;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    justify-content: center;
    align-items: center;

    .icon {
      font-size: 30px;
    }
  }
}
.used-form {
  padding: 20px;
  .top-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fbf8e9;
    padding: 10px 16px;
    .label,
    .label-status {
      font-size: 20px;
      font-weight: bold;
    }
    & > div:nth-child(2) {
      display: flex;
      flex-direction: column;
    }
    .label-status {
      margin-left: auto;
      // color: black;
    }
    .qr-code {
      width: 16px;
      height: 16px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .modules-title {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    font-weight: bold;
    padding: 5px 0;
  }
  .modules-title_tip {
    font-size: 14px;
    font-weight: normal;
    margin-left: 10px;
  }
  .pre-content {
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
  }
  .not-launched {
    color: #999;
  }
  .have-launched {
    color: #fac774;
  }
  .not-pass {
    color: red;
  }
  .be-audited {
    color: #cc6600;
  }
  .freeze {
    color: #3b86f7;
  }
  .bottom {
    margin: 0 0 16px 0;
  }
  .audit-data {
    border-right: 1px solid #dcdfe6;
    border-top: 1px solid #dcdfe6;
    .message {
      display: flex;
      div {
        color: #606266;
        padding: 15px 0;
        border-left: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        display: flex;
        align-items: center;
      }
      .title {
        width: 15%;
        justify-content: center;
      }
      .audit-content {
        width: 35%;
        padding-left: 10px;
        .color0 {
          color: #00cc67;
        }
        .color1 {
          color: #cc6600;
        }
        .color2 {
          color: #4b6bf7;
        }
        .reference {
          font-size: 14px;
        }
      }
    }
  }
  .license-plate-number {
    display: flex;
    flex-wrap: wrap;
    border-left: 1px solid #dcdfe6;
    // border-top: 1px solid #dcdfe6;
    div {
      color: #606266;
      padding: 10px 0;
      width: 20%;
      text-align: center;
      overflow: hidden;
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;
      &:nth-child(-n + 5) {
        border-top: 1px solid #dcdfe6;
      }
    }
  }
  .not-available {
    color: #999;
  }
  .consult {
    display: inline-block;
    position: relative;
    .tag-item {
      position: absolute;
      top: 0;
      left: 0;
      display: inline-block;
      padding: 3px 5px;
      background-color: #ffffff;
      border: 1px solid #dedede;
      font-size: 12px;
    }
  }
  .img-audit-box {
    .operation-icon {
      text-align: right;
      -moz-user-select: none;
      -webkit-user-select: none;
      padding: 10px;
      position: relative;
      z-index: 1001;
      // background-color: #f1f1f1;
      .size {
        font-size: 32px;
        margin: 0 5px;
        cursor: pointer;
      }
    }
  }
  .add-log {
    display: flex;
    align-items: center;
    width: 95%;
    .log-content {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.info-message {
  position: relative;
  padding-left: 500px;
  min-height: 380px;
}
.info-message-img-audit {
  display: inline-block;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1000;
}
.info-message-img {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-block;
  width: 500px;
  height: 375px;
  object-fit: cover;
}
.info-message-mes {
  margin: 0;
}
.image {
  display: inline-block;
  width: 200px;
  height: 150px;
  object-fit: cover;
  margin-right: 10px;
  border: 1px solid #dedede;
}
.img-area {
  .item {
    display: flex;
    flex-direction: column;
    > span {
      margin-bottom: 10px;
    }
    &:nth-child(1) {
      margin-right: 30px;
    }
    img {
      width: 180px;
      height: 150px;
    }
  }
}
.text-align-left {
  text-align: left;
}
</style>
