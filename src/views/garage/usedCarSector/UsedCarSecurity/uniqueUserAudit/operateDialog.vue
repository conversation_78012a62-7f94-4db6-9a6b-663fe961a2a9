<template>
  <el-dialog
    title="用户处理"
    center
    append-to-body
    v-model="visible"
    width="719px"
    class="unique-user-dialog"
    :before-close="handleClose"
  >
    <div>
      <span class="mr10">{{ ruleForm.relateName }}</span>
      <el-button @click="goUser(ruleForm.relateId)" type="primary" link>{{
        ruleForm.relateId
      }}</el-button>
    </div>
    <div class="mb10">
      风险标记：{{ detailsData.riskText || '无' }} | 相关工单：{{
        detailsData.relatedOrders || '无'
      }}
    </div>
    <div class="line"></div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="80px"
      class="demo-ruleForm"
    >
      <el-form-item label="处理方式" prop="processStat">
        <el-radio-group @change="handleChange" v-model="ruleForm.processStat">
          <el-radio :label="2">暂不处理</el-radio>
          <el-radio :label="3">标记风险</el-radio>
          <el-radio :label="1">提交工单</el-radio>
        </el-radio-group>
      </el-form-item>

      <template v-if="ruleForm.processStat !== ''">
        <el-form-item v-if="ruleForm.processStat === 3" label="">
          <el-checkbox-group v-model="checkList" @change="changeCheckList">
            <el-checkbox :label="1">风险弹窗</el-checkbox>
            <el-checkbox :label="2">禁止查看手机微信</el-checkbox>
            <el-checkbox :label="3">限制私信</el-checkbox>
            <el-checkbox :label="4">禁止发布二手车</el-checkbox>
            <el-checkbox :label="5">二手车限流</el-checkbox>
          </el-checkbox-group>
          <div class="flag-risk-tips">标记风险的操作将会更新现有的标记内容</div>
        </el-form-item>
        <el-form-item v-if="ruleForm.processStat === 1" label="问题类型">
          <el-select disabled :model-value="0">
            <el-option label="二手车/用户异常" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="ruleForm.processStat === 1 ? '问题描述' : '备注'"
          prop="workOrderDesc"
        >
          <el-input
            type="textarea"
            :placeholder="ruleForm.processStat === 2 ? '请输入备注信息' : ''"
            v-model="ruleForm.workOrderDesc"
          ></el-input>
          <div
            v-if="ruleForm.processStat === 1"
            style="display: flex; justify-content: flex-end"
          >
            <el-checkbox v-model="ruleForm.urgent">紧急问题</el-checkbox>
          </div>
        </el-form-item>
        <el-form-item
          v-if="ruleForm.processStat === 1"
          label="关联对象"
          prop="processStat"
        >
          <div class="relation">
            <el-radio-group v-model="ruleForm.relateType">
              <el-radio
                :disabled="option.disabled"
                v-for="(option, index) in relations"
                :key="index"
                :label="index + 1"
                >{{ option.label }}</el-radio
              >
            </el-radio-group>
            <el-input
              disabled
              style="width: 150px; margin-left: 20px"
              v-model="ruleForm.relateName"
            ></el-input>
            <span class="ml5">{{ ruleForm.relateId }}</span>
          </div>
        </el-form-item>
        <el-form-item
          v-if="ruleForm.processStat === 1"
          label="责任人"
          prop="name"
        >
          <el-autocomplete
            v-model="ruleForm.dutyName"
            :fetch-suggestions="querySearchDuty"
            :placeholder="ruleForm.dutyName || '请输入用户名称'"
            style="width: 200px"
            clearable
            @select="handleSelectDuty"
          ></el-autocomplete>
        </el-form-item>
      </template>
    </el-form>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirm">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { editUniqueUser } from '@/api/garage'
import { getByDutyName } from '@/api/system'

export default {
  props: ['recordIds'],
  data() {
    return {
      visible: false,
      relations: [
        {
          label: '用户',
          disabled: false
        },
        {
          label: '商家',
          disabled: true
        },
        {
          label: '厂家',
          disabled: true
        },
        {
          label: '平台',
          disabled: true
        }
      ],
      ruleForm: {
        relateType: 1,
        feedbackSource: 2,
        dutyId: '',
        dutyName: '',
        relateName: '',
        bizType: 5,
        feedbackType: 12,
        relateId: '',
        urgent: false,
        processStat: '',
        workOrderDesc: '',
        riskTip: '',
        limitContact: '',
        limitPm: '',
        limitPublish: ''
      },
      userData: {},
      rules: {
        processStat: [
          { required: true, message: '请选择处理方式', trigger: 'change' }
        ]
      },
      checkList: [],
      detailsData: {}
    }
  },
  methods: {
    changeCheckList(e) {
      this.ruleForm.riskTip = e.includes(1) ? 1 : 0
      this.ruleForm.limitContact = e.includes(2) ? 1 : 0
      this.ruleForm.limitPm = e.includes(3) ? 1 : 0
      this.ruleForm.limitPublish = e.includes(4) ? 1 : 0
      this.ruleForm.limitFlow = e.includes(5) ? 1 : 0
    },
    handleChange(val) {
      if (val === 1) {
        this.ruleForm.workOrderDesc = this.desc
      } else {
        this.ruleForm.workOrderDesc = ''
      }
      this.ruleForm.riskTip = ''
      this.ruleForm.limitContact = ''
      this.ruleForm.limitPm = ''
      this.ruleForm.limitPublish = ''
      this.checkList = []
    },
    init(data, details) {
      this.visible = true
      this.ruleForm.relateId = data.uid
      this.ruleForm.relateName = data.userName
      this.desc = details.sceneRule || ''
      this.detailsData = details
    },
    handleSelectDuty(item) {
      this.ruleForm.dutyId = item.uid
      this.ruleForm.dutyName = item.value
    },
    // 获取责任人
    querySearchDuty(queryString, cb) {
      const requestParams = {
        // page: 1,
        // limit: 100,
        name: queryString
      }
      getByDutyName(requestParams)
        .then((response) => {
          const data = response.data
          console.log(response, 'response')
          if (data.code === 0) {
            const userNameList = []
            const result = data.data
            result.map(function (value) {
              const newObj = {
                value: value.name,
                uid: value.uid
              }
              userNameList.push(newObj)
            })
            console.log('userNameList', userNameList)
            cb(userNameList)
          }
        })
        .catch(() => {})
    },
    handleClose() {
      this.visible = false
      this.$nextTick(() => {
        this.reset()
      })
    },
    reset() {
      this.ruleForm = {
        relateType: 1,
        feedbackSource: 2,
        dutyId: '',
        dutyName: '',
        relateName: '',
        bizType: 5,
        feedbackType: 12,
        relateId: '',
        urgent: false,
        processStat: '',
        workOrderDesc: '',
        riskTip: '',
        limitContact: '',
        limitPm: '',
        limitPublish: ''
      }
      this.$refs['ruleForm'].resetFields()
    },
    async confirm() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
          if (!this.checkList.length && this.ruleForm.processStat === 3) {
            return this.$message.error('请选择处理方式')
          }
          if (this.ruleForm.processStat === 1) {
            const relateObj = {
              relateType: this.ruleForm.relateType || '',
              relateId: this.ruleForm.relateId || '',
              relateName: this.ruleForm.relateName || ''
            }
            this.ruleForm['related'] = [JSON.stringify(relateObj)]
          }
          console.log({
            ...this.ruleForm,
            recordIds: this.recordIds
          })
          const res = await editUniqueUser({
            ...this.ruleForm,
            recordIds: this.recordIds
          })
          if (res.data.code === 0) {
            this.reset()
            this.visible = false
            $emit(this, 'success')
          }
        }
      })
    },
    goUser(uid) {
      this.visible = false
      this.$router.push({
        name: 'UserAccountCorrelation',
        query: {
          uid
        }
      })
    }
  },
  emits: ['success']
}
</script>

<style lang="scss">
.unique-user-dialog {
  .relation {
    display: flex;
    align-items: center;
  }
  .line {
    height: 1px;
    background-color: #dedede;
  }
  .flag-risk-tips {
    color: red;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
