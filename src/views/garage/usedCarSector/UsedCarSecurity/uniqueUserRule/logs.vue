<template>
  <el-dialog
    center
    v-model="dialogVisible"
    :title="`${isEdit ? '编辑' : '查看'}场景`"
    width="700px"
    append-to-body
    :close-on-click-modal="!isEdit"
    :close-on-press-escape="!isEdit"
  >
    <!-- <div class="punish-top">
      <span>经销商名称：将就将就军车行</span>
      <span style="margin-left: 20px">经销商ID：12312314</span>
      <div class="phone">
        <span>联系电话：</span>
        <el-button size="small" style="margin-left: 10px" @click="seeMobile()"
          >查看</el-button
        >
      </div>
    </div> -->
    <el-form label-position="left" label-width="80px">
      <el-form-item label="场景ID"> {{ data.id }} </el-form-item>
      <el-form-item label="场景名称">
        <el-input
          v-if="isEdit"
          v-model="data.sceneName"
          :autosize="{ minRows: 1 }"
          type="textarea"
          resize="none"
          :disabled="!isEdit"
        ></el-input>
        <template v-else> {{ data.sceneName }} </template>
      </el-form-item>
      <el-form-item label="场景分类">
        <el-radio-group v-if="isEdit" v-model="data.sceneType">
          <el-radio
            v-for="(item, key) in sceneTypeEnum"
            :key="item"
            :label="item"
            >{{ key }}</el-radio
          >
        </el-radio-group>
        <template v-else> {{ data.sceneType }} </template>
      </el-form-item>
      <el-form-item label="场景规则">
        <el-input
          v-model="data.sceneRule"
          :autosize="{ minRows: 3 }"
          type="textarea"
          resize="none"
          :disabled="!isEdit"
        ></el-input
      ></el-form-item>
      <el-form-item label="重要场景">
        <el-switch
          v-if="isEdit"
          v-model="data.importantScene"
          :active-value="1"
          :inactive-value="0"
        >
        </el-switch>
        <span class="ml5"> {{ data.importantScene ? '是' : '否' }} </span>
      </el-form-item>
      <el-form-item label="人工审核">
        <el-switch
          v-if="isEdit"
          v-model="data.isCheck"
          :active-value="1"
          :inactive-value="0"
          @change="changeIsCheck"
        >
        </el-switch>
        <span class="ml5"> {{ data.isCheck ? '是' : '否' }} </span>
        <span v-if="!isEdit && !data.isCheck && strategyText">
          （{{ strategyText }}）
        </span>
      </el-form-item>
      <el-form-item v-if="isEdit && !data.isCheck" label="处理策略">
        <el-checkbox-group v-model="strategy">
          <el-checkbox
            v-for="(item, key) in strategyList"
            :key="key"
            :label="key"
            >{{ item }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item :label="isEdit ? '是否有效' : '有效性'">
        <template v-if="isEdit">
          <el-switch
            v-model="data.effect"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
          <span class="ml5"> {{ data.effect ? '是' : '否' }} </span>
        </template>
        <template v-else> {{ data.effect ? '有效' : '无效' }} </template>
      </el-form-item>
    </el-form>
    <div v-if="isEdit" class="wd-text-center">
      <el-button type="primary" class="wd-w-150px" @click="saveScene(data)">
        保存
      </el-button>
    </div>
    <template v-else>
      <div class="mb10" style="font-size: 14px; font-weight: bold">日志</div>
      <el-table
        v-loading="loading"
        align="center"
        :data="logs"
        height="400"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="remark" label="日志信息" align="center" />
        <el-table-column
          prop="userName"
          width="120"
          label="操作人"
          align="center"
        >
          <template v-slot="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="operateDate"
          label="操作时间"
          align="center"
          width="160px"
        >
        </el-table-column>
      </el-table>
      <el-pagination
        style="margin-top: 20px; justify-content: center"
        v-model:current-page="params.page"
        :total="total"
        background
        :page-size="params.limit"
        layout="total, prev, pager, next, jumper"
        @current-change="currentChange"
      />
    </template>
    <ChooseSeePhone ref="seePhone" />
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import { getUniqueLog, updateScene } from '@/api/garage'
import { paymentStatus, payCategory } from '@/utils/enum'
import { uidEnum } from '@/utils/enum/uid'
import { convertKeyValueEnum } from '@/utils/convert'
import ChooseSeePhone from '@/components/Dialog/ChooseSeePhone.vue'
import { $emit } from '@/utils/gogocodeTransfer'

export default {
  name: 'OperateLogs',
  components: {
    ChooseSeePhone
  },
  emits: ['success'],
  data() {
    return {
      data: {},
      dialogVisible: false,
      uidEnum,
      loading: false,
      logs: [],
      total: 0,
      convertPaymentStatus: convertKeyValueEnum(paymentStatus), // 订单状态
      convertPayCategory: convertKeyValueEnum(payCategory),
      refundDialog: false, // 退款弹框
      refundForm: {
        id: '', // id
        price: '' // 退款金额
      },
      params: {
        limit: 10,
        page: 1
      },
      isEdit: false,
      strategy: [],
      strategyList: {
        riskTip: '风险弹窗',
        limitContact: '禁止查看手机微信',
        limitPm: '限制私信',
        limitPublish: '禁止发布二手车',
        limitFlow: '二手车限流'
      },
      sceneTypeEnum: {
        私信异常: 1,
        行为异常: 2,
        发布异常: 3,
        身份异常: 4
      }
    }
  },
  computed: {
    ...mapGetters(['uid']),
    strategyText() {
      const arr = []
      this.strategy.forEach((key) => {
        if (this.strategyList[key]) {
          arr.push(this.strategyList[key])
        }
      })
      return arr.join('、')
    }
  },
  methods: {
    formatBusinessType_filter(type) {
      const enums = {
        1: '询价客户',
        2: '试驾客户',
        3: '电话客户',
        4: '直通车',
        5: '一口价',
        6: '二手车',
        7: '店铺活动',
        8: '驾校报名',
        9: '租车业务',
        10: '维修',
        11: '救援',
        12: '加油站',
        13: '在售车型',
        14: '微信客户',
        15: '广告营销'
      }
      return enums[type]
    },
    currentChange(page) {
      this.params.page = page
      this.getLogs()
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({ mobile: mobile, source: 'decode-mobile' })
    },
    // 外部调用 初始化
    setStatus(data, flag) {
      const me = this
      this.isEdit = !!flag
      this.data = JSON.parse(JSON.stringify(data))
      this.data = {
        ...this.data,
        sceneType: this.isEdit
          ? this.sceneTypeEnum[this.data.sceneType] || ''
          : this.data.sceneType,
        importantScene: this.data.importantScene ? 1 : 0,
        isCheck: this.data.isCheck ? 1 : 0,
        effect: this.data.effect ? 1 : 0
      }
      this.strategy = []
      Object.keys(this.strategyList).forEach((key) => {
        if (this.data[key]) {
          this.strategy.push(key)
        }
      })
      me.dialogVisible = true
      if (!this.isEdit) {
        me.ready()
      }
    },
    ready() {
      this.total = 0
      this.logs = []
      this.getLogs(1)
    },
    // 获取记录
    getLogs(page) {
      this.params.page = page || this.params.page
      const me = this
      this.loading = true
      getUniqueLog({ id: this.data.id, ...this.params })
        .then((response) => {
          if (response.data.code === 0) {
            me.logs = response.data.data.listData
            this.total = response.data.data.total
          }
        })
        .catch((err) => {
          me.$message.error(`${err.data.message}` || '获取失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 确认升级组件传来信息
    closeView(paySuccess) {
      const me = this
      setTimeout(() => {
        me.ready()
      }, 1000)
    },
    changeIsCheck() {
      this.strategy = []
    },
    saveScene(data) {
      if (!data.id) return
      if (!data.sceneName) {
        return this.$message.error('请输入场景名称')
      }
      if (!data.sceneType) {
        return this.$message.error('请选择场景分类')
      }
      if (!data.sceneRule) {
        return this.$message.error('请输入场景规则')
      }
      if (!data.isCheck && !this.strategy.length) {
        return this.$message.error('请选择处理策略')
      }
      let parameter = {
        sceneId: data.id,
        sceneName: data.sceneName,
        sceneType: data.sceneType,
        sceneRule: data.sceneRule,
        importantScene: data.importantScene,
        isCheck: data.isCheck,
        effect: data.effect
      }
      Object.keys(this.strategyList).forEach((key) => {
        parameter[key] = this.strategy.includes(key) ? 1 : 0
      })
      updateScene(parameter).then((res) => {
        if (res.data.code === 0) {
          this.$message.success('保存成功')
          $emit(this, 'success')
          this.dialogVisible = false
        } else {
          this.$message.error(res.data.msg || '保存失败')
        }
      })
    }
  }
}
</script>
