<template>
  <div class="risk-user-manage">
    <div class="operating-button">
      <el-form :inline="true" :model="form">
        <el-form-item label="用户ID" v-if="!showPhone">
          <el-input v-model="form.uid" placeholder="请输入用户ID"></el-input>
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input
            v-model="form.mobile"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
        <el-form-item label="关联用户ID" v-if="showPhone">
          <el-input
            v-model="form.userId"
            placeholder="请输入关联用户ID"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button class="mr10" @click="downloadExcel">下载EXCEL</el-button>
        <UploadExcelNew
          :buttonName="'批量导入'"
          :range="1"
          :loading="exportState"
          @success="uploadSuccess"
        />
        <el-button class="ml10" type="primary" @click="newCreate"
          >新增</el-button
        >
      </div>
    </div>
    <div class="table-box">
      <el-table :data="tableData" height="75vh" border>
        <el-table-column
          prop="username"
          label="用户"
          width="150"
          align="center"
          v-if="!showPhone"
        >
          <template v-slot="scope">
            <div>{{ scope.row.username }}</div>
            <div>{{ scope.row.uid }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="mobile"
          label="手机号码"
          align="center"
          width="160"
        >
          <template v-slot="scope">
            <div>{{ scope.row.mobile }}</div>
            <el-button
              type="success"
              size="small"
              v-if="scope.row.mobile"
              @click="seeData(scope.row.mobile)"
              >查看号码</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="最近收录原因" align="center">
        </el-table-column>
        <el-table-column
          prop="markRiskTime"
          label="最近收录时间"
          width="180"
          align="center"
        >
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.markRiskTime)
          }}</template>
        </el-table-column>
        <el-table-column label="风险弹窗" width="100" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.riskTip"
              @change="changeRiskTip(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="禁止查看手机微信" width="150" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.limitContact"
              @change="changeLimitContact(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="限制私信" width="100" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.limitPm"
              @change="changeLimitPm(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="限制二手车发布" width="130" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.limitPublish"
              @change="changeLimitUsedCar(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="二手车限流" width="130" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.limitFlow"
              @change="changeLimitFlow(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template v-slot="scope">
            <el-button type="primary" link @click="openLog(scope.row)"
              >日志</el-button
            >
            <el-button
              v-if="showPhone"
              type="primary"
              link
              @click="showUserList(scope.row)"
              >关联用户</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        style="text-align: center; margin-top: 10px"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
    <el-dialog title="日志信息" v-model="showLog" center width="45%">
      <el-table :data="logData" border>
        <el-table-column
          prop="remark"
          label="日志"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="operateDate"
          label="操作时间"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="userName"
          label="操作人"
          width="120"
          align="center"
        ></el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="logPage"
        :page-size="5"
        :total="logTotal"
        layout="total, prev, pager, next"
        style="text-align: center; margin-top: 10px"
        @current-change="currentChangeLog"
      >
      </el-pagination>
    </el-dialog>
    <el-dialog title="关联用户" v-model="showUserLog" center width="45%">
      <el-table :data="logUserData" border>
        <el-table-column prop="uid" label="用户ID" width="150" align="center" />
        <el-table-column prop="username" label="用户名称" align="center" />
        <el-table-column
          prop="markRiskTime"
          label="关联时间"
          width="180"
          align="center"
        >
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.markRiskTime)
          }}</template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog title="新增风险用户" v-model="showAppend" center width="600px">
      <el-form :model="appendForm" label-width="80px" class="append-form">
        <el-form-item label="风险用户" required v-if="!showPhone">
          <el-radio-group v-model="idOrName">
            <el-radio :label="0">用户ID</el-radio>
            <el-radio :label="1">用户名</el-radio>
          </el-radio-group>
          <div v-if="!idOrName">
            <el-input
              :placeholder="
                appendForm.name && appendForm.uid ? '' : '请输入用户ID'
              "
              v-model="userIdOrName"
              clearable
              style="width: 300px"
            >
              <template v-slot:prefix>
                <el-tag
                  v-if="appendForm.uid && appendForm.name"
                  type="info"
                  size="small"
                  closable
                  @close="closeTag"
                  >{{ appendForm.name }} {{ appendForm.uid }}</el-tag
                >
              </template>
            </el-input>
            <el-button type="primary" @click="searchIdOrName(userIdOrName)"
              >查询</el-button
            >
          </div>
          <div v-else>
            <el-autocomplete
              v-model="userIdOrName"
              :fetch-suggestions="searchIdOrName"
              :placeholder="
                appendForm.name && appendForm.uid ? '' : '请输入用户名'
              "
              :trigger-on-focus="false"
              clearable
              style="width: 300px"
              @select="handleSelect"
            >
              <template v-slot:prefix>
                <el-tag
                  v-if="appendForm.uid && appendForm.name"
                  type="info"
                  size="small"
                  closable
                  @close="closeTag"
                  >{{ appendForm.name }} {{ appendForm.uid }}</el-tag
                >
              </template>
            </el-autocomplete>
          </div>
        </el-form-item>
        <el-form-item label="风险手机号" label-width="100px" required v-else>
          <div>
            <el-input
              placeholder="请输入手机号"
              v-model="appendForm.mobile"
              clearable
              style="width: 300px"
            />
          </div>
        </el-form-item>
        <el-form-item label="处理方式" required>
          <el-checkbox-group v-model="appendForm.checkList">
            <el-checkbox :label="1">风险弹窗</el-checkbox>
            <el-checkbox :label="2">禁止查看手机微信</el-checkbox>
            <el-checkbox :label="3">限制私信</el-checkbox>
            <el-checkbox :label="4">限制二手车发布</el-checkbox>
            <el-checkbox :label="5">二手车限流</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="收录原因" required>
          <el-radio-group
            v-model="appendForm.reason"
            @change="customReason = ''"
          >
            <el-radio
              style="line-height: 36px"
              v-for="(item, index) in rulesList"
              :key="index"
              :label="item.sceneName"
            >
              {{ item.sceneName }}
              <el-popover
                popper-class="rules-popover"
                placement="bottom-end"
                :visible-arrow="false"
                trigger="click"
                :content="item.sceneRule"
              >
                <template v-slot:reference>
                  <el-icon @click.prevent="rulesPopover"
                    ><IconQuestion
                  /></el-icon>
                </template>
              </el-popover>
            </el-radio>
            <el-radio label="自定义"></el-radio>
          </el-radio-group>
          <el-input
            v-if="appendForm.reason === '自定义'"
            type="textarea"
            v-model="customReason"
            :autosize="{ minRows: 1 }"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            class="mt8"
            type="textarea"
            v-model="appendForm.remark"
            :autosize="{ minRows: 3 }"
          ></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button type="primary" @click="newRiskUsers"> 添加 </el-button>
      </div>
    </el-dialog>
    <ChooseCommonDecrypt ref="ChooseSeePhone" />
  </div>
</template>

<script>
import { QuestionFilled as IconQuestion } from '@element-plus/icons-vue'
import { getUserAccountCorrelationV2 } from '@/api/user'
import {
  getRiskManagerLog,
  getRiskManagerList,
  riskManagerAdd,
  riskManagerOpenRiskTip,
  riskManagerOpenLimitContact,
  riskManagerOpenLimitPm,
  riskManagerOpenRiskTipMoblie,
  riskManagerOpenLimitContactMobile,
  riskManagerOpenLimitPmByMobile,
  riskManagerOpenLimitUsedCar,
  riskManagerOpenLimitUsedCarMobile,
  riskManagerBatchAdd,
  riskManagerOpenFlow
} from '@/api/usedCarPublish'
import { getUniqueRuleList } from '@/api/garage'
import UploadExcelNew from '@/components/UploadExcel/new.vue'
import ChooseCommonDecrypt from '@/components/Dialog/ChooseCommonDecrypt.vue'
export default {
  components: {
    UploadExcelNew,
    IconQuestion,
    ChooseCommonDecrypt
  },
  props: {
    showPhone: {
      // 展示手机号维度
      type: Boolean,
      default: false
    }
  },
  name: 'UsedCarRiskUserManage',
  data() {
    return {
      form: {
        uid: '',
        mobile: ''
      },
      page: 1,
      limit: 10,
      total: 0,
      tableData: [],
      showLog: false,
      currentRow: {},
      logPage: 1,
      logTotal: 0,
      logData: [],
      showAppend: false,
      idOrName: 0,
      appendForm: {
        uid: '',
        name: '',
        checkList: [],
        reason: '',
        remark: ''
      },
      userIdOrName: '',
      rulesList: [],
      customReason: '',
      exportState: false,
      showUserLog: false,
      logUserData: []
    }
  },
  activated() {
    this.search()
  },
  methods: {
    resetForm() {
      this.form = {
        uid: '',
        mobile: '',
        userId: ''
      }
      this.page = 1
      this.limit = 10
      this.total = 0
      this.tableData = []
      this.getList()
    },
    search() {
      this.currentChange(1)
    },
    sizeChange(limit) {
      this.limit = limit
      this.currentChange(1)
    },
    currentChange(page) {
      this.page = page
      this.getList()
    },
    getList() {
      getRiskManagerList({
        ...this.form,
        page: this.page,
        limit: this.limit,
        uid: this.showPhone ? this.form.userId : this.form.uid,
        optType: this.showPhone ? 2 : 1
      }).then((res) => {
        if (res.data.code === 0) {
          this.tableData = res.data.data.listData || []
          this.tableData.forEach((_) => {
            _.riskTip = !!_.riskTip
            _.limitContact = !!_.limitContact
            _.limitPm = !!_.limitPm
            _.limitPublish = !!_.limitPublish
            _.limitFlow = !!_.limitFlow
          })
          this.total = res.data.data.total || 0
        }
      })
    },
    openLog(data) {
      this.logPage = 1
      this.logTotal = 0
      this.logData = []
      this.currentRow = data
      this.showLog = true
      this.getLog()
    },
    currentChangeLog(page) {
      this.logPage = page
      this.getLog()
    },
    getLog() {
      getRiskManagerLog({
        uid: this.currentRow.uid,
        page: this.logPage,
        limit: 5,
        mobile: this.showPhone ? this.currentRow.mobile : ''
      }).then((res) => {
        if (res.data.code === 0) {
          this.logTotal = res.data.data.total || []
          this.logData = res.data.data.listData || 0
        }
      })
    },
    newCreate() {
      this.idOrName = 0
      this.appendForm = {
        uid: '',
        name: '',
        checkList: [],
        reason: '',
        mobile: '',
        remark: ''
      }
      this.userIdOrName = ''
      this.getRulesList()
      this.showAppend = true
    },
    newRiskUsers() {
      if (!this.appendForm.uid && !this.appendForm.mobile) {
        const tip = this.showPhone ? '请输入手机号码' : '请选择风险用户'
        return this.$message.error(tip)
      }
      if (!this.appendForm.checkList.length) {
        return this.$message.error('请选择处理方式')
      }
      if (!this.appendForm.reason) {
        return this.$message.error('请选择收录原因')
      }
      if (this.appendForm.reason === '自定义' && !this.customReason) {
        return this.$message.error('请填写收录原因')
      }
      riskManagerAdd({
        uid: this.appendForm.uid,
        optType: this.showPhone ? 2 : 1,
        mobile: this.showPhone ? this.appendForm.mobile : '',
        riskTip: this.appendForm.checkList.includes(1) ? 1 : 0,
        limitContact: this.appendForm.checkList.includes(2) ? 1 : 0,
        limitPm: this.appendForm.checkList.includes(3) ? 1 : 0,
        limitPublish: this.appendForm.checkList.includes(4) ? 1 : 0,
        limitFlow: this.appendForm.checkList.includes(5) ? 1 : 0,
        reason:
          this.appendForm.reason === '自定义'
            ? this.customReason
            : this.appendForm.reason,
        remark: this.appendForm.remark
      }).then((res) => {
        if (res.data.code === 0) {
          this.$message.success('操作成功')
          this.showAppend = false
          this.search()
        } else {
          this.$message.error(res.data.msg || '操作失败')
        }
      })
    },
    searchIdOrName(query, cb) {
      if (!query) return
      let parameter = {
        page: 1,
        limit: 100
      }
      if (!this.idOrName) {
        parameter.uid = query
      } else {
        parameter.username = query
      }
      getUserAccountCorrelationV2(parameter).then((res) => {
        if (res.status === 200 || res.data.code === 0) {
          const data = res.data.code === 0 ? res.data.data : res.data
          if (!this.idOrName) {
            if (data.list && data.list.length) {
              this.appendForm.uid = data.list[0].uid || ''
              this.appendForm.name = data.list[0].username || ''
            } else {
              return this.$message.error('用户不存在')
            }
            this.userIdOrName = ''
          } else {
            const userInfoList =
              data.list &&
              data.list.map((_) => {
                return {
                  value: _.username,
                  uid: _.uid
                }
              })
            cb(userInfoList || [])
          }
        }
      })
    },
    closeTag() {
      this.appendForm.uid = ''
      this.appendForm.name = ''
    },
    handleSelect(data) {
      this.appendForm.uid = data.uid || ''
      this.appendForm.name = data.value || ''
      this.userIdOrName = ''
    },
    changeRiskTip(data) {
      const url = this.showPhone
        ? riskManagerOpenRiskTipMoblie
        : riskManagerOpenRiskTip
      url({
        open: data.riskTip,
        uid: this.showPhone ? '' : data.uid,
        mobile: this.showPhone ? data.mobile : ''
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
          this.getList()
        })
        .catch(() => {
          this.getList()
        })
    },
    changeLimitContact(data) {
      const url = this.showPhone
        ? riskManagerOpenLimitContactMobile
        : riskManagerOpenLimitContact
      url({
        open: data.limitContact,
        uid: this.showPhone ? '' : data.uid,
        mobile: this.showPhone ? data.mobile : ''
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
          this.getList()
        })
        .catch(() => {
          this.getList()
        })
    },
    changeLimitPm(data) {
      const url = this.showPhone
        ? riskManagerOpenLimitPmByMobile
        : riskManagerOpenLimitPm
      url({
        open: data.limitPm,
        uid: this.showPhone ? '' : data.uid,
        mobile: this.showPhone ? data.mobile : ''
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
          this.getList()
        })
        .catch(() => {
          this.getList()
        })
    },
    changeLimitUsedCar(data) {
      const url = this.showPhone
        ? riskManagerOpenLimitUsedCarMobile
        : riskManagerOpenLimitUsedCar
      url({
        open: data.limitPublish,
        uid: this.showPhone ? '' : data.uid,
        mobile: this.showPhone ? data.mobile : ''
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
          this.getList()
        })
        .catch(() => {
          this.getList()
        })
    },
    changeLimitFlow(data) {
      riskManagerOpenFlow({
        open: data.limitFlow,
        uid: data.uid
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
          this.getList()
        })
        .catch(() => {
          this.getList()
        })
    },
    // 规则列表
    getRulesList(cb) {
      getUniqueRuleList({
        page: 1,
        limit: 100
      }).then((res) => {
        if (res.data.code === 0) {
          this.rulesList = res.data.data.listData || []
          let obj = {}
          this.rulesList.map((_) => {
            obj[_.id] = _.sceneName
          })
          cb && cb(obj)
        }
      })
    },
    rulesPopover() {
      // 触发 Popover
    },
    async downloadExcel() {
      this.getRulesList(cb)
      async function cb(obj) {
        let reason = '收录原因：0-自定义'
        Object.keys(obj).forEach((key) => {
          const value = `；${key}-${obj[key]}`
          reason = reason + value
        })
        if (reason.length > 90) {
          const list = reason.match(/[\s\S]{1,90}/g) || []
          reason = list.join('\n')
        }
        const title =
          reason +
          '\n备注：\n1、选择自定义时，收录原因文案需填写\n2、风险弹窗、禁止查看手机微信、限制私信，需开启的限制在表格内填入1'
        const tHeader = [
          '用户ID',
          '手机号码',
          '收录原因枚举',
          '收录原因文案',
          '风险弹窗',
          '禁止查看手机微信',
          '限制私信',
          '限制二手车发布',
          '二手车限流',
          '备注'
        ]
        const { export_json_to_excel_new } = await import(
          '@/vendor/Export2Excel'
        )
        export_json_to_excel_new({
          title,
          header: tHeader,
          data: [],
          filename: `二手车风险用户管理`
        })
      }
    },
    uploadSuccess(data) {
      const me = this
      this.getRulesList(cb)
      function cb(obj) {
        me.exportState = true
        let flag = false
        const fun = (s) => {
          return s && s !== '0' ? 1 : 0
        }
        const dataList =
          data &&
          data.map((_) => {
            let reasonType = _['收录原因枚举']
            try {
              reasonType = reasonType.toString()
            } catch (e) {
              flag = true
            }
            if ((!_['用户ID'] && !_['手机号码']) || !reasonType) {
              flag = true
            }
            if (reasonType && reasonType === '0' && !_['收录原因文案']) {
              flag = true
            }
            if (reasonType && reasonType !== '0' && !obj[reasonType]) {
              flag = true
            }
            const reason =
              reasonType === '0' ? _['收录原因文案'] : obj[reasonType]
            return {
              uid: _['用户ID'] || '',
              mobile: _['手机号码'] || '',
              optType: me.showPhone ? 2 : 1,
              riskTip: fun(_['风险弹窗']),
              limitContact: fun(_['禁止查看手机微信']),
              limitPm: fun(_['限制私信']),
              limitPublish: fun(_['限制二手车发布']),
              limitFlow: fun(_['二手车限流']),
              reason: reason || '',
              remark: _['备注'] || ''
            }
          })

        if (flag) {
          me.$message.error('有必填项为空，请补全后再上传')
          me.exportState = false
        } else {
          if (dataList && dataList.length) {
            me.bulkImport(dataList)
          } else {
            me.$message.error('获取文件失败')
            me.exportState = false
          }
        }
      }
    },
    // 批量导入
    bulkImport(data) {
      riskManagerBatchAdd({
        userRiskManager: JSON.stringify(data)
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success(res.data.msg || '导入成功')
            this.search()
          }
        })
        .finally(() => {
          this.exportState = false
        })
    },
    showUserList(item) {
      getRiskManagerList({
        page: 1,
        limit: 1000,
        mobile: item.mobile,
        optType: 1
      }).then((res) => {
        if (res.data.code === 0) {
          this.logUserData = res.data.data.listData || []
          this.showUserLog = true
        }
      })
    },
    seeData(value) {
      this.$refs.ChooseSeePhone &&
        this.$refs.ChooseSeePhone.init({
          decryptContent: value,
          source: 'usedCarRiskUserManage',
          type: 1,
          operateType: 29
        })
    }
  }
}
</script>

<style lang="scss">
.rules-popover {
  background-color: #303133;
  border-color: #303133;
  color: #ffffff;
  padding: 10px 12px;
  max-width: 200px;
  &[x-placement^='bottom'] {
    margin-top: 5px;
  }
}
</style>

<style lang="scss" scoped>
.risk-user-manage {
  padding: 20px;
  .operating-button {
    display: flex;
    justify-content: space-between;
  }
  .table-box {
    width: 100%;
  }
}
.append-form {
  .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
