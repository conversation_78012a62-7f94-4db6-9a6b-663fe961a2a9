<template>
  <div class="reportManagement">
    <div class="header">
      <el-button type="primary" @click="search">刷新</el-button>
    </div>
    <el-row :gutter="24">
      <el-col :span="10" style="border-right: 1px solid #ddd">
        <div class="follow-up">
          待处理举报 <span class="not-pass">{{ total }}</span> 条
        </div>
        <el-table
          :data="tableData"
          border
          :row-class-name="tableRowClassName"
          height="64vh"
          @row-click="chooseRow"
        >
          <el-table-column
            prop="reportedUsername"
            label="被举报人"
            align="center"
          >
            <template v-slot="scope">
              <div>{{ scope.row.reportedUsername }}</div>
              <div>{{ scope.row.reportedUid }}</div>
              <div>
                <el-tag v-if="scope.row.shopType === 1" size="small"
                  >认证商家</el-tag
                >
                <el-tag v-if="scope.row.shopType === 0" size="small"
                  >关联商家</el-tag
                >
                <el-tag v-if="scope.row.riskWarning" size="small" type="danger"
                  >高风险</el-tag
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="relatedId" label="被举报车源" align="center">
            <template v-slot="scope">
              <div>{{ scope.row.title }}</div>
              <div>{{ scope.row.relatedId || '' }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createdate"
            label="最近举报时间"
            align="center"
          >
            <template v-slot="scope">{{
              $filters.timeFullS(scope.row.createdate)
            }}</template>
          </el-table-column>
          <el-table-column label="标记" align="center" width="90px">
            <template v-slot="scope">
              <el-tag
                v-if="scope.row.cheat"
                type="danger"
                effect="plain"
                class="plain-tag"
              >
                诈
              </el-tag>
              <el-tag
                v-if="scope.row.checkFlag"
                type="warning"
                effect="plain"
                class="plain-tag"
              >
                巡
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="total"
          :page-size="limit"
          :page-sizes="[10, 20, 40, 60]"
          v-model:current-page="page"
          :total="total"
          align="center"
          layout="total, sizes, prev, pager, next, jumper"
          class="el-pagination-center"
          @size-change="handleSizeChange"
          @current-change="currentChange"
        />
      </el-col>
      <el-col :span="14" v-if="JSON.stringify(leftData) !== '{}'">
        <div class="untreated">
          <div class="untreated-left">
            <div>
              <div>
                未处理 <span class="not-pass">{{ leftData.count || 0 }}</span> |
                累计举报 {{ detailTotal }}
              </div>
            </div>
            <div class="untreated-left-right" v-if="leftData.relatedId">
              <div class="show-detail" @click="showH5Preview(leftData)">
                {{ leftData.title }}
              </div>
              <div>
                <span>{{ leftData.relatedId }}</span>
                <span> | </span>
                <!-- <span :class="classList[leftData.relatedStatus]">{{ ststusList[leftData.relatedStatus] }}</span> -->
                <span
                  v-if="
                    vehicleReportingInfo.scene !== 9 &&
                    vehicleReportingInfo.scene !== null
                  "
                  :class="sceneClassList[vehicleReportingInfo.scene]"
                  >{{ sceneList[vehicleReportingInfo.scene] }}</span
                >
                <span v-else :class="classList[vehicleReportingInfo.status]">{{
                  ststusList[vehicleReportingInfo.status]
                }}</span>
                <span> | </span>
                <span
                  :class="
                    vehicleReportingInfo.ifSell === 1
                      ? 'classIfSell'
                      : 'classNoIfSell'
                  "
                >
                  {{ vehicleReportingInfo.ifSell === 1 ? '已售出' : '未售出' }}
                </span>
                <span> | </span>
                <span>{{
                  leftData.shopType === 1 ? '经销商发布' : '个人发布'
                }}</span>
              </div>
            </div>
          </div>
          <div>
            <el-button @click="toHistory">处理记录</el-button>
            <el-button type="primary" @click="toProcess(leftData)"
              >处理</el-button
            >
          </div>
        </div>
        <el-table :data="detailData" border highlight-current-row height="64vh">
          <el-table-column
            prop="auther"
            label="举报人"
            align="center"
            width="150px"
          >
            <template v-slot="scope">
              <div>{{ scope.row.auther }}</div>
              <div>{{ scope.row.autherId }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="累计举报次数历史90天/总量"
            align="center"
            width="115px"
          >
            <template v-slot="scope">
              <div>
                {{ scope.row.reportCount90Days || 0 }}/{{
                  scope.row.reportCount || 0
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createDate"
            label="举报类型"
            align="center"
            width="130px"
          >
            <template v-slot="scope">{{
              scope.row.reportDetail
                ? reportTypeList[scope.row.reportDetail.reportType]
                : '其他'
            }}</template>
          </el-table-column>
          <el-table-column label="事件描述" align="center" width="400px">
            <template v-slot="scope">
              <div v-if="scope.row.reportDetail">
                <div v-if="scope.row.reportDetail.notCompliant !== 8">
                  {{
                    violationTypeList[scope.row.reportDetail.reportType][
                      scope.row.reportDetail.notCompliant
                    ]
                  }}
                </div>
                <div v-if="scope.row.reportDetail.notCompliant === 8">
                  {{ scope.row.reportDetail.notCompliantDesc }}
                </div>
                <div>{{ scope.row.reportDetail.eventDesc }}</div>
                <div class="img-box">
                  <img
                    v-for="(img, index) in scope.row.reportDetail.imgs.slice(
                      0,
                      5
                    )"
                    :key="index"
                    :src="img"
                    alt=""
                    class="img"
                    @click="seeBigImg(img)"
                  />
                  <span
                    class="beyond"
                    v-if="scope.row.reportDetail.imgs.length - 5 > 0"
                    @click="getReportDetails(scope.row)"
                    >+{{ scope.row.reportDetail.imgs.length - 5 }}</span
                  >
                </div>
              </div>
              <div v-else>
                <p
                  v-show="
                    scope.row.reportReasonType === 6 ||
                    scope.row.reportReasonType === null
                  "
                >
                  {{ scope.row.text }}
                </p>
                <img
                  v-for="(img, index) in scope.row.imgs"
                  :key="index"
                  :src="img.replace('!forum', '!forum300')"
                  alt=""
                  class="img"
                  @click="seeBigImg(img)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createDate"
            label="举报时间"
            align="center"
            width="180px"
          >
            <template v-slot="scope">{{
              $filters.timeFullS(scope.row.createDate)
            }}</template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="处理状态"
            align="center"
            width="100px"
            fixed="right"
          >
            <template v-slot="scope">
              <div :class="[scope.row.status ? 'not-launched' : 'not-pass']">
                {{ scope.row.status ? '已处理' : '未处理' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="100px"
            fixed="right"
          >
            <template v-slot="scope">
              <el-button type="primary" link @click="creatWorkOrder(scope.row)"
                >创建工单</el-button
              >
              <el-button
                type="primary"
                link
                @click="viewingExceptions(scope.row)"
                >私信对话查询</el-button
              >
              <el-button
                v-if="scope.row.reportDetail"
                type="primary"
                link
                @click="getReportDetails(scope.row)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="detailTotal"
          :page-size="detailLimit"
          :page-sizes="[10, 20, 40, 60]"
          v-model:current-page="detailPage"
          :total="detailTotal"
          align="center"
          layout="total, sizes, prev, pager, next, jumper"
          class="el-pagination-center"
          @size-change="handleSizeChangeDetail"
          @current-change="currentChangeDetail"
        />
      </el-col>
    </el-row>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="二手车详情"
      center
      class="choose-handle"
    >
      <car-detail ref="carDetail" />
    </el-dialog>
    <ReportDetails ref="reportDetails" />
    <el-dialog title="处理记录" v-model="showHistory" width="70%" center>
      <el-table :data="historyList" border highlight-current-row>
        <el-table-column prop="reportTypeDesc" label="举报类型" align="center">
          <template v-slot="scope">
            <div
              v-for="(item, index) in scope.row.reportTitle.split('/n')"
              :key="index"
            >
              {{ item }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="businessId"
          label="车源ID"
          align="center"
          width="120"
        />
        <el-table-column
          prop="sourceId"
          label="用户ID"
          align="center"
          width="145"
        />
        <el-table-column prop="handleInfo" label="处理结果" align="center" />
        <el-table-column
          prop="createTime"
          label="处理时间"
          align="center"
          width="165"
        >
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.createTime)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="operator"
          label="处理人"
          align="center"
          width="120"
        />
        <el-table-column prop="remark" label="备注" align="center" />
      </el-table>
      <el-pagination
        v-if="logTotal"
        :page-size="logLimit"
        v-model:current-page="logPage"
        :total="logTotal"
        align="center"
        layout="total, prev, pager, next, jumper"
        class="el-pagination-center"
        @current-change="currentChangeLog"
      />
    </el-dialog>
    <choose-show-image ref="showImage" />
    <PublishDialog
      ref="publishDialog"
      :from="0"
      v-model:punishInfo="punishInfo"
      v-model:visible="visiblePublishDialog"
      @updateSuccess="updateSuccess"
    />

    <CreateWorkOrder
      ref="createWorkOrder"
      source="feedback"
      @success="success"
    ></CreateWorkOrder>
  </div>
</template>

<script>
import CarDetail from '../components/carDetail.vue'
import {
  usedCarWaitingList,
  usedCarWaitingLog,
  getReportDetail
} from '@/api/user'
import { getNewCarDetail } from '@/api/garage'
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import { violationTypeList, reportTypeList } from '@/utils/enum'
import PublishDialog from '@/components/ReportManagement/publishDialog.vue'
import ReportDetails from '../components/reportDetails.vue'
import CreateWorkOrder from '@/components/CreateWorkOrder/create-work-order.vue'
export default {
  name: 'ReportManagement',
  components: {
    CarDetail,
    ChooseShowImage,
    PublishDialog,
    ReportDetails,
    CreateWorkOrder
  },
  data() {
    return {
      sceneList: {
        0: '删除',
        1: '申诉中',
        2: '审核中',
        3: '审核不通过',
        4: '已上架',
        5: '售出下架',
        6: '主动下架',
        7: '系统下架',
        8: '系统下架',
        9: '车辆冻结',
        10: '申诉待处理',
        11: '售出中'
      },
      sceneClassList: {
        0: 'be-audited',
        3: 'have-launched',
        4: 'not-launched',
        5: 'not-launched',
        9: 'freeze'
      },
      ststusList: {
        0: '系统下架',
        1: '已上架',
        2: '待审核',
        3: '审核不通过',
        4: '个人删除',
        5: '已售出',
        6: '用户下架',
        7: '系统下架',
        8: '车源过期',
        9: '已冻结',
        10: '系统下架-受限下架',
        11: '手动确认售出',
        12: '买家确认售出',
        13: '售出中'
      },
      classList: {
        0: 'be-audited',
        1: 'not-launched',
        2: 'not-pass',
        3: 'have-launched',
        4: 'be-audited',
        5: 'not-launched',
        9: 'freeze'
      },
      sourceList: {
        1: '经销商发布',
        2: '个人发布'
      },
      dialogVisible: false,
      tableData: [],
      detailData: [],
      page: 1,
      limit: 10,
      total: 0,
      detailPage: 1,
      detailLimit: 10,
      detailTotal: 0,
      violationTypeList,
      reportTypeList,
      visiblePublishDialog: false,
      punishInfo: {},
      leftData: {},
      showHistory: false,
      historyList: [],
      logPage: 1,
      logLimit: 10,
      logTotal: 0,
      currentIndex: 0,
      vehicleReportingInfo: {} // 车辆举报信息
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    search() {
      this.currentChange(1)
    },
    currentChange(page) {
      this.page = page
      this.leftData = {}
      this.currentIndex = 0
      this.getList()
    },
    handleSizeChange(limit) {
      this.limit = limit
      this.getList()
    },
    getList() {
      usedCarWaitingList({
        page: this.page,
        limit: this.limit
      }).then((res) => {
        if (res.data.code === 0) {
          this.tableData = res.data.data.list || []
          this.total = res.data.data.total
          this.showData(this.tableData)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    tableRowClassName({ row }) {
      if (row.reportId === this.leftData.reportId) {
        return 'selected-row'
      }
      return ''
    },
    showData(list) {
      const index = list.findIndex((_) => _.reportId === this.leftData.reportId)
      if (index === -1) {
        if (list.length) {
          this.chooseRow(
            list[this.currentIndex] || list[this.currentIndex - 1] || list[0]
          )
        } else {
          this.leftData = {}
          this.currentIndex = 0
        }
      }
    },
    chooseRow(detailData) {
      this.detailPage = 1
      this.leftData = detailData
      this.currentIndex = 0
      this.vehicleReportingInfo = {}
      this.getDetailList()
    },
    currentChangeDetail(page) {
      this.detailPage = page
      this.getDetailList()
    },
    handleSizeChangeDetail(limit) {
      this.detailLimit = limit
      this.getDetailList()
    },
    getDetailList() {
      this.detailData = []
      getReportDetail({
        packageType: this.leftData.packageType, // 平台
        originalId: this.leftData.relatedId,
        reportedUid: !this.leftData.relatedId ? this.leftData.reportedUid : '',
        auditDetail: 1,
        type: 'used_car',
        page: this.detailPage,
        limit: this.detailLimit
      }).then((response) => {
        if (response.data.code === 0) {
          this.parseContent(response.data.data.list || [])
          this.detailData = response.data.data.list || []
          this.detailTotal = response.data.data.total
        } else {
          this.$message.error(response.data.msg)
        }
      })
      if (this.leftData.relatedId) {
        getNewCarDetail({
          id: this.leftData.relatedId
        }).then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data || {}
            this.vehicleReportingInfo = data
          }
        })
      }
    },
    parseContent(list) {
      list.map((item) => {
        const contentJson =
          item.content.indexOf('[{') > -1 ? JSON.parse(item.content) : []
        item.imgs =
          item.content.indexOf('[{') > -1
            ? contentJson.filter((_) => _.img).map((_) => _.img)
            : []
        item.text =
          item.content.indexOf('[{') > -1
            ? contentJson
                .filter((_) => _.content)
                .map((_) => _.content)
                .join(',')
            : item.content
        item.text = item.text === '[]' ? '' : item.text
        if (item.reportDetail) {
          const reportImgs =
            (item.reportDetail.reportImgs &&
              item.reportDetail.reportImgs.split(',')) ||
            []
          const relatedImgs =
            (item.reportDetail.relatedImgs &&
              item.reportDetail.relatedImgs.split(',')) ||
            []
          item.reportDetail.imgs = [...reportImgs, ...relatedImgs]
        }
      })
    },
    showH5Preview(item) {
      const me = this
      me.dialogVisible = true
      setTimeout(() => {
        me.$refs.carDetail.init({ id: item.relatedId })
      }, 100)
    },
    handleClose() {
      this.dialogVisible = false
    },
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(link)
    },
    getReportDetails(data) {
      this.$refs.reportDetails.init(data)
    },
    toProcess(data) {
      this.punishInfo = data
      this.visiblePublishDialog = true
      this.$nextTick(() => {
        this.$refs['publishDialog'].init()
      })
    },
    toHistory() {
      this.logPage = 1
      this.historyList = []
      this.showHistory = true
      this.getLog()
    },
    getLog() {
      let sourceId = ''
      let isShop = 0
      if (JSON.stringify(this.vehicleReportingInfo) !== '{}') {
        sourceId =
          this.vehicleReportingInfo.source === 1
            ? this.vehicleReportingInfo.shopId
            : this.leftData.reportedUid
        isShop = this.vehicleReportingInfo.source === 1 ? 1 : 0
      } else {
        sourceId = this.leftData.reportedUid
      }
      usedCarWaitingLog({
        page: this.logPage,
        limit: this.logLimit,
        sourceId,
        isShop
      }).then((res) => {
        if (res.data.code === 0) {
          this.historyList = res.data.data.listData || []
          this.logTotal = res.data.data.total || 0
        }
      })
    },
    currentChangeLog(page) {
      this.logPage = page
      this.getLog()
    },
    updateSuccess() {
      this.currentIndex = this.tableData.findIndex(
        (_) => _.reportId === this.leftData.reportId
      )
      this.leftData = {}
      setTimeout(() => {
        this.getList()
      }, 1000)
    },
    viewingExceptions(data) {
      const toUid = this.leftData?.reportedUid || ''
      const uid = data.autherId || ''
      if (!uid) return
      this.$router.push({
        name: 'PrivateMessageQuery',
        query: {
          uid,
          toUid: toUid || '',
          isSelected: true
        }
      })
    },
    creatWorkOrder(data) {
      const imgList = []
      const reportDetail = data.reportDetail || {}
      if (reportDetail.relatedImgs) {
        reportDetail.relatedImgs.split(',').map((item) => {
          imgList.push(item)
        })
      }
      if (reportDetail.reportImgs) {
        reportDetail.reportImgs.split(',').map((item) => {
          imgList.push(item)
        })
      }
      const related = [
        {
          relateType: 1, //关联对象类型
          relateId: this.leftData.reportedUid, //关联对象id
          relateName: this.leftData.reportedUsername //关联对象名称
        }
      ]
      if (this.leftData.shopId) {
        related.unshift({
          relateType: 2, //关联对象类型
          relateId: this.leftData.shopId, //关联对象id
          relateName: this.leftData.shopName //关联对象名称
        })
      }
      this.$refs.createWorkOrder.init({
        feedbackSource: 1,
        feedbackId: data.autherId || '', //反馈用户ID
        feedbackName: data.auther || '', //反馈用户名
        workOrderDesc: reportDetail.eventDesc, //问题描述
        bizType: 5, //业务类型
        feedbackType: 7, //反馈类型
        datas: imgList,
        related: related
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.reportManagement {
  padding: 20px;
  .header {
    border-bottom: 1px solid #dedede;
    padding-bottom: 20px;
  }
  .follow-up {
    height: 80px;
    line-height: 80px;
    font-size: 18px;
  }
  :deep(.selected-row) {
    background-color: #ecf5ff;
  }
  .untreated {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .untreated-left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 20px;
      .untreated-left-right {
        padding-left: 20px;
        margin-left: 20px;
        border-left: 1px solid #dedede;
      }
    }
  }

  .not-launched {
    color: #999;
  }
  .have-launched {
    color: #fac774;
  }
  .not-pass {
    color: red;
  }
  .be-audited {
    color: #cc6600;
  }
  .freeze {
    color: #3b86f7;
  }
  .img-box {
    display: inline-flex;
    align-items: center;
  }
  .img {
    display: inline-block;
    width: 60px;
    height: 60px;
    object-fit: cover;
    margin-left: 5px;
  }
  .beyond {
    display: inline-block;
    padding: 0 10px;
    border-radius: 20px;
    background-color: #fff;
    border: 1px solid #dedede;
    margin-left: 10px;
    cursor: pointer;
  }
  .details-cotent {
    .main {
      display: flex;
      line-height: 16px;
      margin-bottom: 8px;
      .left {
        color: #c4c4c6;
        width: 90px;
      }
      .right {
        color: #666d7f;
        flex: 1;
      }
    }
    .prompt {
      color: #111e36;
      margin-top: 15px;
    }
    .voucher {
      display: flex;
      flex-wrap: wrap;
      .img-box {
        position: relative;
        margin: 10px 10px 0 0;
        border-radius: 5px;
        overflow: hidden;
        .image {
          width: 83px;
          height: 83px;
          object-fit: cover;
        }
      }
    }
  }
  .show-detail {
    color: #409eff;
    cursor: pointer;
  }
}
.classIfSell {
  color: #999;
}
.classNoIfSell {
  color: #00cc67;
}
.plain-tag {
  margin: 0 3px;
  padding: 0 5px;
}
</style>
