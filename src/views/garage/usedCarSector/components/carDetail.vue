<template>
  <div class="used-form">
    <el-form
      v-if="!isCarAudit"
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      :inline="true"
      label-width="100px"
    >
      <p
        class="modules-title"
        style="border-bottom: 1px solid #ccc; padding: 5px 0 5px 10px"
      >
        基本信息
        <span
          v-if="ruleForm.auditFailType === 21"
          style="border: 1px solid #ccc; padding: 4px 10px"
        >
          高风险用户
        </span>
      </p>
      <div class="modules">
        <el-form-item label="用户名：" label-width="80px">
          <span>{{ ruleForm.userName }}</span>
        </el-form-item>
        <el-form-item label="联系人：" label-width="80px">
          <span>{{ ruleForm.ownerName }}</span>
        </el-form-item>
        <el-form-item label="联系电话：" label-width="120px">
          <span>
            {{ ruleForm.mobile }}&ensp;&ensp;&ensp;&ensp;
            <el-button
              v-if="ruleForm.mobile"
              type="primary"
              @click="seeMobile(ruleForm.mobile)"
              >查看电话</el-button
            >
          </span>
        </el-form-item>
        <el-form-item label="状态：" label-width="120px">
          <span :class="classList[ruleForm.status]">{{
            ststusList[ruleForm.status]
          }}</span>
          <el-button
            v-if="ruleForm.status === 1 || ruleForm.status === 3"
            style="margin-left: 10px"
            @click="changeStatus(ruleForm.status)"
            >{{ ruleForm.status === 1 ? '审核不通过' : '审核通过' }}</el-button
          >
        </el-form-item>
        <el-form-item label="原因：" label-width="120px">
          <span>{{
            ruleForm.auditFailReason || reasonForFailure[ruleForm.status]
          }}</span>
        </el-form-item>
        <el-form-item label="是否售出：" label-width="120px">
          <span>{{ ruleForm.ifSell === 1 ? '已售出' : '未售出' }}</span>
        </el-form-item>
        <el-table
          ref="articleList"
          :data="dataList"
          row-key="articleList"
          border
          style="width: 100%"
        >
          <el-table-column prop="title" label="车辆名称" align="center" />
          <el-table-column prop="mileage" label="行驶里程" align="center" />
          <el-table-column
            prop="transferTimes"
            label="过户次数"
            align="center"
          />
          <el-table-column prop="address" label="所属地区" align="center" />
          <el-table-column prop="price" label="转出价格(元)" align="center" />
          <el-table-column
            prop="buyPrice"
            label="购入价格(元)"
            align="center"
          />
        </el-table>
        <p v-if="!isCarAudit">行驶证信息</p>
        <div class="info-message">
          <img
            :src="$filters.replaceImgUrl(ruleForm.drivingLicenseImage)"
            alt
            class="info-message-img"
            @click="seeBigImg(ruleForm, true)"
          />
          <p class="info-message-mes">
            <el-form-item label="首次上牌：" label-width="100px">
              <span>{{ $filters.timeFullS(ruleForm.placeTime) }}</span>
            </el-form-item>
          </p>
          <p class="info-message-mes">
            <el-form-item label="车牌号：" label-width="100px">
              <span>{{ ruleForm.licensePlateAddress }}</span>
            </el-form-item>
          </p>
          <p class="info-message-mes">
            <el-form-item label="车辆识别号：" label-width="100px">
              <span>{{ ruleForm.vehicleIdentificationNumber }}</span>
            </el-form-item>
          </p>
          <p class="info-message-mes">
            <el-form-item label="发动机号：" label-width="100px">
              <span>{{ ruleForm.engineNumber }}</span>
            </el-form-item>
          </p>
          <p class="info-message-mes">
            <el-form-item label="服务：" label-width="100px">
              <span
                v-for="(l, i) in provideService"
                :key="i"
                class="service-name"
                >{{ l }}&ensp;&ensp;</span
              >
            </el-form-item>
          </p>
        </div>
      </div>
    </el-form>
    <el-row v-else :gutter="20">
      <el-col :span="9">
        <div class="img-audit-box">
          <img
            :src="$filters.replaceImgUrl(ruleForm.drivingLicenseImage)"
            alt
            class="info-message-img-audit"
            id="img-audit"
            @click="seeBigImg(ruleForm, true)"
          />
        </div>
      </el-col>
      <el-col :span="15">
        <p v-if="showTimeStatus" class="bottom">
          更新时间：{{ $filters.timeFullS(ruleForm.editTime) }}
        </p>
        <p v-if="ruleForm.auditFailReason" class="not-pass bottom">
          上次审核不通过原因：{{ ruleForm.auditFailReason }}
        </p>
        <div class="audit-data">
          <div class="message">
            <div class="title">品牌车型</div>
            <div class="audit-content">{{ ruleForm.showName }}</div>
            <div class="title">转出价格 (元)</div>
            <div class="audit-content">
              <span class="not-pass">{{ ruleForm.price }}</span>
              <span v-if="ruleForm.placeStatus === 0" class="reference">{{
                ruleForm.minPrice && ruleForm.maxPrice
                  ? `（参考价：${ruleForm.minPrice} - ${ruleForm.maxPrice}）`
                  : ''
              }}</span>
              <span v-if="ruleForm.placeStatus === 2" class="reference">{{
                ruleForm.carBalancePayment &&
                ruleForm.carBalancePayment !== 'null'
                  ? `（尾款：${ruleForm.carBalancePayment}）`
                  : '（尾款：/）'
              }}</span>
            </div>
          </div>
          <div class="message">
            <div class="title">上牌类型</div>
            <div class="audit-content">
              <span :class="`color${ruleForm.placeStatus}`">{{
                cardType[ruleForm.placeStatus]
              }}</span>
              <span v-if="!ruleForm.placeStatus"
                >&nbsp;({{ ruleForm.licensePlateAddress }})</span
              >
              <span v-if="ruleForm.placeStatus === 1"
                >&nbsp;({{ ruleForm.billNum }})</span
              >
              <el-button
                v-if="ruleForm.id"
                size="small"
                style="margin-left: 10px"
                @click="updateData(ruleForm)"
                >修改</el-button
              >
            </div>
            <div class="title">{{ titleList[ruleForm.placeStatus] }}</div>
            <div class="audit-content">
              {{ $date.format(ruleForm.placeTime, 'YYYY-MM-DD') }}
            </div>
          </div>
        </div>
        <p>
          近期发布车辆车牌号（历史已发布 {{ ruleForm.passCount || 0 }} 辆车）
        </p>
        <div
          v-if="ruleForm.licenceNumber && ruleForm.licenceNumber.length"
          class="license-plate-number"
        >
          <template
            v-for="(item, index) in ruleForm.licenceNumber.slice(0, 10)"
            :key="index"
          >
            <div>{{ item }}</div>
          </template>
        </div>
        <p v-else class="not-available">近期暂无发布车辆</p>
        <p>车况信息</p>
        <div class="modules">
          <el-input
            v-model="ruleForm.vehicleCondition"
            type="textarea"
            rows="4"
            readonly
            style="margin-bottom: 10px"
          />
          <div v-if="ruleForm.carImage && ruleForm.carImage.length">
            <div
              v-for="(item, index) in ruleForm.carImage"
              :key="index"
              @click="seeBigImg(item.goodsThumb)"
            >
              <img :src="item.goodsThumb" alt />
              <span v-if="item.goodsCarName" class="tag-item">
                {{ item.goodsCarName }}
              </span>
            </div>
          </div>
        </div>
        <!-- <el-form ref="ruleForm" :model="ruleForm" :rules="rules" :inline="true" label-width="100px" label-position="left">
                        <p class="info-message-mes">
                          <el-form-item label="状态：" label-width="120px">
                            <span :class="classList[ruleForm.status]">{{ ststusList[ruleForm.status] }}</span>
                            <el-button v-if="ruleForm.status === 1 || ruleForm.status === 3" style="margin-left: 10px" @click="changeStatus(ruleForm.status)">{{
                              ruleForm.status === 1 ? '审核不通过' : '审核通过'
                            }}</el-button>
                          </el-form-item>
                        </p>
                        <p class="info-message-mes">
                          <el-form-item label="原因：" label-width="120px">
                            <span>{{ ruleForm.auditFailReason || reasonForFailure[ruleForm.status] }}</span>
                          </el-form-item>
                        </p>
                        <p class="info-message-mes">
                          <el-form-item label="品牌车型:" label-width="120px">
                            <span>{{ ruleForm.showName }}</span>
                          </el-form-item>
                        </p>
                        <p class="info-message-mes">
                          <el-form-item label="转出价格(元)：" label-width="120px">
                            <span>{{ ruleForm.price }}</span>
                          </el-form-item>
                        </p>
                        <p class="info-message-mes">
                          <el-form-item label="购入价格(元)：" label-width="120px">
                            <span>{{ ruleForm.buyPrice }}</span>
                          </el-form-item>
                        </p>
                        <p class="info-message-mes">
                          <el-form-item label="上牌类型：" label-width="100px">
                            <span :class="{ red: ruleForm.placeStatus === 1 || ruleForm.placeStatus === 2 }">{{ cardType[ruleForm.placeStatus] }}</span>
                            <el-button v-if="ruleForm.id" type="primary" size="small" style="margin-left: 10px" @click="updateData(ruleForm)">修改</el-button>
                          </el-form-item>
                        </p>
                        <p v-if="ruleForm.placeStatus === 1" class="info-message-mes">
                          <el-form-item label="车辆开票时间：" label-width="130px">
                            <span style="color: red">{{ $filters.timeFullS(ruleForm.placeTime) }}</span>
                          </el-form-item>
                        </p>
                        <p v-if="ruleForm.placeStatus === 1" class="info-message-mes">
                          <el-form-item label="发票号码：">
                            <span style="color: red">{{ ruleForm.billNum }}</span>
                          </el-form-item>
                        </p>
                        <p v-if="ruleForm.placeStatus === 2" class="info-message-mes">
                          <el-form-item label="订单提车时间：" label-width="100px">
                            <span style="color: red">{{ $filters.timeFullS(ruleForm.placeTime) }}</span>
                          </el-form-item>
                        </p>
                        <p v-if="ruleForm.placeStatus !== 1 && ruleForm.placeStatus !== 2" class="info-message-mes">
                          <el-form-item label="首次上牌：" label-width="100px">
                            <span>{{ $filters.timeFullS(ruleForm.placeTime) }}</span>
                          </el-form-item>
                        </p>
                        <p v-if="ruleForm.placeStatus !== 1 && ruleForm.placeStatus !== 2" class="info-message-mes">
                          <el-form-item label="车牌号：" label-width="100px">
                            <span>{{ ruleForm.licensePlateAddress }}</span>
                          </el-form-item>
                        </p>
                        <p v-if="ruleForm.secondHandMember" style="color: yellow">会员商家</p>
                        <p class="info-message-mes">
                          <el-form-item label="服务：" label-width="100px">
                            <span v-for="(l, i) in provideService" :key="i" class="service-name">{{ l }}&ensp;&ensp;</span>
                          </el-form-item>
                        </p>
                      </el-form> -->
      </el-col>
    </el-row>
    <el-form
      v-if="!isCarAudit"
      ref="imgData"
      :model="ruleForm"
      :inline="true"
      label-width="100px"
    >
      <p
        class="modules-title"
        style="border-bottom: 1px solid #ccc; padding: 5px 0 5px 10px"
      >
        车况信息
      </p>
      <div class="modules">
        <p class="title">
          <!-- {{ ruleForm.title }} -->
          <span v-if="showTimeStatus" class="fl-right"
            >更新时间: &ensp;&ensp;&ensp;{{
              $filters.timeFullS(ruleForm.editTime)
            }}</span
          >
        </p>
        <el-input
          v-model="ruleForm.vehicleCondition"
          type="textarea"
          rows="4"
          readonly
          style="margin-bottom: 10px"
        />
        <div class="item mt20">
          <span>车辆视频/图片</span>
          <div v-if="ruleForm.videoImagesDetailVOS" class="img-box">
            <div
              @click="seeVideoBigImg(image)"
              class="mr10 consult"
              v-for="(image, index) in ruleForm.videoImagesDetailVOS"
              :key="index"
            >
              <img
                :src="
                  image.type === 1
                    ? image.imagesTypeUrl.imgUrl
                    : image.videoInfoDTO.coverUrl
                "
                alt
              />
              <div v-if="image.type === 2" class="video-icon">
                <el-icon class="icon el-icon-caret-right"></el-icon>
              </div>
              <span v-if="index === 0" class="tag-item">
                {{ image.type === 2 ? '封面 | 视频' : '封面' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <choose-show-image ref="showImage" />
    <ChooseCommonDecrypt ref="seePhone" />
    <reject-notice
      ref="rejectNotice"
      :show-radio="true"
      @confirmRejection="getRejectData"
    />
    <el-dialog v-model="dialogStatus" title="信息修改" class="dialog-con">
      <el-form
        ref="updatafuleForm"
        :model="updataFuleForm"
        :inline="true"
        label-width="100px"
      >
        <p>
          上牌类型:&ensp;
          <el-select
            v-model="updataFuleForm.placeStatus"
            @change="updataTimeTip(updataFuleForm.placeStatus)"
          >
            <el-option
              v-for="(item, index) in cardTypeList"
              :key="index"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </p>
        <el-form-item
          v-if="updataFuleForm.placeStatus === 0"
          label="车牌号码:"
          label-width="73px"
        >
          <el-input
            v-focus
            v-model="updataFuleForm.licensePlateAddress"
            type="text"
            maxlength="12"
          />
        </el-form-item>
        <el-form-item
          v-if="updataFuleForm.placeStatus === 1"
          label="发票号码:"
          label-width="73px"
        >
          <el-input
            v-focus
            v-model="updataFuleForm.billNum"
            type="text"
            maxlength="12"
          />
        </el-form-item>
        <p>
          {{ timeTip }}:&ensp;
          <el-date-picker
            v-model="updataFuleForm.placeTime"
            type="date"
            placeholder="选择日期时间"
          />
        </p>
        <div class="dialog-content center footer">
          <el-button @click=";(updataFuleForm = {}), (dialogStatus = false)"
            >取 消</el-button
          >
          <el-button type="primary" @click="verificationFuleForm()"
            >确 定</el-button
          >
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import ChooseCommonDecrypt from '@/components/Dialog/ChooseCommonDecrypt.vue'
import RejectNotice from '../components/rejectNotice.vue'
import { timeFullS } from '@/filters'
import { deepCopy } from '@/utils'
import {
  getNewCarDetail,
  updateAuditStatus,
  UpdateUsedCarData
} from '@/api/garage'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
import { replaceImgUrl } from '@/filters'
export default {
  name: 'UsedCarDetail',
  components: {
    ChooseShowImage,
    ChooseCommonDecrypt,
    RejectNotice
  },
  props: {
    isDetail: {
      type: Boolean,
      default() {
        return false
      }
    },
    isCarAudit: {
      type: Boolean,
      default() {
        return false
      }
    } // 审核
  },
  data() {
    return {
      usedCarId: '', // 详情id
      loading: false,
      showTimeStatus: false, // 是否展示时间
      dialogStatus: false, // 蒙层展示
      timeTip: '首次上牌', // 时间提示
      classList: {
        0: 'be-audited',
        1: 'not-launched',
        2: 'not-pass',
        3: 'have-launched',
        4: 'be-audited',
        5: 'not-launched'
      },
      ststusList: {
        0: '运营下架',
        1: '正常',
        2: '待审核',
        3: '审核不通过',
        4: '个人删除',
        5: '已售出',
        6: '用户下架',
        7: '系统下架',
        8: '车源过期',
        9: '已冻结',
        10: '系统下架-受限下架',
        11: '手动确认售出',
        12: '买家确认售出',
        13: '售出中'
      },
      applyList: {
        审核通过: 1,
        审核不通过: 3
      }, // 接口文档对应 显示
      serviceList: [
        '随时看车',
        '车况描述真实',
        '支持验车',
        '赠送保养',
        '代办过户',
        '包过户',
        '定金可退',
        '7天可退',
        '一口价',
        '可小刀',
        '支持三方验车',
        '准新车',
        '订单车',
        '0次过户',
        '只出本地',
        '包运费'
      ], // 所有可选服务
      provideService: [], // 已有的服务
      ruleForm: {}, // 展示数据
      rules: {},
      dataList: [], // list 展示的相关数据
      cardType: {
        0: '已上牌',
        1: '未上牌',
        2: '订单车'
      },
      titleList: {
        0: '首次上牌时间',
        1: '开票时间',
        2: '提车时间'
      },
      cardTypeList: [
        {
          name: '已上牌',
          value: 0
        },
        {
          name: '未上牌',
          value: 1
        },
        {
          name: '订单车',
          value: 2
        }
      ],
      updataFuleForm: {},
      reasonForFailure: {
        7: '连续30天未刷新车源',
        8: '发票已过期，请重新开票后发布'
      },
      angle: 0,
      size: 1,
      height: 0
    }
  },
  computed: {},
  watch: {},
  activated() {},
  methods: {
    init(data) {
      this.usedCarId = data.id
      this.getDetail()
      this.showTimeStatus = data.showTime || this.showTimeStatus || false
      // this.getImgHeight()
      // this.initRotationScaling()
    },
    getDetail() {
      const me = this
      me.loading = true
      getNewCarDetail({
        id: me.usedCarId
      })
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data
            if (!data.id) {
              return me.$message.error('没有查询到数据')
            }
            batchRecordBeforeAlter(data, me.usedCarId)
            data.imgList =
              data.images && data.images.length ? data.images.split(',') : []
            data.address = `${data.ownerProvince}${data.ownerCity}`
            me.dataList = []
            const showName =
              data.carportName || data.carportGoodsName
                ? `${data.carportName || ''}${data.carportGoodsName || ''}`
                : `${data.brandName || ''}${data.goodsName || ''}`
            data.showName = showName
            me.dataList.push({
              showName: showName,
              mileage: data.mileage,
              transferTimes: data.transferTimes,
              address: `${data.ownerProvince}${data.ownerCity}`,
              price: data.price,
              buyPrice: data.buyPrice,
              title: data.title
            })
            me.provideService = data.provideService
              ? data.provideService.split(',').map((_) => {
                  return me.serviceList[_ - 1]
                })
              : []
            data.showStatus = me.ststusList[data.status]
            me.ruleForm = data
          }
        })
        .catch((err) => {
          console.log(err)
          me.loading = false
        })
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({
          decryptContent: mobile,
          source: 'UsedCarDetail',
          type: 1, // 1手机号解密，2微信号解密，3，身份证解密，4支付宝
          operateType: 14
        })
    },
    // 大图查看图片
    seeBigImg(data, flag) {
      const img = data.drivingLicenseImage ? data.drivingLicenseImage : data
      // this.$refs.showImage.init(flag ? replaceImgUrl(img) : img)
      this.$viewerApi({
        images: [flag ? replaceImgUrl(img) : img],
        options: {
          initialViewIndex: 0
        }
      })
    },
    // 多图多视频查看大图
    seeVideoBigImg(data) {
      if (data.type === 2) {
        window.open(data.videoInfoDTO.sourceVideoUrl)
        return
      }
      let carImageVideo = []
      this.ruleForm.videoImagesDetailVOS &&
        this.ruleForm.videoImagesDetailVOS.map((item) => {
          if (item.type === 1) {
            carImageVideo.push(replaceImgUrl(item.imagesTypeUrl.imgUrl))
          }
        })
      const curIndex = carImageVideo.findIndex(
        (item) => item === data.imagesTypeUrl.imgUrl
      )
      this.$viewerApi({
        images: carImageVideo,
        options: {
          initialViewIndex: curIndex
        }
      })
    },
    // 变更状态
    changeStatus(status) {
      const me = this
      console.log(status)
      const errStatus = 3 // 异常状态，审核不通过
      const num = status === errStatus ? '1' : '3'
      const timeNum = 50
      setTimeout(() => {
        if (num === '3') {
          me.$refs.rejectNotice.init()
          return
        }
        me.updataStatusData({}, num)
      }, timeNum)
    },
    // 拒绝回来的数据
    getRejectData(data) {
      this.updataStatusData(data, '3')
    },
    updateData(data) {
      this.updataFuleForm = deepCopy(data)
      this.updataTimeTip()
      this.dialogStatus = true
    },
    // 更新提示
    updataTimeTip() {
      const placeStatus = this.updataFuleForm.placeStatus
      this.timeTip =
        placeStatus === 0
          ? '首次上牌'
          : placeStatus === 1
          ? '开票时间'
          : '订单提车时间'
    },
    // 验证提交数据
    verificationFuleForm() {
      if (
        this.updataFuleForm.placeStatus === 0 &&
        !this.updataFuleForm.licensePlateAddress
      ) {
        return this.$message.error('请输入车牌号码')
      }
      const licenseNum = 7 // 7位数的车牌号码
      if (
        this.updataFuleForm.placeStatus === 0 &&
        this.updataFuleForm.licensePlateAddress &&
        this.updataFuleForm.licensePlateAddress.length !== licenseNum
      ) {
        return this.$message.error('请输入7位数的车牌号码')
      }
      if (
        this.updataFuleForm.placeStatus === 1 &&
        !this.updataFuleForm.billNum
      ) {
        return this.$message.error('请输入发票号码')
      }
      const billNum = 8 // 8位数发票号码
      if (
        this.updataFuleForm.placeStatus === 1 &&
        this.updataFuleForm.billNum &&
        this.updataFuleForm.billNum.length !== billNum
      ) {
        return this.$message.error('请输入8位数发票号码')
      }
      if (!this.updataFuleForm.placeTime) {
        return this.$message.error('请选择时间')
      }
      this.confirmUpdataFuleForm()
    },
    // 提交数据
    confirmUpdataFuleForm() {
      const me = this
      const postData = {
        id: me.updataFuleForm.id,
        placeStatus: me.updataFuleForm.placeStatus,
        placeTime: me.updataFuleForm.placeTime
          ? Math.floor(me.updataFuleForm.placeTime)
          : '',
        billNum:
          me.updataFuleForm.placeStatus === 1
            ? me.updataFuleForm.billNum || ''
            : '',
        licensePlateAddress:
          me.updataFuleForm.placeStatus === 0
            ? me.updataFuleForm.licensePlateAddress || ''
            : ''
      }
      console.log(postData)
      UpdateUsedCarData(postData)
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('更新数据成功')
            me.updataFuleForm.placeTime = me.updataFuleForm.placeTime
              ? Math.floor(me.updataFuleForm.placeTime)
              : ''
            me.ruleForm = me.updataFuleForm
            me.updataFuleForm = {}
            me.dialogStatus = false
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    updataStatusData(rejectData, status) {
      const me = this
      updateAuditStatus({
        id: me.usedCarId,
        status: status,
        auditFailType: rejectData.auditFailType || '',
        auditFailReason: rejectData.auditFailReason || ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            setTimeout(() => {
              me.getDetail()
            }, 1000)
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.used-form {
  padding: 20px;
  .not-launched {
    color: #999;
  }
  .have-launched {
    color: #00cc67;
  }
  .not-pass {
    color: red;
  }
  .be-audited {
    color: #cc6600;
  }
  .bottom {
    margin: 0 0 16px 0;
  }
  .audit-data {
    border-right: 1px solid #dcdfe6;
    border-top: 1px solid #dcdfe6;
    .message {
      display: flex;
      div {
        color: #606266;
        padding: 15px 0;
        border-left: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        display: flex;
        align-items: center;
      }
      .title {
        width: 15%;
        justify-content: center;
      }
      .audit-content {
        width: 35%;
        padding-left: 10px;
        .color0 {
          color: #00cc67;
        }
        .color1 {
          color: #cc6600;
        }
        .color2 {
          color: #4b6bf7;
        }
        .reference {
          font-size: 14px;
        }
      }
    }
  }
  .license-plate-number {
    display: flex;
    flex-wrap: wrap;
    border-left: 1px solid #dcdfe6;
    // border-top: 1px solid #dcdfe6;
    div {
      color: #606266;
      padding: 10px 0;
      width: 20%;
      text-align: center;
      overflow: hidden;
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;
      &:nth-child(-n + 5) {
        border-top: 1px solid #dcdfe6;
      }
    }
  }
  .not-available {
    color: #999;
  }
  .consult {
    display: inline-block;
    position: relative;
    span {
      position: absolute;
      top: 0;
      left: 0;
      display: inline-block;
      padding: 3px 5px;
      background-color: #ffffff;
      border: 1px solid #dedede;
      font-size: 12px;
    }
  }
  .img-audit-box {
    .operation-icon {
      text-align: right;
      -moz-user-select: none;
      -webkit-user-select: none;
      padding: 10px;
      position: relative;
      z-index: 1001;
      // background-color: #f1f1f1;
      .size {
        font-size: 32px;
        margin: 0 5px;
        cursor: pointer;
      }
    }
  }
}
.info-message {
  position: relative;
  padding-left: 500px;
  min-height: 380px;
}
.info-message-img-audit {
  display: inline-block;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1000;
}
.info-message-img {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-block;
  width: 500px;
  height: 375px;
  object-fit: cover;
}
.info-message-mes {
  margin: 0;
}
.image {
  display: inline-block;
  width: 200px;
  height: 150px;
  object-fit: cover;
  margin-right: 10px;
  border: 1px solid #dedede;
}
.img-box {
  display: flex;
  flex-wrap: wrap;
  .consult {
    display: inline-block;
    position: relative;
    .tag-item {
      position: absolute;
      top: 0;
      left: 0;
      display: inline-block;
      padding: 3px 5px;
      background-color: #ffffff;
      border: 1px solid #dedede;
      font-size: 12px;
    }
    img {
      width: 100px;
      height: 80px;
      object-fit: cover;
    }
  }
  .video-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    width: 50px;
    height: 50px;
    border: 2px solid black;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    justify-content: center;
    align-items: center;

    .icon {
      font-size: 30px;
    }
  }
}
</style>
