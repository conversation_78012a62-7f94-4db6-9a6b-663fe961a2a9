<template>
  <div class="section">
    <el-button type="primary" style="margin: 10px 0" @click="insterType">
      新增
    </el-button>
    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column prop="businessTypeName" label="业务" align="center">
        <template #default="{ row }">
          <span>{{ row.businessTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" align="center">
        <template #default="{ row }">
          <span>{{ row.certificateTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="domainName" label="域名" align="center" />
      <el-table-column prop="version" label="版本" align="center" />
      <el-table-column prop="reminderUname" label="提醒人" align="center" />
      <el-table-column prop="reminderTime" label="下次提醒日期" align="center">
        <template #default="{ row }">
          <span>{{ $filters.timeFull(row.reminderTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="operate(row)">操作</el-button>
          <el-button type="primary" link @click="deleteItem(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 40]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 20px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <popuDetail ref="popuDetailRef" @success="updateList" />
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import popuDetail from './popu-detail.vue'
import { CertificateEnum, CertificateBusinessEnum } from '@/utils/enum'
import { getCertificateList, postCertificateDelete } from '@/api/system'
const router = useRouter()
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const handleSizeChange = (val) => {
  pageSize.value = val
  getDataConfigList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getDataConfigList()
}

onMounted(() => {
  getDataConfigList()
})

const getDataConfigList = () => {
  getCertificateList({
    page: currentPage.value,
    limit: pageSize.value
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      tableData.value = data.listData || []
      total.value = data.total || 0
    }
  })
}

const popuDetailRef = ref(null)

const operate = (row) => {
  popuDetailRef.value.open(row)
}

const insterType = () => {
  popuDetailRef.value.open()
}

const deleteItem = (row) => {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    postCertificateDelete({ id: row.id }).then((res) => {
      if (res.data.code === 0) {
        ElMessage.success('操作成功')
        getDataConfigList()
      } else {
        ElMessage.error(res.data.msg || '操作失败')
      }
    })
  })
}

const updateList = () => {
  getDataConfigList()
}
</script>
<style lang="scss" scoped>
.section {
  padding: 20px;
}
</style>
