<template>
  <div class="version-content">
    <header class="action">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="80px"
        style="margin: 10px 0"
      >
        <el-form-item label="平台">
          <el-select v-model="form.platform" clearable>
            <el-option
              v-for="(value, index) in platformList"
              :key="value"
              :label="value"
              :value="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本" clearable>
          <el-input v-focus v-model="form.version" type="text" />
        </el-form-item>
        <el-form-item label="ID" clearable>
          <el-input v-focus v-model="form.id" type="text" />
        </el-form-item>
        <el-form-item label="反馈者ID" clearable>
          <el-input v-focus v-model="form.autherid" type="text" />
        </el-form-item>
        <el-form-item label="是否处理">
          <el-select v-model="form.status" clearable>
            <el-option
              v-for="value in modelList"
              :key="value.type"
              :label="value.name"
              :value="value.type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="问题类型">
          <el-select v-model="form.type" clearable>
            <el-option
              v-for="(value, index) in typeList"
              :key="value"
              :label="value"
              :value="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="createDateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="是否跟进">
          <el-select v-model="form.followflag">
            <el-option
              v-for="(value, index) in effective"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型">
          <el-select v-model="form.businessType" clearable>
            <el-option
              v-for="(value, index) in businessList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户登录">
          <el-select v-model="form.loginStatus">
            <el-option
              v-for="(value, index) in usersLoginList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label>
          <el-button type="primary" @click="getList({ page: 1 })"
            >查询</el-button
          >
          <el-button @click="initGetList">重置</el-button>
          <el-button type="primary" @click="exportExcel()">导出Excel</el-button>
        </el-form-item>
      </el-form>
    </header>
    <el-table
      :data="versionData"
      border
      highlight-current-row
      height="65vh"
      @row-dblclick="editorData"
    >
      <el-table-column prop="id" label="ID" align="center" />
      <el-table-column prop="autherid" label="反馈者ID" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.autherid ? scope.row.autherid : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="auther" label="反馈者" align="center" />
      <el-table-column prop="mobile" label="手机" align="center" width="140">
        <template v-slot="scope">
          <span>{{ scope.row.mobile }}</span
          >&ensp;
          <el-button
            v-if="scope.row.mobile"
            type="primary"
            size="small"
            @click="seeMobile(scope.row.mobile)"
            >查看号码</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="联系方式" align="center" width="140">
        <template v-slot="scope">
          <span>{{ scope.row.contactWay }}</span
          >&ensp;
          <el-button
            v-if="scope.row.contactWay"
            type="primary"
            size="small"
            @click="seeMobile(scope.row.contactWay)"
            >查看号码</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="device" label="手机型号" align="center" />
      <el-table-column prop="businessType" label="业务类型" align="center">
        <template v-slot="scope">
          <span>{{
            scope.row.businessType
              ? convertBusinessList[scope.row.businessType]
              : ''
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="问题类型" align="center">
        <template v-slot="scope">
          <span>{{ typeList[scope.row.type] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="content"
        label="反馈内容"
        width="350"
        align="center"
      >
        <template v-slot="scope">
          <div
            v-for="(content, index) in scope.row.showContent"
            :key="index"
            class="content"
          >
            <div v-if="content.type === '1'" class="text-mes">
              <!-- 文字部分 -->
              <pre
                :class="{ 'text-link': content.link }"
                class="text-content"
                v-text="content.content"
              />
            </div>
            <div v-if="content.type === '2'" class="img-content">
              <!-- 单张图或图文模式 -->
              <div v-if="content.img" class="show-img-content">
                <img :src="content.img" alt class="detail-imgs" />
              </div>
            </div>
          </div>
          <div v-if="scope.row.workOrderId" class="work-order-id">
            <span>关联工单ID：</span>
            <template
              v-for="(id, i) in scope.row.workOrderId.split(',')"
              :key="i"
            >
              <span v-if="i">,</span>
              <el-button type="primary" link @click="goToDetail(id)">
                {{ id }}
              </el-button>
            </template>
          </div>
          <div
            v-if="scope.row.shopId && scope.row.businessType == 15"
            class="work-order-id"
          >
            <span>关联驾校ID： {{ scope.row.shopId }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="platform" label="平台" align="center">
        <template v-slot="scope">
          <div>{{ platformList[scope.row.platform] }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="version" label="版本" align="center" />
      <el-table-column prop="osVersion" label="系统" align="center" />
      <el-table-column
        prop="followflag"
        label="是否跟进"
        align="center"
        width="150px"
      >
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.followflag"
            active-color="#13ce66"
            inactive-color="#C0C0C0"
            @change="changeFollowflag(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="feedbackObject" label="反馈对象" align="center" />
      <el-table-column prop="feedbackResult" label="反馈结果" align="center" />
      <el-table-column
        prop="createDate"
        align="center"
        width="150"
        label="回复用户时间"
      >
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.handledate)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        align="center"
        width="150"
        label="用户反馈时间"
      >
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.createdate)
        }}</template>
      </el-table-column>
      <el-table-column label="设备ID" align="center" width="140">
        <template v-slot="scope">
          <el-button
            type="primary"
            size="small"
            @click="GetUserAccountDetails(scope.row)"
            >查看设备ID</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="20"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="justify-content: center; margin-top: 15px"
      @size-change="currentChange"
      @current-change="currentChange"
    />
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      append-to-body
      width="60%"
      top="5vh"
    >
      <template #header>
        <div class="custom-header">
          <el-button type="primary" @click="creatWorkOrder(editorListData)"
            >创建工单</el-button
          >
          <div class="title">反馈信息处理</div>
          <div class="placeholder"></div>
        </div>
      </template>
      <el-form :model="editorListData">
        <div class="business-type">
          <p style="width: 150px">业务类型</p>
          <el-select v-model="editorListData.businessType">
            <el-option
              v-for="(value, index) in businessList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </div>
        <div class="feedbackContent">
          <p class="feedback">反馈内容</p>
          <div class="content">
            <div
              v-for="(content, index) in editorListData.showContent"
              :key="index"
            >
              <div v-if="content.type === '1' && content.content">
                <el-input
                  :rows="4"
                  v-model="content.content"
                  type="textarea"
                  disabled
                ></el-input>
              </div>
            </div>
          </div>
          <div
            v-if="
              editorListData.showContent && editorListData.showContent.length
            "
            class="feedbackImage"
          >
            <div
              v-for="(content, index) in editorListData.showContent.filter(
                (content) => content.type === '2' && content.img
              )"
              :key="index"
            >
              <img
                :src="content.img"
                style="width: 100%; margin-bottom: 10px"
                @click="seeBigImg(content.img)"
              />
            </div>
          </div>
        </div>

        <div class="feedback-info">
          <div class="feedback-info_item">
            <p class="prob">
              反馈者ID&nbsp;&nbsp;
              <span class="answer">{{ editorListData.autherid }}</span>
            </p>
            <p class="prob">
              平台&nbsp;&nbsp;
              <span class="answer">{{
                platformList[editorListData.platform]
              }}</span>
            </p>
          </div>
          <div class="feedback-info_item">
            <p class="prob">
              反馈者昵称&nbsp;&nbsp;
              <span class="answer">{{ editorListData.auther }}</span>
            </p>
            <p class="prob">
              版本、系统&nbsp;&nbsp;
              <span class="answer"
                >{{ editorListData.osVersion }}、{{
                  editorListData.version
                }}</span
              >
            </p>
          </div>
          <div class="feedback-info_item">
            <p class="prob">
              手机号码&nbsp;&nbsp;
              <span class="answer" style="margin-right: 20px">{{
                editorListData.mobile
              }}</span>
              <el-button
                v-if="editorListData.mobile"
                type="primary"
                size="small"
                @click="seeMobile(editorListData.mobile)"
                >查看号码</el-button
              >
            </p>
            <p class="prob">
              问题类型&nbsp;&nbsp;
              <span class="answer">{{ typeList[editorListData.type] }}</span>
            </p>
          </div>
          <div class="feedback-info_item">
            <p class="prob">
              联系方式&nbsp;&nbsp;
              <span class="answer" style="margin-right: 20px">{{
                editorListData.contactWay
              }}</span>
              <el-button
                v-if="editorListData.contactWay"
                type="primary"
                size="small"
                @click="seeMobile(editorListData.contactWay)"
                >查看号码</el-button
              >
            </p>
            <p class="prob">
              用户反馈时间&nbsp;&nbsp;
              <span class="answer">{{
                $filters.timeFullS(editorListData.createdate)
              }}</span>
            </p>
          </div>
          <div class="feedback-info_item">
            <p class="prob">
              手机型号&nbsp;&nbsp;
              <span class="answer">{{ editorListData.device }}</span>
            </p>
            <p class="prob">
              回复用户时间&nbsp;&nbsp;
              <span class="answer">{{
                $filters.timeFullS(editorListData.handledate)
              }}</span>
            </p>
          </div>
          <div class="feedback-info_item">
            <p class="prob">
              反馈来源&nbsp;&nbsp;
              <span class="answer">{{
                editorListData.type != 18
                  ? '本APP下的吐槽反馈'
                  : '创作者平台的吐槽反馈'
              }}</span>
            </p>
            <p class="prob">
              关联工单ID&nbsp;&nbsp;
              <span class="answer work-order-box">
                <el-input
                  v-if="isCompileId"
                  v-model="workOrderId"
                  clearable
                  size="small"
                  ref="compileInput"
                  class="compile-input"
                  @input="restrictedInput"
                  @blur="changeWorkOrderId"
                />
                <template v-else>
                  <template v-for="(id, i) in workOrderId.split(',')" :key="i">
                    <span v-if="i">,</span>
                    <el-button type="primary" link @click="goToDetail(id)">
                      {{ id }}
                    </el-button>
                  </template>
                </template>
                <el-icon
                  v-if="!isCompileId"
                  class="compile-icon"
                  @click="compileWorkOrderId"
                >
                  <IconEditPen />
                </el-icon>
              </span>
            </p>
          </div>
        </div>
        <div
          v-if="editorListData.autherid || editorListData.contactWay"
          class="reply-user"
        >
          <p style="width: 130px">回复用户</p>
          <div style="width: calc(80% - 150px)">
            <el-tabs
              v-model="informStatus"
              type="border-card"
              @tab-click="handleClick"
            >
              <el-tab-pane
                :disabled="!Boolean(editorListData.autherid)"
                name="notifyflag"
                label="系统通知"
              >
                <el-input
                  v-model="editorListData.handleresult"
                  :rows="5"
                  type="textarea"
                  placeholder="请输入回复用户内容"
                />
                <p></p>
                <div style="display: flex; align-items: center">
                  <b style="width: 80px">跳转链接</b>
                  <el-input
                    v-model="editorListData.handleLink"
                    placeholder="请输入跳转链接"
                  ></el-input>
                </div>

                <div class="feedBackSend">
                  <el-button
                    type="primary"
                    :disabled="!editorListData.autherid"
                    @click="feedBackSend"
                    >发送</el-button
                  >
                </div>
              </el-tab-pane>
              <el-tab-pane
                :disabled="!Boolean(editorListData.contactWay)"
                name="sendMessageFlag"
                label="短信通知"
              >
                <el-select
                  v-model="replyFlag"
                  size="small"
                  style="margin-bottom: 2px"
                >
                  <el-option
                    v-for="(value, index) in replyFlagList"
                    :key="value"
                    :label="value"
                    :value="index"
                  />
                </el-select>
                <el-input
                  v-model="placeholderContent"
                  :rows="4"
                  type="textarea"
                  readonly
                />
                <div class="feedBackSend">
                  <el-button
                    type="primary"
                    :disabled="!editorListData.contactWay"
                    @click="feedBackSend"
                    >发送</el-button
                  >
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div class="feedback-object">
          <p style="width: 150px">反馈对象</p>
          <el-input
            v-model="editorListData.feedbackObject"
            :rows="2"
            autosize
            type="textarea"
            placeholder="请输入反馈对象"
          />
        </div>
        <div class="feedback-result">
          <p style="width: 150px">反馈结果</p>
          <el-input
            v-model="editorListData.feedbackResult"
            :autosize="{ minRows: 4 }"
            type="textarea"
            placeholder="请输入反馈结果"
          />
        </div>
        <div class="food">
          <el-button type="success" @click="submitForm">确认</el-button>
          <el-button type="danger" @click="handleClose">取消</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog v-model="deviceVisible" title="设备信息" center width="450px">
      <el-form ref="ruleForm" :model="deviceinfo" label-width="100px">
        <el-form-item label="deviceId:">
          <span>{{ deviceinfo.deviceId }}</span>
        </el-form-item>
        <el-form-item label="deviceIdBus:">
          <span>{{ deviceinfo.deviceIdBus }}</span>
        </el-form-item>
        <el-form-item label="反馈设备ID:">
          <span>{{ deviceinfo.feedbackDeviceId }}</span>
        </el-form-item>
      </el-form>
      <div class="food">
        <el-button type="success" @click="deviceVisible = false"
          >确认</el-button
        >
      </div>
    </el-dialog>
    <choose-show-image ref="showImage" />
    <choose-see-phone ref="seePhone" />
    <CreateWorkOrder
      ref="createWorkOrder"
      source="feedback"
      @success="success"
    ></CreateWorkOrder>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { deepCopy } from '@/utils'
import {
  GetFeedbackList,
  FeedBackSave,
  UpdateFollow,
  FeedBackSend
} from '@/api/system'
import { getUserAccountDetails } from '@/api/user'
import { forwardPickerOptions } from '@/utils/configData'
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import ChooseSeePhone from '@/components/Dialog/ChooseSeePhone.vue'
import { timeFullS } from '@/filters'
import { mapGetters } from 'vuex'
import { platformList } from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import { recordOldData, recordBeforeAlter } from '@/utils/enum/logData'
import { MANAGERURL } from '@/utils/configData/config'
import CreateWorkOrder from '@/components/CreateWorkOrder/create-work-order.vue'
import { EditPen as IconEditPen } from '@element-plus/icons-vue'
platformList[31] = '鸿蒙'
export default {
  data() {
    return {
      pickerOptions: forwardPickerOptions,
      platformList: platformList,
      dialogVisible: false,
      // 数据发送
      isPostDataStatus: false,
      // 页码
      page: 1,
      // 反馈列表总数
      total: 0,
      form: {
        id: '', // 反馈id
        autherid: '', // 反馈者ID
        status: '', // 状态
        beginDate: '', // 开始
        endDate: '', // 结束
        version: '', //  版本
        origin: '1', // 反馈来源
        platform: '', // 平台
        type: '', // 类型
        businessType: '', // 业务类型
        followflag: '',
        loginStatus: '', // 登录状态
        limit: 20
      },
      // 编辑的反馈信息
      editorListData: {
        businessType: ''
      },
      // 反馈列表
      versionData: [],
      modelList: [
        {
          name: '是',
          type: '1'
        },
        {
          name: '否',
          type: '0'
        }
      ],
      typeList: {
        1: '用车功能问题',
        2: '车辆信息问题',
        3: '商家、驾校问题',
        4: '内容、审核、圈子问题',
        5: '功能问题',
        6: '其他问题或者想要的功能参数',
        7: '一口价反馈',
        11: '车辆参数配置问题',
        12: '没有找到品牌',
        13: '没有找到车款',
        14: '车辆价格问题',
        15: '车辆图片问题',
        16: '商城问题',
        17: '账号问题',
        18: '创作者平台问题',
        19: '网络环境整治行动',
        20: '骑行榜单问题',
        21: '厂商客服电话查询',
        22: '实测数据问题'
      },
      sourceEnum: {
        xm: '小米',
        hw: '华为',
        vi: 'VIVO',
        qm: '七麦数据',
        wdj: '豌豆荚'
      },
      businessList: {},
      convertBusinessList: {},
      convertTypeList: {},
      // 是否跟进
      effective: { 是: 1, 否: 0 },
      usersLoginList: { 全部: '', 是: 1, 否: 0 },
      informStatus: 'notifyflag',
      replyFlag: '1',
      replyFlagList: {
        1: '默认回复',
        2: '审核回复',
        3: '通用问题回复'
      },
      placeholderContent:
        '亲爱的摩友你好，平台已经收到你的反馈，感谢你对摩托范的支持。',
      placeholderContentList: {
        1: '亲爱的摩友你好，平台已经收到你的反馈，感谢你对摩托范的支持。',
        2: '亲爱的摩友你好，平台已经收到你的反馈，我们会尽快审核你发布的内容，感谢你对摩托范的支持。',
        3: '亲爱的摩友你好，平台已经收到你的反馈，我们会尽快处理，感谢你对摩托范的支持。'
      },
      deviceVisible: false,
      // 设备信息
      deviceinfo: {},
      dayjs,
      workOrderId: '',
      workOrderIdInit: '',
      isCompileId: false
    }
  },
  name: 'FeedbackProcessing',
  components: {
    ChooseShowImage,
    ChooseSeePhone,
    CreateWorkOrder,
    IconEditPen
  },
  computed: {
    ...mapGetters(['uid', 'token']),
    createDateRange: {
      get() {
        if (this.form.beginDate && this.form.endDate) {
          return [this.form.beginDate, this.form.endDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.beginDate = value[0]
          this.form.endDate = value[1]
          console.log('==============')
          console.log(this.form.beginDate)
          console.log(this.form.endDate)
        } else {
          this.form.beginDate = ''
          this.form.endDate = ''
        }
      }
    }
  },
  watch: {
    replyFlag(value) {
      this.placeholderContent = this.placeholderContentList[value]
    },
    $route: {
      deep: true,

      handler(route) {
        const list = {
          车库: '1',
          二手车: '2',
          驾校报名: '15',
          新车: '3',
          圈子: '4',
          内容: '5',
          视频: '6',
          骑行: '7',
          广告: '8',
          认证: '9',
          推送: '10',
          能量体系: '11',
          账号: '12',
          用车功能: '13',
          其他: '14'
        }
        if (route.name === 'FeedbackProcessing') {
          delete list['车库']
          this.businessList = list
        } else {
          this.businessList = list
        }
        this.convertBusinessList = convertKeyValueEnum(this.businessList)
      },

      immediate: true
    }
  },
  created() {},
  activated() {
    this.convertTypeList = convertKeyValueEnum(this.typeList)

    this.getList({ page: 1 })
  },
  methods: {
    // 获取设备信息
    GetUserAccountDetails(row) {
      const me = this
      const uid = row.autherid
      me.deviceinfo = {}
      if (!uid) {
        me.deviceinfo.feedbackDeviceId = row.deviceId || ''
        me.deviceVisible = true
        return
      }
      getUserAccountDetails({
        uid: uid
      })
        .then((response) => {
          console.log(response, 'response')
          if (response.data.code === 0) {
            me.deviceinfo = response.data.data
          } else {
            me.deviceinfo = {}
            return me.$message.error('用户数据获取失败')
          }
        })
        .finally(() => {
          me.deviceinfo.feedbackDeviceId = row.deviceId || ''
          me.deviceVisible = true
        })
    },
    // 获取版本列表
    getList(paramsObj) {
      const me = this
      me.page = paramsObj.page
      const requestParams = {
        ...this.form,
        ...paramsObj
      }
      if (
        this.$route.name === 'FeedbackProcessing' &&
        this.form.businessType === ''
      ) {
        requestParams.excludeTypeCar = 1
      }
      console.log(requestParams, 9999)
      GetFeedbackList(requestParams).then((response) => {
        if (response.status === 200) {
          const data = response.data
          recordOldData(data.list)
          data.list.map(function (value) {
            value.showContent = ''
            if (value.content.length) {
              // 兼容老数据格式
              value.showContent =
                value.content.indexOf('[{') > -1
                  ? JSON.parse(value.content).splice(0, 3)
                  : [{ content: value.content, type: '1' }]
            }
          })
          me.versionData = data.list
          me.versionData.map((_) => {
            _.followflag = !!_.followflag
            _.businessType = _.businessType === null ? '' : _.businessType
          })
          me.total = data.total
          if (me.dialogVisible) {
            const item = me.versionData.find(
              (_) => _.id === me.editorListData.id
            )
            if (item) {
              me.workOrderId = item.workOrderId || ''
            }
          }
        }
      })
    },
    // 改变是否跟进
    changeFollowflag(item) {
      const me = this
      UpdateFollow({
        id: item.id,
        followflag: item.followflag === true ? 1 : 0
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('修改成功')
          } else {
            item.followflag = false
            me.$message.error('修改失败')
          }
        })
        .catch((_) => {
          item.followflag = false
          me.$message.error('修改失败')
        })
    },
    // 更新页码
    currentChange(page) {
      this.page = page
      this.getList({
        page: page
      })
    },
    // 重置
    initGetList() {
      this.form = {
        id: '', // 反馈id
        autherid: '', // 反馈者ID
        status: '', // 状态
        beginDate: '', // 开始
        endDate: '', // 结束
        version: '', //  版本
        origin: '1', // 反馈来源
        platform: '', // 平台
        type: '', // 类型
        businessType: '', // 业务类型
        followflag: '',
        loginStatus: '', // 登录状态
        limit: 20
      }
      this.getList({ page: 1 })
    },
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(link)
    },
    // 编辑详情
    editorData(data) {
      this.editorListData = deepCopy(data)
      this.editorListData.showContent = ''
      if (data.content.length) {
        // 兼容老数据格式
        this.editorListData.showContent =
          this.editorListData.content.indexOf('[{') > -1
            ? JSON.parse(this.editorListData.content)
            : [{ content: this.editorListData.content, type: '1' }]
      }
      this.editorListData.businessType = String(
        this.editorListData.businessType
      )
      this.informStatus = this.editorListData.autherid
        ? 'notifyflag'
        : this.editorListData.contactWay
        ? 'sendMessageFlag'
        : 'notifyflag'
      this.replyFlag = this.editorListData.replyFlag
        ? String(this.editorListData.replyFlag)
        : '1'
      this.editorListData.notifyflag =
        this.informStatus === 'notifyflag' ? true : false
      this.editorListData.sendMessageFlag =
        this.informStatus === 'sendMessageFlag' ? true : false
      if (!this.editorListData.autherid && !this.editorListData.contactWay) {
        this.editorListData.notifyflag = false
        this.editorListData.sendMessageFlag = false
      }
      this.workOrderId = data.workOrderId || ''
      this.dialogVisible = true
    },
    // 提交
    submitForm() {
      const me = this
      if (me.isPostDataStatus) {
        me.$message.error('已有数据正在发送')
        return
      }
      me.isPostDataStatus = true
      const postData = deepCopy(me.editorListData)
      postData.createdate = timeFullS(postData.createdate)
      ;(postData.followflag = postData.followflag ? 1 : 0),
        (postData.notifyflag = postData.notifyflag ? 1 : 0),
        (postData.sendMessageFlag = postData.sendMessageFlag ? 1 : 0),
        (postData.smsnotify = 0),
        (postData.replyFlag = postData.sendMessageFlag ? me.replyFlag : ''),
        (postData.workOrderId = me.workOrderId)
      delete postData.handledate
      recordBeforeAlter(postData, 'id')
      FeedBackSave(postData)
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('处理成功')
            setTimeout(() => {
              me.handleClose()
              me.getList({ page: me.page })
            }, 200)
          }
          me.isPostDataStatus = false
        })
        .catch((err) => {
          me.isPostDataStatus = false
          me.$message.error(err.message || '处理成功失败')
        })
    },
    handleClick(tab) {
      const me = this
      me.editorListData.notifyflag =
        tab.props.label === '系统通知' ? true : false
      me.editorListData.sendMessageFlag =
        tab.props.label === '短信通知' ? true : false
    },
    // 反馈优化
    feedBackSend() {
      const me = this
      if (!me.editorListData.handleresult && me.editorListData.notifyflag) {
        me.$message.error('请填写回复用户内容')
        return
      }
      if (me.isPostDataStatus) {
        me.$message.error('已有数据正在发送')
        return
      }
      me.isPostDataStatus = true
      const postData = deepCopy(me.editorListData)
      postData.createdate = timeFullS(postData.createdate)
      ;(postData.followflag = postData.followflag ? 1 : 0),
        (postData.notifyflag = postData.notifyflag ? 1 : 0),
        (postData.sendMessageFlag = postData.sendMessageFlag ? 1 : 0),
        (postData.smsnotify = 0),
        (postData.replyFlag = postData.sendMessageFlag ? me.replyFlag : ''),
        delete postData.handledate
      FeedBackSend(postData)
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('发送成功')
          }
          me.isPostDataStatus = false
        })
        .catch((err) => {
          me.isPostDataStatus = false
          me.$message.error(err.message || '发送失败')
        })
    },
    // 取消
    handleClose() {
      this.dialogVisible = false
      this.$nextTick(() => {
        this.editorListData = {}
        this.workOrderId = ''
        this.workOrderIdInit = ''
        this.isCompileId = false
      })
    },
    // 导出Excel表格
    exportExcel() {
      const me = this
      me.$confirm('你确认导出到Excel么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const getData = deepCopy(this.form)
          if (
            this.$route.name === 'FeedbackProcessing' &&
            this.form.businessType === ''
          ) {
            getData.excludeTypeCar = 1
          }
          console.log('导出到Excel')
          window.open(
            `${MANAGERURL}forum/oss/feedBackController/export?id=${
              getData.id
            }&autherid=${getData.autherid}&status=${getData.status}&beginDate=${
              getData.beginDate
            }&endDate=${getData.endDate}&version=${getData.version}&origin=${
              getData.origin
            }&platform=${getData.platform}&type=${
              getData.type || ''
            }&businessType=${getData.businessType}&followflag=${
              getData.followflag
            }&ossUserId=${me.uid}&ossToken=${me.token}${
              getData.excludeTypeCar
                ? '&excludeTypeCar=' + getData.excludeTypeCar
                : ''
            }`
          )
        })
        .catch()
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({
          mobile: mobile,
          source: 'feedbackProcessing'
        })
    },
    creatWorkOrder(data) {
      let content = ''
      const imgList = []
      data.showContent &&
        data.showContent.forEach((_) => {
          if (_.type === '1' && _.content) {
            content = !content ? _.content : content + ';' + _.content
          }
          if (_.type === '2' && _.img) {
            imgList.push(_.img)
          }
        })
      this.$refs.createWorkOrder.init({
        feedbackSource: 1,
        feedbackId: data.autherid || '', //反馈用户ID
        feedbackName: data.auther || '', //反馈用户名
        workOrderDesc: content, //问题描述
        datas: imgList,
        feedBackToWorkOrderId: data.id
      })
    },
    success() {
      this.getList({ page: this.page })
    },
    goToDetail(id) {
      this.$router.push({
        name: 'WorkerOrderDetail',
        query: { id }
      })
      this.handleClose()
    },
    compileWorkOrderId() {
      this.workOrderIdInit = this.workOrderId
      this.isCompileId = true
      this.$nextTick(() => {
        this.$refs.compileInput.focus()
      })
    },
    restrictedInput(e) {
      this.workOrderId = e.replace(/[^\d,]/g, '')
    },
    changeWorkOrderId() {
      const arr = []
      this.workOrderId.split(',').forEach((_) => {
        if (_ && !arr.includes(_)) {
          arr.push(_)
        }
      })
      this.workOrderId = arr.join(',')
      this.isCompileId = false
      // if (this.workOrderId !== this.workOrderIdInit) { }
    }
  }
}
</script>

<style lang="scss">
.el-textarea__inner {
  width: 74%;
  line-height: 30px !important;
}

.reply-user {
  .el-textarea__inner {
    width: 100%;
  }
}
</style>

<style lang="scss" scoped>
.action {
  margin: 10px;
}

.feedback-content {
  max-height: 300px;
  overflow: hidden;

  .feedback-show-content {
    overflow-y: scroll;
    max-height: 300px;
  }
}

.content {
  margin-bottom: 10px;
  padding: 0 14px;

  .icon {
    display: inline-block;
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: #fff;
    z-index: 1;
  }

  .text-mes {
    .answer-all {
      font-size: 14px;
      color: #ff8400;

      img {
        width: 15px;
        height: 15px;
        transform: rotate(90deg);
      }
    }
  }

  .text-content {
    font-size: 17px;
    color: #333;
    line-height: 24px;
    word-wrap: break-word;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    letter-spacing: 0.5px;

    .short-topic {
      color: #2eaee9;
      font-size: 17px;
    }
  }

  .text-link {
    padding: 0 0 0 20px;
    color: #2eaee5;
  }

  .img-content {
    position: relative;

    .show-img-content {
      position: relative;
      min-height: 30px;

      .detail-imgs {
        max-height: 300px;
        max-width: 300px;
      }
    }

    .del-img {
      position: absolute;
      right: 0;
      top: 0;
      width: 30px;
      height: 30px;
    }
  }

  .content-explain {
    margin-top: 15px;

    img {
      width: 10px;
      height: 10px;
      margin: 3px 6px;
      position: absolute;
    }

    p {
      padding: 0 0 0 25px;
      color: #999;
      font-size: 14px;
      line-height: 20px;
      white-space: pre-wrap;
      white-space: -moz-pre-wrap;
      white-space: -pre-wrap;
      white-space: -o-pre-wrap;
      word-wrap: break-word;
      overflow: hidden;
    }
  }
}

.food {
  text-align: center;
  margin-top: 15px;
}

.feedbackContent {
  width: 100%;
  height: 120px;
  padding: 20px 0px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  position: relative;

  .feedback {
    font-size: 14px;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
  }

  .content {
    position: absolute;
    left: 134px;
    width: 60%;
    height: 100%;
  }

  /* ::-webkit-scrollbar {
    display: none;
  } */
  .feedbackImage {
    width: 170px;
    overflow-y: auto;
    height: 680px;
    position: relative;
    z-index: 10;
  }
}

.reply-user {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .feedBackSend {
    margin-top: 10px;
    text-align: center;
  }
}

.feedback-object {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.feedback-result {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    line-height: 24px;
    font-size: 18px;
    color: #303133;
  }

  .placeholder {
    width: 100px;
  }
}

.business-type {
  display: flex;
  align-items: center;
}

.feedback-info {
  width: 100%;
  height: auto;
  padding: 20px 180px 20px 0px;
  margin-top: 20px;
  box-sizing: border-box;

  &_item {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .prob {
      font-size: 14px;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      display: flex;
      align-items: center;
      width: 50%;
    }

    .answer {
      font-size: 14px;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
  }
}

.work-order-id {
  text-align: right;
  line-height: 14px;
  font-size: 14px;
}

.work-order-box {
  flex: 1;
  display: flex;
  align-items: center;
  padding-bottom: 2px;

  .compile-input {
    flex: 1;
  }

  .compile-icon {
    margin-left: 10px;
    cursor: pointer;
  }
}
</style>
