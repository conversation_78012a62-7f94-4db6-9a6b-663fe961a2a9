/** *私信对话查询 */
<template>
  <div class="private-message-query">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="主用户ID">
        <el-input
          v-model="ruleForm.uid"
          style="width: 200px"
          clearable
          placeholder="请输入主用户ID"
        />
      </el-form-item>
      <el-form-item label="对话用户ID">
        <el-input
          v-model="ruleForm.toUid"
          style="width: 210px"
          clearable
          placeholder="请输入对话用户ID（选填）"
        />
      </el-form-item>
      <el-form-item label>
        <el-button type="primary" @click="search(false)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content">
      <!-- 用户及操作栏 -->
      <div class="master-user mb15">
        <el-row>
          <el-col class="mt10" :span="5">
            <span>主用户</span>
            <span class="user-name">
              <el-tag v-if="userInfo.shopUser === 1">商家</el-tag>
              {{ userInfo.username }}
            </span>
            <span class="color-uid">{{ userInfo.uid }}</span>
          </el-col>
          <el-col :span="10">
            <div v-if="detailsList && detailsList.length" class="preivew-btn">
              <el-button @click="showPreview = false" v-if="showPreview"
                >x 关闭预览</el-button
              >
              <el-button @click="showView" v-else>对话预览</el-button>
            </div>
          </el-col>
          <el-col :span="7">
            <div v-if="showPreview" class="preview-role ml30">
              <div>{{ switchRole ? '对话用户视图' : '主用户视图' }}</div>
              <el-button @click="switchRole = !switchRole">切换角色</el-button>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-row>
        <!-- 对话用户 -->
        <el-col :span="5">
          <div>
            <el-table
              ref="userTable"
              :data="userList"
              border
              height="73vh"
              style="width: 100%"
              highlight-current-row
              @row-click="rowClick"
            >
              <el-table-column :label="`对话用户（${total}）`" align="center">
                <template v-slot="scope">
                  <div class="dialogue-user">
                    <el-tag v-if="scope.row.shopUser === 1">商家</el-tag>
                    {{ `${scope.row.toUserName}（${scope.row.toUid}）` }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              v-model:current-page="page"
              :page-size="20"
              :pager-count="5"
              :total="total"
              layout="prev, pager, next"
              class="el-pagination-center"
              @current-change="currentChange"
            />
          </div>
        </el-col>
        <!-- 历史对话 -->
        <el-col :span="10">
          <div class="history">
            <div class="history-top">
              历史对话（共包含{{ logTotal }}条会话）
            </div>
            <div v-if="detailsList && detailsList.length">
              <!-- <div class="message-item" v-infinite-scroll="load" infinite-scroll-immediate="false"> -->
              <div class="message-item" id="chat-frame" @scroll="scroll">
                <p v-if="flag" class="ending">加载中...</p>
                <p v-if="noMore" class="ending">没有更多了</p>
                <div v-for="(item, index) in detailsList" :key="index">
                  <div class="time">{{ item.ymd }}</div>
                  <div
                    v-for="(log, idx) in item.list"
                    :key="idx"
                    :class="[
                      'message',
                      log.authorid === userInfo.uid ? 'mine' : 'adverse'
                    ]"
                  >
                    <div class="userinfo">
                      <span>{{
                        log.authorid === userInfo.uid
                          ? userInfo.username
                          : currentPlid.toUserName
                      }}</span>
                      <span>{{ log.hms }}</span>
                    </div>
                    <div class="messageblob flex">
                      <!-- <el-popover
                        placement="top-start"
                        title="标题"
                        width="200"
                        trigger="hover"
                        content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。"
                      >
                        <el-button slot="reference">hover 激活</el-button>
                        <p slot="reference" v-if="!log.isImg && !log.isData" class="text" v-html="log.message"></p>
                      </el-popover> -->
                      <auditDom
                        :log="log"
                        v-if="log.authorid === userInfo.uid"
                      />
                      <RTip
                        v-if="!log.isImg && !log.isVideo && !log.isData"
                        :log="log"
                        :sensitiveWord="log.sensitiveWord"
                      />
                      <p v-if="log.isData" class="text">
                        卡片：二手车ID:{{ log.message.sourceId }}
                      </p>
                      <img
                        v-if="log.isImg"
                        :src="log.message"
                        class="img"
                        @click="seeBigImg(log.message)"
                      />
                      <div
                        v-if="log.isVideo"
                        class="flex-center"
                        @click.stop="seeVideoPlay(log.message)"
                      >
                        <img
                          class="cover"
                          :src="
                            getVideoInfo(log.message).cover?.replace(
                              '!forum',
                              '!forum300'
                            )
                          "
                          alt=""
                        />
                        <el-icon
                          style="position: absolute; color: #fff"
                          :size="40"
                          ><VideoPlay
                        /></el-icon>
                      </div>
                      <auditDom
                        :log="log"
                        v-if="log.authorid !== userInfo.uid"
                      />
                    </div>
                    <p v-if="log.secondHandCarFraudWarn" class="text">
                      卡片：二手车防诈骗卡片（仅买家可见）
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="no-data">暂无数据</div>
          </div>
        </el-col>
        <!-- 视图 -->
        <el-col :span="7">
          <div v-if="showPreview" class="preview">
            <div class="preview-top">
              <i class="el-icon-arrow-left"></i
              >{{ !switchRole ? currentPlid.toUserName : userInfo.username }}
            </div>
            <div v-if="detailsList2 && detailsList2.length">
              <!-- <div class="message-item" v-infinite-scroll="load" infinite-scroll-immediate="false"> -->
              <div
                class="message-item"
                id="chat-frame1"
                @scroll="preivewScroll"
              >
                <p v-if="flag" class="ending">加载中...</p>
                <p v-if="noMore" class="ending">没有更多了</p>
                <div v-for="(item, index) in detailsList2" :key="index">
                  <div class="time">{{ item.ymd }}</div>
                  <template v-for="(log, idx) in item.list" :key="idx">
                    <div
                      :style="{ marginBottom: log.activeTip ? '10px' : 0 }"
                      :class="[
                        'message',
                        log.authorid === userInfo.uid
                          ? !switchRole
                            ? 'mine'
                            : 'adverse'
                          : switchRole
                          ? 'mine'
                          : 'adverse'
                      ]"
                    >
                      <div
                        class="messageblob"
                        :class="{
                          'messageblob-no-flex': log.auditStatus === 2
                        }"
                      >
                        <div
                          class="messageblob-left"
                          :style="{
                            order:
                              log.authorid === userInfo.uid
                                ? !switchRole
                                  ? 1
                                  : 0
                                : switchRole
                                ? 1
                                : 0
                          }"
                        >
                          <img
                            v-if="log.auditStatus !== 2"
                            :src="
                              log.authorid === userInfo.uid
                                ? userInfo.avatar
                                : currentPlid.avatar
                            "
                            alt=""
                          />
                        </div>
                        <div
                          class="messageblob-right"
                          :style="{
                            order:
                              log.authorid === userInfo.uid
                                ? !switchRole
                                  ? 0
                                  : 1
                                : switchRole
                                ? 0
                                : 1
                          }"
                        >
                          <p
                            :class="[
                              log.authorid === userInfo.uid
                                ? !switchRole
                                  ? 'isMain'
                                  : 'isOther'
                                : switchRole
                                ? 'isMain'
                                : 'isOther'
                            ]"
                            v-if="
                              !log.isImg &&
                              !log.isVideo &&
                              !log.isData &&
                              log.auditStatus !== 2
                            "
                            class="text"
                            v-html="log.message"
                          ></p>

                          <p
                            :class="[
                              log.authorid === userInfo.uid
                                ? !switchRole
                                  ? 'isMain'
                                  : 'isOther'
                                : switchRole
                                ? 'isMain'
                                : 'isOther'
                            ]"
                            v-if="log.isData"
                            class="text"
                          >
                            卡片：二手车ID:{{ log.message.sourceId }}
                          </p>
                          <img
                            v-if="log.isImg && log.auditStatus !== 2"
                            :src="log.message"
                            class="img"
                            @click="seeBigImg(log.message)"
                          />
                          <div
                            v-if="log.isVideo && log.auditStatus !== 2"
                            class="flex-center"
                            @click.stop="seeVideoPlay(log.message)"
                          >
                            <img
                              class="cover"
                              :src="
                                getVideoInfo(log.message).cover?.replace(
                                  '!forum',
                                  '!forum300'
                                )
                              "
                              alt=""
                            />
                            <el-icon
                              style="position: absolute; color: #fff"
                              :size="40"
                              ><VideoPlay
                            /></el-icon>
                          </div>
                          <div
                            v-if="[0, 2].includes(log.auditStatus)"
                            style="color: #999"
                          >
                            <p
                              v-if="log.auditStatus === 0 && log.isImg"
                              class="text-center"
                            >
                              {{
                                log.authorid === userInfo.uid
                                  ? !switchRole
                                    ? errTipList[0]
                                    : ''
                                  : switchRole
                                  ? errTipList[0]
                                  : ''
                              }}
                            </p>
                            <div
                              v-else-if="log.auditStatus === 2"
                              class="text-center messageblob-right-err-tip"
                            >
                              <template
                                v-if="
                                  (log.authorid === userInfo.uid &&
                                    !switchRole) ||
                                  (log.authorid !== userInfo.uid && switchRole)
                                "
                                >{{ errTipList[1] }}</template
                              >

                              <p v-else>{{ errTipList[2] }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      :style="{ marginBottom: log.activeTip ? '10px' : 0 }"
                      :class="[
                        'message',
                        log.authorid === userInfo.uid
                          ? !switchRole
                            ? 'mine'
                            : 'adverse'
                          : switchRole
                          ? 'mine'
                          : 'adverse'
                      ]"
                    >
                      <div
                        class="messageblob"
                        :class="{
                          'messageblob-no-flex': log.auditStatus === 2
                        }"
                      >
                        <div
                          class="messageblob-right"
                          :style="{
                            order:
                              log.authorid === userInfo.uid
                                ? !switchRole
                                  ? 0
                                  : 1
                                : switchRole
                                ? 0
                                : 1
                          }"
                        >
                          <p
                            :class="[
                              log.authorid === userInfo.uid
                                ? !switchRole
                                  ? 'isMain'
                                  : 'isOther'
                                : switchRole
                                ? 'isMain'
                                : 'isOther'
                            ]"
                            v-if="log.secondHandCarFraudWarn"
                            class="text"
                          >
                            卡片：二手车防诈骗卡片（仅买家可见）
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      :key="idx + 'str'"
                      class="tip-box"
                      v-if="log.activeTip"
                    >
                      <span
                        v-html="
                          getTip(log.activeTip, log.authorid === userInfo.uid)
                        "
                      ></span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div v-else class="no-data">暂无数据</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <ChooseShowImage ref="showImage" />
  </div>
</template>

<script>
import { VideoPlay } from '@element-plus/icons-vue'
import {
  privateMessagesList,
  privateMessagesDetails,
  selectedUser
} from '@/api/user'
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import RTip from './tip.vue'
import auditDom from './auditDom.vue'
export default {
  name: 'PrivateMessageQuery',
  components: {
    ChooseShowImage,
    RTip,
    auditDom,
    VideoPlay
  },
  data() {
    return {
      ruleForm: {
        uid: '', // 主用户ID
        toUid: '' // 对话用户ID
      },
      switchRole: false,
      showPreview: false,
      page: 1,
      total: 0,
      logPage: 1,
      logPage2: 1,
      logTotal: 0,
      logTotal2: 0,
      userList: [], // 对话用户列表
      currentPlid: {}, // 当前的对话
      detailsList: [], // 历史对话列表
      detailsList2: [], // 历史对话列表
      userInfo: {},
      flag: false,
      noMore: false,
      flag2: false,
      noMore2: false,
      onBottom: true,
      onBottom2: true,

      bottomChangeText: {
        loading: '正在加载更多...',
        nodata: '暂无更多数据',
        error: '请求数据出错，请点击重试'
      },
      dataList: [1, 2, 3, 4, 5], // 模拟数据
      errTipList: [
        '您的图片可能涉嫌违规，审核通过后对方可见',
        '您的消息违规审核不通过，如有疑问请联系在线客服',
        '对方消息违规，已被系统删除'
      ]
    }
  },
  activated() {
    const query = this.$route.query
    if (query.uid) {
      this.ruleForm.uid = query.uid
      if (query.toUid) {
        this.ruleForm.toUid = query.toUid
      }
      this.search(!!query.isSelected)
    }
  },
  mounted() {
    this.keyDown()
  },
  methods: {
    getTip(tip = {}, current) {
      if (current) {
        if (this.switchRole) {
          return tip.toPromptContent
        } else {
          return tip.fromPromptContent
        }
      } else {
        if (this.switchRole) {
          return tip.fromPromptContent
        } else {
          return tip.toPromptContent
        }
      }
    },
    showView() {
      this.showPreview = true
      this.getDetails2()
    },
    getVideoInfo(video) {
      return JSON.parse(video.replace(/\[|\/video]|video]/g, '') || '{}')
    },
    seeVideoPlay(video) {
      return window.open(this.getVideoInfo(video)?.videoUrl)
    },
    // 监听键盘
    keyDown() {
      document.onkeydown = (e) => {
        //事件对象兼容
        let e1 =
          e || event || window.event || arguments.callee.caller.arguments[0]
        //键盘按键判断:左箭头-37;上箭头-38；右箭头-39;下箭头-40
        //左
        if (e1 && e1.keyCode == 38) {
          // 按下左箭头
          this.setNextCurrentPlid(0)
          e.preventDefault()
        } else if (e1 && e1.keyCode == 40) {
          this.setNextCurrentPlid(1)
          e.preventDefault()
          // 按下右箭头
        }
      }
    },
    setNextCurrentPlid(direct) {
      const len = this.userList.length
      const index = this.userList.findIndex(
        (item) => item.plid === this.currentPlid.plid
      )
      if (direct === 1 && index + 2 > len) return
      if (direct === 0 && index - 1 < 0) return
      this.showPreview = false
      const row = this.userList[direct ? index + 1 : index - 1]
      this.$refs['userTable'].setCurrentRow(row)
      this.currentPlid = row
      // this.$tools.debounce(() => this.refresh(), 1000)()
      this.refresh()
    },
    search(status) {
      if (!this.ruleForm.uid) {
        return this.$message('请输入主用户ID')
      }
      this.showPreview = false
      this.switchRole = false
      this.page = 1
      this.total = 0
      this.userList = []
      this.userInfo = {}
      this.currentPlid = {}
      this.refresh(false)
      this.getUserInfo()
      this.getList(status)
    },
    resetForm() {
      this.ruleForm = {
        uid: '',
        toUid: ''
      }
      this.page = 1
      this.total = 0
      this.logPage = 1
      this.logTotal = 0
      this.userList = []
      this.currentPlid = {}
      this.detailsList = []
      this.userInfo = {}
      this.flag = false
      this.noMore = false
      this.showPreview = false
      this.logPage2 = 1
      this.logTotal2 = 0
      this.detailsList2 = []
      this.flag2 = false
      this.noMore2 = false
      this.switchRole = false
    },
    getUserInfo() {
      selectedUser({
        uid: this.ruleForm.uid,
        page: 1,
        limit: 10
      }).then((res) => {
        if (res.data.code === 0) {
          this.userInfo = res.data.data.list[0] || {}
        }
      })
    },
    currentChange(page) {
      this.page = page
      this.getList()
    },
    getList(status) {
      const me = this
      privateMessagesList({
        ...me.ruleForm,
        page: me.page,
        limit: 20
      }).then((res) => {
        if (res.data.code === 0) {
          me.userList = res.data.data.listData || []
          me.total = res.data.data.total || 0
          if (status && me.ruleForm.toUid) {
            const findData =
              me.userList.find(
                (item) => item.toUid === Number(me.ruleForm.toUid)
              ) || {}
            me.rowClick(findData)
          }
        }
      })
    },
    rowClick(e) {
      if (this.currentPlid.plid === e.plid) return
      this.currentPlid = e
      this.showPreview = false
      this.refresh()
    },
    refresh(flag = true) {
      this.logPage = 1
      this.logTotal = 0
      this.detailsList = []
      this.flag = false
      this.noMore = false
      this.onBottom = true
      this.logPage2 = 1
      this.logTotal2 = 0
      this.detailsList2 = []
      this.flag2 = false
      this.noMore2 = false
      this.onBottom2 = true
      this.switchRole = false
      flag && this.getDetails()
    },
    scroll() {
      const chatBox = document.getElementById('chat-frame')
      if (chatBox.scrollTop === 0) {
        this.getDetails()
      }
    },
    preivewScroll() {
      const chatBox = document.getElementById('chat-frame1')
      if (chatBox.scrollTop === 0) {
        this.getDetails2()
      }
    },
    getDetails2() {
      if (this.flag2 || this.noMore2) return
      this.flag2 = true
      privateMessagesDetails({
        plid: this.currentPlid.plid,
        page: this.logPage2,
        limit: 20
      })
        .then((res) => {
          this.flag2 = false
          if (res.data.code === 0) {
            this.logPage2++
            if (res.data.data.listData.length < 20) {
              this.noMore2 = true
            }
            const data = res.data.data.listData || []
            const sensitiveWord = res.data.data.sensitiveMessageVOS || []
            this.handleData(data, sensitiveWord, 'detailsList2')
            this.logTotal2 = res.data.data.total || 0
            if (this.onBottom2) {
              this.onBottom2 = false
              this.$nextTick(() => {
                setTimeout(() => {
                  const chatBox1 = document.getElementById('chat-frame1')
                  if (chatBox1) {
                    chatBox1.scrollTop = chatBox1.scrollHeight
                  }
                }, 100)
              })
            }
          }
        })
        .catch(() => {
          this.flag2 = false
        })
        .finally(() => {
          this.flag2 = false
        })
    },
    getDetails() {
      if (this.flag || this.noMore) return
      this.flag = true
      privateMessagesDetails({
        plid: this.currentPlid.plid,
        page: this.logPage,
        limit: 20
      })
        .then((res) => {
          this.flag = false
          if (res.data.code === 0) {
            this.logPage++
            if (res.data.data.listData.length < 20) {
              this.noMore = true
            }
            const data = res.data.data.listData || []
            const sensitiveWord = res.data.data.sensitiveMessageVOS || []
            this.handleData(data, sensitiveWord, 'detailsList')
            this.logTotal = res.data.data.total || 0
            if (this.onBottom) {
              this.onBottom = false
              this.$nextTick(() => {
                setTimeout(() => {
                  const chatBox = document.getElementById('chat-frame')
                  if (chatBox) {
                    chatBox.scrollTop = chatBox.scrollHeight
                  }
                }, 100)
              })
            }
          }
        })
        .catch(() => {
          this.flag = false
        })
        .finally(() => {
          this.flag = false
        })
    },
    handleData(data, sensitiveWord, key) {
      data.map((_) => {
        _.sensitiveWord = sensitiveWord
        const time = this.$date.format(_.dateline * 1000)
        const ymd = time.split(' ')[0] || ''
        const hms = time.split(' ')[1] || ''
        _.hms = hms
        // 图片处理
        _.isImg = _.message.indexOf('[img]') > -1
        if (_.isImg) {
          _.message = _.message.replace(/\[|\/img]|img]/g, '')
        }
        // 视频处理
        _.isVideo = _.message.indexOf('[video]') > -1
        if (_.isImg) {
          _.message = _.message.replace(/\[|\/video]|video]/g, '')
        }
        // 卡片处理
        _.isData = _.message.indexOf('[data]') > -1
        if (_.isData) {
          _.message = JSON.parse(_.message.replace(/\[|\/data]|data]/g, ''))
        }
        let flag = false
        sensitiveWord.forEach((item) => {
          const index =
            !_.isImg && !_.isVideo && !_.isData
              ? _.message.indexOf(item.sensitiveStr)
              : -1
          if (index > -1) {
            if (!flag) {
              _.activeTip = { ...item }
              const arr = ['blue', 'red']
              arr.forEach((tag) => {
                _.activeTip.fromPromptContent =
                  _.activeTip.fromPromptContent?.replaceAll(
                    `<${tag}>`,
                    `<span style="color: ${tag}">`
                  )
                _.activeTip.fromPromptContent =
                  _.activeTip.fromPromptContent?.replaceAll(
                    `</${tag}>`,
                    '</span>'
                  )
                _.activeTip.toPromptContent =
                  _.activeTip.toPromptContent?.replaceAll(
                    `<${tag}>`,
                    `<span style="color: ${tag}">`
                  )
                _.activeTip.toPromptContent =
                  _.activeTip.toPromptContent?.replaceAll(
                    `</${tag}>`,
                    '</span>'
                  )
              })
              flag = true
            }
          }
        })
        const index = this[key].findIndex((_) => _.ymd === ymd)
        if (index === -1) {
          this[key].unshift({
            ymd,
            sensitiveWord,
            list: [_]
          })
        } else {
          this[key][index].list.unshift(_)
        }
      })
    },
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(link)
    }
  }
}
</script>

<style lang="scss" scoped>
.private-message-query {
  padding: 15px;
  // 用户及操作栏
  .content {
    .dialogue-user {
      text-align: left;
    }
    .master-user {
      // height: 46px;
      .user-name {
        margin: 0 5px 0 10px;
      }
      .color-uid {
        color: #409eff;
      }
    }
    .history {
      margin-left: 30px;
      height: 73vh;
      border: 1px solid #ebeef5;
      .history-top {
        height: 44px;
        line-height: 44px;
        text-align: center;
        border-bottom: 1px solid #ebeef5;
        font-weight: bold;
        color: #333;
        font-size: 14px;
      }
      .message-item {
        padding: 10px 15px;
        font-size: 14px;
        overflow-y: scroll;
        height: calc(73vh - 44px);
        &::-webkit-scrollbar {
          width: 3px;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #e0e0e0;
          border-radius: 3px;
        }
        .time {
          text-align: center;
          margin-top: 10px;
        }
        .message {
          position: relative;
          .userinfo {
            font-size: 12px;
            color: #02a7f0;
            > span:nth-child(2) {
              color: #999999;
              margin-left: 5px;
            }
          }
          .messageblob {
            margin-top: 5px;
            line-height: 18px;
            justify-content: flex-start;
            p {
              margin: 0;
            }
            .img {
              max-width: 12vw;
              max-height: 12vw;
            }
          }
        }
        .message:hover {
          background-color: #e4f6fd;
          border-radius: 6px;
          border: 1px solid #009dea;
        }
        .adverse {
          padding: 5px 20% 5px 0;
        }
        .mine {
          text-align: right;
          padding: 5px 5px 5px 20%;
          .messageblob {
            justify-content: flex-end;
          }

          .userinfo {
            color: #f59a23;
          }
        }
        .ending {
          text-align: center;
          color: #999999;
        }
      }
      .no-data {
        height: calc(73vh - 44px);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  .preivew-btn {
    display: flex;
    justify-content: flex-end;
  }
  .preview-role {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .container {
    height: 73vh;
    overflow: hidden;
    background: #f7f7f7;
  }
  .item {
    height: 150px;
    margin-bottom: 20px;
    background: #fff;
    overflow: hidden;
  }
  /* 注意：伪类选择最后一项是nth-last-of-type(2)不是(1) */
  .item:nth-last-of-type(2) {
    margin-bottom: 0;
  }
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
    .cover {
      width: 100px;
      height: 100px;
      object-fit: cover;
    }
  }
  .preview {
    margin-left: 30px;
    height: 73vh;
    border: 1px solid #ebeef5;
    background: #f7f7f7;
    .history-top {
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-bottom: 1px solid #ebeef5;
      font-weight: bold;
      color: #333;
      font-size: 14px;
    }
    .message-item {
      padding: 10px 15px;
      font-size: 14px;
      overflow-y: scroll;
      height: calc(73vh - 44px);
      &::-webkit-scrollbar {
        width: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #e0e0e0;
        border-radius: 3px;
      }
      .time {
        text-align: center;
        margin-top: 10px;
      }
      .message {
        margin-top: 10px;
        display: flex;
        position: relative;
        .userinfo {
          font-size: 12px;
          > span:nth-child(2) {
            color: #999999;
            margin-left: 5px;
          }
        }
        .messageblob {
          display: flex;
          margin-top: 5px;
          line-height: 18px;
          /* display: flex; */
          .messageblob-left {
            img {
              width: 30px;
              height: 30px;
              border-radius: 50%;
            }
          }
          .messageblob-right {
            padding: 0 10px;
            p {
              margin: 0;
              padding: 5px;
              border-radius: 5px;
            }
            .img {
              max-width: 12vw;
              max-height: 12vw;
            }
            .messageblob-right-err-tip {
              width: 100%;
              position: absolute;
              left: 0;
              right: 0;
            }
          }
          .isMain {
            background: #6b9eef;
            color: white;
          }
          .isOther {
            background: white;
            color: black;
          }
        }
        .messageblob-no-flex {
          display: block;
          margin-bottom: 20px;
        }
      }
      .adverse {
        padding-right: 20%;
      }
      .mine {
        justify-content: flex-end;
        padding-left: 20%;
      }
      .ending {
        text-align: center;
        color: #999999;
      }
    }
    .no-data {
      height: calc(73vh - 44px);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #909399;
      font-size: 14px;
    }
    .preview-top {
      height: 30px;
      display: flex;
      background: white;
      align-items: center;
    }
    .tip-box {
      width: 100%;
      text-align: center;
      > span {
        color: #999999;
        font-size: 12px;
      }
    }
  }
}
</style>
