/** *私信查询 */
<template>
  <div
    class="private-letter-search"
    v-loading="loading"
    style="margin: 20px 20px 0"
  >
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="用户ID">
        <el-input
          v-model="ruleForm.uid"
          style="width: 170px"
          clearable
          placeholder="请输入用户ID"
        />
      </el-form-item>
      <el-form-item label="发送人用户名">
        <el-autocomplete
          v-model="searchNameValue"
          :fetch-suggestions="querySearchAsyncUser"
          :trigger-on-focus="false"
          placeholder="请输入发送人用户名"
          style="width: 300px"
          clearable
          @clear="clearUserName()"
          @select="handleSelectUser"
        />
      </el-form-item>
      <el-form-item label="私信内容">
        <el-input
          v-model="ruleForm.message"
          style="width: 170px"
          clearable
          placeholder="请输入私信内容"
        />
      </el-form-item>
      <el-form-item label>
        <el-button type="primary" @click="search(1)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="dataList"
      :data="dataList"
      row-key="dataList"
      border
      style="width: 100%"
      @row-dblclick="skipUserAccount"
    >
      <el-table-column prop="userId" label="发送人用户ID" align="center" />
      <el-table-column prop="userName" label="发送人用户名" align="center">
        <template v-slot="scope">
          <el-tag v-if="scope.row.shopUser === 1">商家</el-tag>
          {{ scope.row.userName }}
        </template>
      </el-table-column>
      <el-table-column prop="groupName" label="状态" align="center" />
      <el-table-column prop="content" label="私信内容" align="center">
        <template v-slot="scope">
          <div>
            <span v-if="scope.row.isText">
              {{ scope.row.content }}
            </span>
            <img
              v-else-if="
                scope.row.isImg &&
                scope.row.content &&
                scope.row.content.indexOf('http') > -1
              "
              class="cover"
              :src="scope.row.content.replace('!forum', '!forum300')"
              alt=""
              @click.stop="seeImg(scope.row.content)"
            />
            <div
              v-else-if="scope.row.isVideo && scope.row.content"
              class="flex-center"
              @click.stop="seeVideoPlay(scope.row.content)"
            >
              <img
                class="cover"
                :src="scope.row.content.cover.replace('!forum', '!forum300')"
                alt=""
              />
              <el-icon style="position: absolute; color: #fff" :size="40"
                ><VideoPlay
              /></el-icon>
            </div>
            <div v-if="[0, 2].includes(scope.row.auditStatus)" class="fl-right">
              <!-- 审核中 -->
              <el-popover
                v-if="scope.row.auditStatus === 0"
                v-model="popoverStatus"
                placement="bottom"
                width="100"
                trigger="click"
              >
                <template #reference>
                  <img
                    style="width: 28px"
                    src="/static/img/private-message-audit.png"
                  />
                </template>
                <template #default>
                  <p>审核中: {{ scope.row.auditReason }}</p>
                </template>
              </el-popover>
              <!-- 审核不通过 -->
              <el-popover
                v-else
                v-model="popoverStatus"
                :width="400"
                placement="bottom"
                trigger="click"
              >
                <template #default>
                  <p>
                    审核不通过：{{ scope.row.auditReason }}<br />
                    发送方提示：您的消息违规审核不通过，如有疑问请联系在线客服<br />
                    接收方提示：对方消息违规，已被系统删除
                  </p>
                </template>
                <template #reference>
                  <img
                    style="width: 28px"
                    src="/static/img/private-message-warning.png"
                  />
                </template>
              </el-popover>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="发送时间" align="center">
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.createTime * 1000)
        }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="scope">
          <el-button
            type="primary"
            link
            @click="toPrivateMessageQuery(scope.row)"
            >查看对话</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="50"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @current-change="currentChange"
    />
    <choose-show-image ref="showImage" />
  </div>
</template>

<script>
import { VideoPlay } from '@element-plus/icons-vue'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import { GetMessageLastMessageList, selectedUser } from '@/api/user'
export default {
  name: 'PrivateLetterSearch',
  components: {
    ChooseShowImage,
    VideoPlay
  },
  data() {
    return {
      searchNameValue: '', // 搜索用户名关键字
      page: 1,
      total: 0,
      loading: false,
      popoverStatus: true,
      ruleForm: {
        uid: '',
        limit: 50,
        message: ''
      },
      dataList: []
    }
  },
  created() {},
  activated() {
    const seData = JSON.parse(
      sessionStorage.getItem('PrivateLetterSearch') || '{}'
    )
    this.ruleForm.uid = this.$route.query.id || seData.uid || ''
    this.search()
  },
  methods: {
    // 查询列表
    search(page) {
      const me = this
      me.loading = true
      const postData = {
        page: page || me.page,
        ...me.ruleForm
      }
      sessionStorage.setItem('PrivateLetterSearch', JSON.stringify(postData))

      me.page = page || me.page
      GetMessageLastMessageList(postData)
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data
            me.dataList = data.list
            me.dataList.map((_) => {
              if (_.content.indexOf('[img]') > -1) {
                _.isText = false
                _.isImg = true
                _.isVideo = false
                _.content = _.content.replace(/\[|\/img]|img]/g, '')
              } else if (_.content.indexOf('[video]') > -1) {
                _.isText = false
                _.isImg = false
                _.isVideo = true
                _.content = JSON.parse(
                  _.content.replace(/\[|\/video]|video]/g, '') || '{}'
                )
              } else {
                _.isText = true
                _.isVideo = false
                _.isImg = false
              }
            })
            me.total = data.total
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          me.loading = false
        })
    },
    resetForm() {
      this.page = 1
      this.searchNameValue = ''
      this.ruleForm = Object.assign(this.ruleForm, {
        uid: '',
        limit: 50,
        message: ''
      })
      this.$nextTick(() => {
        this.search(1)
      })
    },
    // 变更页签
    currentChange(page) {
      this.page = page
      this.search(this.page)
    },
    // 模糊查询用户名
    querySearchAsyncUser(queryString, cb) {
      const me = this
      this.$tools.debounce(searchAsyncAuther, 300)()
      function searchAsyncAuther() {
        me.ruleForm.uid = ''
        selectedUser({
          username: me.searchNameValue,
          page: 1,
          limit: 20
        }).then((response) => {
          if (response.data.code === 0) {
            const userNameList = []
            const result = response.data.data && response.data.data.list
            result.map(function (value) {
              const newObj = {
                value: value.username,
                uid: value.uid
              }
              userNameList.push(newObj)
            })
            cb(userNameList)
          }
        })
      }
    },
    // 选择用户名
    handleSelectUser(item) {
      this.ruleForm.uid = item.uid
    },
    clearUserName() {
      this.ruleForm.uid = ''
      this.searchNameValue = ''
    },
    // 查看大图
    seeImg(link) {
      this.$refs.showImage.init(link)
    },
    // 查看视频
    seeVideoPlay(video = {}) {
      window.open(video.videoUrl)
    },
    // 跳转用户账号相关
    skipUserAccount(item) {
      const me = this
      me.$router.push({
        name: 'UserAccountCorrelation',
        query: {
          username: item.userName,
          uid: item.userId
        }
      })
    },
    toPrivateMessageQuery(item) {
      const me = this
      me.$router.push({
        name: 'PrivateMessageQuery',
        query: {
          uid: item.userId,
          toUid: item.toUid,
          isSelected: true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.private-letter-search {
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .cover {
    width: 100px;
    height: 100px;
    object-fit: cover;
  }
}
</style>
