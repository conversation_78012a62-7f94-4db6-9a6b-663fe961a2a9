/** * 新举报管理 */
<template>
  <div v-loading="loading" class="act-essay-list" style="padding: 20px">
    <el-form ref="form" :model="form" :inline="true" label-width="70px">
      <SeletedUser
        ref="selectUserd"
        :name="'被举报人昵称'"
        label-width="100"
        :placeholder="'请输入被举报人昵称'"
        @sendData="setReportedUid"
      />
      <el-form-item label="被举报人ID" label-width="100px">
        <el-input v-model="form.reportedUid" style="width: 150px" />
      </el-form-item>
      <SeletedUser
        ref="selectUser"
        :name="'举报人昵称'"
        label-width="100"
        :placeholder="'请输入举报人昵称'"
        @sendData="setAuthorIdUid"
      />
      <el-form-item label="举报人ID" label-width="100px">
        <el-input v-model="form.authorId" style="width: 150px" />
      </el-form-item>
      <!-- <el-form-item v-if="isReport" label="平台">
        <el-select v-model="form.packageType">
          <el-option v-for="(value, index) in packageType" :key="index" :label="index" :value="value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="举报时间">
        <el-date-picker
          v-model="daterange"
          style="width: 400px"
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="举报类型">
        <!-- <el-select v-model="form.ossQueryType">
          <el-option
            v-for="(value, index) in typeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select> -->
        <el-cascader
          :options="reportTypeList"
          v-model="form.reportReasonType"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="内容ID">
        <el-input v-model="form.originalId" style="width: 125px" />
      </el-form-item>
      <el-form-item label="处理状态">
        <el-select v-model="form.status">
          <el-option
            v-for="(value, index) in treatmentType"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="
            () => {
              page = 1
              getList()
            }
          "
          >查询</el-button
        >
        <el-button @click="reset()">重置</el-button>
      </el-form-item>
    </el-form>
    <div id="table" style="height: 68vh; overflow: auto">
      <div v-for="(item, index) in dataList" :key="index">
        <div class="data-border">
          <div class="content-data">
            <div class="data-flex-column" style="flex: 1">
              <h3>被举报人信息</h3>
              <div class="data-flex-row">
                <el-avatar :src="item.reportedAvatar" />
                <div class="data-flex-column" style="margin-left: 10px">
                  <span
                    ><el-popover placement="bottom" width="100" trigger="hover">
                      <div>
                        <div class="text-center">
                          <el-button @click="goToDetail('essay_detail', item)"
                            >内容列表</el-button
                          >
                        </div>
                        <div class="text-center">
                          <el-button @click="goToDetail('chat_detail', item)"
                            >私信列表</el-button
                          >
                        </div>
                        <div class="text-center">
                          <el-button @click="goToDetail('essaypid', item)"
                            >评论列表</el-button
                          >
                        </div>
                      </div>
                      <template #reference>
                        <span style="cursor: pointer; color: #409eff">{{
                          item.reportedUsername
                        }}</span>
                      </template>
                    </el-popover>
                    &nbsp;<el-button size="small" @click.stop="historical(item)"
                      ><i
                        class="el-icon-document"
                      />&nbsp;历史处理记录</el-button
                    ></span
                  >
                  <span
                    >ID: {{ item.reportedUid }}
                    <el-button
                      type="primary"
                      link
                      class="el-icon-document-copy"
                      v-clipboard:copy="item.reportedUid"
                      v-clipboard:success="clipboardSuccess"
                    />
                  </span>
                </div>
              </div>
              <div class="data-grow">
                <el-tag
                  style="margin: 5px"
                  v-for="tag in item.certifyVoList"
                  :key="tag.tagName"
                  type="info"
                  >{{ tag.certifyName }}
                </el-tag>
                <el-tag v-if="item.ifShopUser" type="info">{{
                  '商家成员'
                }}</el-tag>
              </div>
            </div>
            <div class="data-flex-column" style="flex: 1">
              <h3>举报内容</h3>
              <div>
                类型：
                {{
                  reportTypeEnum[item.idType]
                    ? reportTypeEnum[item.idType]['name']
                    : '其他'
                }}
                &nbsp;<el-button
                  v-if="isPreView(item.idType)"
                  type="primary"
                  link
                  size="small"
                  @click="showH5Preview(item)"
                  ><i class="el-icon-view" />&nbsp;预览</el-button
                >
              </div>
              <div>
                {{ contentIdEnum[item.idType] || '内容' }}ID：{{
                  item.relatedId
                }}
                <el-button
                  type="primary"
                  link
                  class="el-icon-document-copy"
                  v-clipboard:copy="item.relatedId"
                  v-clipboard:success="clipboardSuccess"
                />
                <div v-if="item.businessDataConfigFlag === 1" class="tag-box">
                  已被系统降曝光
                </div>
              </div>
              <div>
                当前{{ contentIdEnum[item.idType] || '内容' }}状态：{{
                  reportTypeEnum[item.idType]
                    ? reportTypeEnum[item.idType]['status'][item.relatedStatus]
                    : ''
                }}
              </div>
            </div>
            <div class="data-flex-column" style="flex: 1">
              <h3>用户历史举报情况</h3>
              <div>用户被举报次数：{{ item.userReportCnt }}</div>
              <div>内容被举报次数：{{ item.contentReportCnt }}</div>
              <div>私信被举报次数：{{ item.messageReportCnt }}</div>
            </div>
          </div>
          <div class="data-flex-row">
            <span class="line"></span>
            <p
              style="text-align: center"
              @click="getTableListData(item, index)"
            >
              未处理（<span style="color: red">{{ item.count }}</span
              >）
              <el-icon :id="'arrow' + index">
                <ArrowDown />
              </el-icon>
            </p>
            <span class="line"></span>
          </div>
          <tableList
            v-if="item.tableOpen"
            :ref="`tableList${index}`"
            :item="item"
          />
        </div>
      </div>
    </div>

    <ChooseIframe ref="ChooseIframe" />
    <CommentDialog ref="CommentDialog" />
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="text-align: center; margin-top: 10px"
      @current-change="currentChange"
    />
    <history ref="history" />
  </div>
</template>

<script>
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
import { convertKeyValueEnum } from '@/utils/convert'
import {
  getReportSummary
  // GetCurrentLimitRec
} from '@/api/user'
import { recordOldData } from '@/utils/enum/logData'
import {
  typeList,
  historicalType,
  treatmentType,
  reportTypeEnum,
  types,
  packageType,
  reportTypeList
} from './components/report-enum'
import { ArrowDown } from '@element-plus/icons-vue'
import tableList from './components/tableList.vue'
import ChooseIframe from '@/components/Dialog/ChooseIframe.vue'
import CommentDialog from './components/comment-dialog.vue'
import history from '@/views/user/reportManagement/components/history.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'realTimeRevenue',
  components: {
    tableList,
    SeletedUser,
    ChooseIframe,
    CommentDialog,
    history,
    ArrowDown
  },
  data() {
    return {
      dialogVisible: false,
      historicalList: [],
      page: 1,
      limit: 20,
      total: 0,
      loading: false,
      reportTypeList,
      form: {
        ossQueryType: '0', // 类型 大类型 （先不做了）
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        originalId: '', // 内容ID
        reportedUid: '', // 被举报人ID
        packageType: '',
        reportReasonType: [], //举报类型
        status: '0', // 处理状态
        authorId: '' // 举报人ID
      },
      typeList,
      historicalType,
      treatmentType,
      reportTypeEnum,
      groupTypes: convertKeyValueEnum(types),
      packageType: packageType,
      convertPackageType: convertKeyValueEnum(packageType),
      dataList: [],
      contentIdEnum: {
        essaypid: '评论',
        person_detail: '用户',
        used_car_pid: '二手车评论',
        chat_detail: '私信',
        cycling_route_reply: '骑行线路评论',
        punch_point_reply: '打卡点评论',
        riding_activity_reply: '约骑评论'
      },
      isGoToDialog: false,
      scrollTopNum: 0
    }
  },
  computed: {
    ...mapGetters(['name']),
    daterange: {
      get() {
        if (this.form.beginDate && this.form.endDate) {
          return [this.form.beginDate, this.form.endDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.beginDate = value[0]
          this.form.endDate = value[1]
        } else {
          this.form.beginDate = ''
          this.form.endDate = ''
        }
      }
    },
    isGoTo() {
      return function (val) {
        const arr = [
          'essaypid',
          'essay_detail',
          'moment_detail',
          'video_detail'
        ]
        return arr.includes(val)
      }
    },
    isPreView() {
      return function (val) {
        const arr = [
          'chat_detail',
          'person_detail',
          'short_topic',
          'activity',
          'driving_topic_pid'
        ]
        return !arr.includes(val)
      }
    }
  },
  activated() {
    const dom = document.getElementById('table')
    this.$nextTick(() => {
      dom.scrollTop = this.scrollTopNum
    })
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 查询列表
    getList() {
      const postData = {
        page: this.page,
        limit: this.limit,
        beginDate: this.form.beginDate, // 开始时间
        endDate: this.form.endDate, // 结束时间
        status: this.form.status, // 状态
        originalId: this.form.originalId, // 内容ID
        reportedUid: this.form.reportedUid,
        authorId: this.form.authorId,
        ossQueryType: this.form.reportReasonType[0],
        packageType: this.form.packageType || '',
        reportReasonType: Number(this.form.reportReasonType[1]) || ''
      }
      console.log(postData)
      this.loading = true
      getReportSummary(postData)
        .then((response) => {
          if (response.data.code === 0) {
            this.dataList = response.data.data.list || []
            this.total = response.data.data.total
            recordOldData(this.dataList)
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch((e) => {
          console.log(e)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 变更页码，上一页或下一页
    currentChange(page) {
      this.page = page
      this.getList()
    },

    // 设置返回uid
    setUid(id) {
      this.form.creatorId = id
    },
    // 重置
    initGetList() {
      this.form = {
        tag: '',
        creatorId: '',
        sortType: ''
      }
    },

    getPlusData(arr, key) {
      return arr.reduce((pre, cur) => {
        pre += cur[key]
        return pre
      }, 0)
    },
    getFindEssayData(item, key) {
      return item.find((_) => _.essayType === key) || {}
    },
    clipboardSuccess() {
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: 1500
      })
    },
    reset() {
      this.page = 1
      this.form = {
        ossQueryType: '0', // 类型
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        originalId: '', // 内容ID
        reportedUid: '',
        packageType: '',
        status: '0',
        authorId: '',
        reportReasonType: []
      }
      this.$refs.selectUserd.clearData()
      this.$refs.selectUser.clearData()
      this.getList()
    },
    setReportedUid(val) {
      this.form.reportedUid = val
    },
    setAuthorIdUid(val) {
      this.form.authorId = val
    },
    //获取展开的table数据
    getTableListData(item, index, isPage) {
      const me = this
      if (!isPage) {
        const dom = document.getElementById(`arrow${index}`)
        dom.style.transform = item.tableOpen ? 'rotate(0deg)' : 'rotate(180deg)'
        dom.style.transition = '0.5s'
        item.tableOpen = !item.tableOpen
      }
      if (!item.tableOpen) {
        return
      }
      this.$nextTick(() => {
        const data = {
          beginDate: me.form.beginDate,
          endDate: me.form.endDate,
          status: me.form.status
        }
        me.$refs[`tableList${index}`][0].open(data)
      })
    },
    // 历史记录
    historical(item) {
      const me = this
      me.$refs.history.showDialog(item)
      // GetCurrentLimitRec({
      //   uid: item.relatedId
      // }).then(response => {
      //   if (response.data.code === 0) {
      //     me.historicalList = response.data.data
      //     me.dialogVisible = true
      //   } else {
      //     me.$message.error(response.data.msg)
      //   }
      // })
    },
    showH5Preview(item) {
      if (
        item.idType === 'essaypid' ||
        item.idType === 'used_car_pid' ||
        item.idType === 'cycling_route_reply' ||
        item.idType === 'punch_point_reply' ||
        item.idTyoe === 'riding_activity_reply'
      ) {
        item.showId = item.relatedEssayId
      } else if (
        item.idType === 'essay_detail' ||
        item.idType === 'person_detail' ||
        item.idType === 'used_car' ||
        item.idType === 'moment_detail' ||
        item.idType === 'video_detail'
      ) {
        item.showId = item.relatedId
      } else {
        item.showId = item.reportedUid
      }
      this.isShow = true
      this.id = item.showId
      this.getH5PreviewSrc(item)
    },
    closeH5Preview() {
      this.isShow = false
      this.id = ''
    },
    getH5PreviewSrc(item) {
      if (item.idType) {
        switch (item.idType) {
          case 'essay_detail':
            this.h5PreviewSrc = `https://wap.corp.mddmoto.com/details-article/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'video_detail':
            this.h5PreviewSrc = `https://wap.corp.mddmoto.com/details-article/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'moment_detail':
            this.h5PreviewSrc = `https://wap.corp.mddmoto.com/details-article/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'person_detail':
            this.h5PreviewSrc = `https://wap.58moto.com/myhomepage?id=${item.showId}&sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'short_topic':
            // this.h5PreviewSrc = `https://wap.58moto.com/topic-homepage?id=${item.showId}&idType=0&sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'used_car_pid':
            this.h5PreviewSrc = `https://wap.corp.mddmoto.com/used-car-detail/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'essaypid':
            this.h5PreviewSrc = `https://wap.corp.mddmoto.com/details-article/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'cycling_route_reply':
            this.h5PreviewSrc = `https://m.58moto.com/cycling-route/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'punch_point_reply':
            this.h5PreviewSrc = `https://m.58moto.com/punch-point/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          case 'riding_activity_reply':
            this.h5PreviewSrc = `https://m.58moto.comriding_party/${item.showId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
            break
          default:
            this.h5PreviewSrc = `https://wap.58moto.com/myhomepage?id=${item.showId}&sourceType=C4CA4238A0B923820DCC509A6F75849B`
        }
        if (
          item.idType === 'essaypid' ||
          item.idType === 'used_car_pid' ||
          item.idType === 'cycling_route_reply' ||
          item.idType === 'punch_point_reply' ||
          item.idType === 'riding_activity_reply'
        ) {
          const types = {
            essaypid: 'essay_detail',
            used_car_pid: 'used_car',
            cycling_route_reply: 'cycling_route',
            punch_point_reply: 'punch_point',
            riding_activity_reply: 'riding_activity'
          }
          return this.$refs.CommentDialog.init(
            this.h5PreviewSrc,
            item.relatedId,
            {
              ...item,
              relatedId: item.showId,
              type: types[item.idType]
            }
          )
        }
        this.$refs.ChooseIframe.init(this.h5PreviewSrc)
      }
    },
    handleGotoClose() {
      this.isGoToDialog = false
    },
    goToDetail(idType, clickItem) {
      const defaultKV = {
        url: 'articleManageList',
        key: 'uid',
        value: 'reportedUid'
      }
      const mapObj = {
        essay_detail: defaultKV,
        moment_detail: defaultKV,
        video_detail: defaultKV,
        essaypid: {
          url: 'ReplyManagement',
          key: 'autherId',
          value: 'reportedUid'
        },
        chat_detail: {
          url: 'PrivateMessageQuery',
          key: 'uid',
          value: 'reportedUid'
        }
      }
      this.$router.push({
        name: mapObj[idType].url,
        query: {
          [mapObj[idType].key]: clickItem[mapObj[idType].value]
        }
      })
      this.handleGotoClose()
    }
  },

  beforeRouteLeave(to, from, next) {
    // ...
    const dom = document.getElementById('table')
    if (dom) {
      this.scrollTopNum = dom.scrollTop
    }
    next()
  }
}
</script>

<style lang="scss" scoped>
.data-border {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 5px 10px;
  margin: 5px;
}
.content-data {
  display: flex;
  justify-content: space-between;
}
.data-grow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  max-height: 150px;
  overflow: auto;
}
.data-flex-column {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 0 5px;
  div {
    line-height: 33px;
  }
  .data-between {
    display: flex;
    width: 100%;
    // justify-content: space-between;
  }
  span {
    display: inline-block;
    flex: 1;
    padding: 0 5px;
    line-height: 26px;
  }
  .tag-box {
    display: inline-block;
    font-size: 12px;
    padding: 3px 5px;
    line-height: 12px;
    border: 1px solid #dedede;
    color: #666666;
    border-radius: 2px;
    margin-left: 5px;
  }
}
.text-center {
  margin: 5px;
  text-align: center;
}
.data-flex-row {
  display: flex;
  align-items: center;
  justify-content: center;
  .line {
    flex: 1;
    display: inline-block;
    margin: 0 15px;
    border-top: 0.1px solid #ccc;
  }
}
</style>
