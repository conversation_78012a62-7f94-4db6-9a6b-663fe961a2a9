import { convertKeyValueEnum } from '@/utils/convert'

export const typeList = {
  全部: '0',
  用户: '1',
  内容: '2',
  私信: '3',
  内容评论: '4',
  // 视频: '5',
  // 其他: '0'
  二手车评论: '5',
  活动: '6',
  驾考评论: '7',
  骑行线路评论: '9',
  打卡点评论: '10',
  约骑评论: 11
}
export const reportStatusEnum = {
  0: '未处理',
  1: '忽略',
  3: '永久忽略',
  2: '已处理'
}

export const reportReasonTypeAllEnum = {
  0: [],
  1: [0, 1, 2, 3, 4, 5, 11, 12, 13, 6],
  2: [0, 1, 2, 3, 4, 5, 7, 8, 9, 6, 17, 19, 20, 21, 22],
  3: [0, 1, 2, 3, 4, 5, 6],
  4: [0, 1, 2, 3, 4, 5, 10, 6],
  5: [0, 1, 2, 3, 4, 5, 10, 6],
  6: [0, 1, 2, 3, 4, 5, 7, 8, 9, 6],
  7: [0, 1, 2, 3, 4, 5, 10, 6],
  9: [0, 1, 2, 3, 4, 5, 10, 6],
  10: [0, 1, 2, 3, 4, 5, 10, 6],
  11: [0, 1, 2, 3, 4, 5, 10, 6]
}

export const reportReasonTypeEnum = {
  0: '全部',
  1: '垃圾广告',
  2: '低俗色情',
  3: '不友善言论',
  4: '违法信息',
  5: '政治敏感',
  6: '其他问题',
  7: '题文不符',
  8: '内容不实',
  9: '抄袭侵权',
  10: '虚假谣言',
  11: '信息不实',
  12: '冒用账号',
  13: '虚假欺骗',
  14: '标题夸张',
  15: '旧闻重提',
  16: '封面反感',
  17: '内容质量差',
  19: '广告软文',
  20: '危险驾驶',
  21: '过时内容',
  22: '疑似AI'
}
const contentTypeEnum = {
  0: '全部',
  1: '广告软文',
  3: '不友善言论',
  6: '其他问题',
  9: '内容抄袭'
}
function reportReason(data, list) {
  const reason = []
  for (let index in data) {
    const enumList = reportReasonTypeEnum
    reason.push({
      value: index,
      label: list[index],
      children: reportReasonTypeAllEnum[index]
        .filter((reason) => enumList[reason])
        .map((item) => {
          return {
            value: item,
            label: enumList[item]
          }
        })
    })
  }
  return reason
}
export const reportTypeList = reportReason(
  reportReasonTypeAllEnum,
  convertKeyValueEnum(typeList)
)

export const historicalType = {
  1: '拉黑',
  2: '禁言',
  3: '封号'
}
export const treatmentType = {
  全部: '',
  未处理: '0',
  忽略: '1',
  永久忽略: '3',
  已处理: '2'
}

export const reportTypeEnum = {
  essay_detail: {
    name: '文章',
    status: {
      '-1': '个人删除',
      0: '系统删除',
      1: '显示',
      2: '待审核',
      3: '审核不通过'
    }
  },
  person_detail: {
    name: '用户',
    status: {
      0: '正常',
      2: '禁言',
      3: '封号'
    }
  },
  short_topic: {
    name: '话题',
    status: {
      0: '无效',
      1: '有效'
    }
  },
  moment_detail: {
    name: '动态',
    status: {
      '-1': '个人删除',
      0: '系统删除',
      1: '显示',
      2: '待审核',
      3: '审核不通过'
    }
  },
  essaypid: {
    name: '评论',
    status: {
      '-1': '个人删除',
      0: '正常',
      1: '待审核',
      2: '系统删除'
    }
  },
  chat_detail: {
    name: '私信',
    status: {
      0: '正常',
      1: '删除'
    }
  },
  used_car: {
    name: '二手车详情',
    status: {
      // 0: '正常',
      // 1: '下架'
      0: '运营下架',
      1: '正常',
      2: '待审核',
      3: '审核不通过',
      4: '个人删除',
      5: '已售出',
      6: '用户下架',
      7: '系统下架',
      8: '发票过期'
    }
  },
  used_car_pid: {
    name: '二手车评论',
    status: {
      '-1': '个人删除',
      0: '正常',
      1: '待审核',
      2: '系统删除'
    }
  },
  video_detail: {
    name: '视频',
    status: {
      '-1': '个人删除',
      0: '系统删除',
      1: '显示',
      2: '待审核',
      3: '审核不通过'
    }
  },
  activity: {
    name: '活动',
    status: {}
  },
  driving_topic_pid: {
    name: '驾考',
    status: {}
  },
  license_plate_trade: {
    name: '牌照交易',
    status: {
      // 0: '正常',
      // 1: '下架'
    }
  },
  cycling_route_reply: {
    name: '骑行线路评论',
    status: {
      '-1': '个人删除',
      0: '正常',
      1: '待审核',
      2: '系统删除'
    }
  },
  punch_point_reply: {
    name: '打卡点评论',
    status: {
      '-1': '个人删除',
      0: '正常',
      1: '待审核',
      2: '系统删除'
    }
  },
  riding_activity_reply: {
    name: '约骑评论',
    status: {
      '-1': '个人删除',
      0: '正常',
      1: '待审核',
      2: '系统删除'
    }
  }
}
export const types = {
  游客: 7,
  待审核: 8,
  小黑屋: 16,
  新手上路: 21,
  禁言: 4,
  封号: 25
}
export const packageType = {
  全部: '',
  主版本: 1,
  商家版: 2
}
