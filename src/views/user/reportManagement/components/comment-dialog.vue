<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    title="评论预览"
    width="450px"
    top="10vh"
    center
    append-to-body
  >
    <div class="content">
      <div class="content-box">
        <span>评论内容</span>
        <el-button v-if="type !== 'content'" type="text" @click="gotoAll"
          >全部评论</el-button
        >
      </div>
      <div v-if="dataList" class="content-item">
        <template v-if="dataList[0].replyDTO">
          <p>
            {{ dataList[0].replyDTO.auther }}:
            {{ dataList[0].replyDTO.content }}
            <img
              v-if="dataList[0].replyDTO?.imageInfo?.imgUrl"
              :src="dataList[0].replyDTO.imageInfo.imgUrl"
              class="comment-img"
              @click="openLink(dataList[0].replyDTO?.imageInfo)"
            />
          </p>
        </template>
        <template v-if="dataList[1].replyDTO">
          <strong
            >{{ dataList[1].replyDTO.auther }}:
            {{ dataList[1].replyDTO.content }}
            <img
              v-if="dataList[1].replyDTO?.imageInfo?.imgUrl"
              :src="dataList[1].replyDTO.imageInfo.imgUrl"
              class="comment-img"
              @click="openLink(dataList[1].replyDTO.imageInfo)"
            />
          </strong>
        </template>
        <template v-if="dataList[2] && dataList[2].replyDTO">
          <p>
            {{ dataList[2].replyDTO.auther }}:
            {{ dataList[2].replyDTO.content }}
            <img
              v-if="dataList[2].replyDTO?.imageInfo?.imgUrl"
              :src="dataList[2].replyDTO.imageInfo.imgUrl"
              class="comment-img"
              @click="openLink(dataList[2].replyDTO.imageInfo)"
            />
          </p>
        </template>
      </div>
    </div>
    <div class="iframe-content">
      <iframe
        allowfullscreen
        v-show="url"
        :src="url"
        frameborder="0"
        class="iframe"
      />
    </div>
    <CommentManage ref="commentManage" />
    <ChooseShowImage ref="showImage" />
  </el-dialog>
</template>
<script>
import { getCommentContent } from '@/api/user'
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import CommentManage from '@/views/business/articleManage/articleManageList/components/CommentManage.vue'
import { getMediaOriginal } from '@/api/article'

export default {
  data() {
    return {
      dialogVisible: false,
      url: '',
      dataList: null,
      type: 'content',
      id: '',
      currentdata: {}
    }
  },
  components: {
    CommentManage,
    ChooseShowImage
  },
  methods: {
    init(url, id, data) {
      this.dialogVisible = true
      this.url = url
      this.type = data.type
      this.currentdata = data
      this.id = id
      this.getData(id)
    },
    getData(id) {
      const me = this
      getCommentContent({
        ids: id
      })
        .then((res) => {
          if (res.data.code === 0) {
            console.log(res.data.data[0])
            me.dataList = res.data.data || []
          }
        })
        .catch((e) => {
          console.log(e)
        })
    },
    gotoAll() {
      console.log(this.currentdata)
      this.currentdata = {
        ...this.currentdata,
        id: this.currentdata.relatedId
      }
      this.$refs.commentManage.init(this.currentdata, this.type)
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      this.url = ''
      this.id = ''
      this.type = 'content'
      this.currentdata = {}
    },
    openLink(info) {
      getMediaOriginal({
        businessType: info.mediaCode,
        object: info.imgUrl
      }).then((res) => {
        if (res.data.code === 0) {
          const link = res.data?.data[info.imgUrl]
          this.$refs.showImage.init(link || info.imgUrl)
        }
      })
    }
  }
}
</script>
<style lang="scss">
.content {
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 2px;
  margin: 10px 0;
}
.content-item {
  margin: 10px 0;
}
.content-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.iframe-content {
  width: 400px !important;
  height: auto !important;
  overflow-y: hidden;
}
.iframe {
  width: 380px;
  height: 600px;
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 5px;
  margin: 0 auto;
  display: block;
}
.comment-img {
  width: 50px;
  height: 50px;
  border-radius: 5px;
}
</style>
