<template>
  <div v-loading="loading" class="review-all">
    <!-- <header class="action">
      <el-button
        v-if="authIds.length !== 0"
        type="success"
        @click="batchUpdateReplyStatus(0)"
        >全部通过</el-button
      >
      <el-button
        v-if="authIds.length !== 0"
        style="background: #ff9933; color: white"
        @click="batchUpdateReplyStatus(3)"
        >全部不通过</el-button
      >
    </header> -->
    <el-row :gutter="10" class="row-bg main">
      <el-col :span="5" class="left">
        <el-form :model="ruleForm" class="replySearch">
          <el-form-item class="action">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset()">重置</el-button>
          </el-form-item>
          <el-form-item label="业务场景">
            <el-select v-model="ruleForm.type">
              <el-option
                v-for="(value, index) in typeList"
                :key="index"
                :label="value"
                :value="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="评论ID">
            <el-input v-model="ruleForm.ids" placeholder="评论ID，逗号分隔" />
          </el-form-item>
          <el-form-item label="内容ID">
            <el-input v-model="ruleForm.relatedId" placeholder="请输入内容ID" />
          </el-form-item>
          <el-form-item label="评论内容">
            <el-input v-model="ruleForm.content" placeholder="请输入评论内容" />
          </el-form-item>
          <el-form-item label="评论人">
            <el-autocomplete
              v-model="ruleForm.auther"
              :fetch-suggestions="querySearchAsyncAuther"
              :trigger-on-focus="false"
              placeholder="请输入评论人"
              style="width: 100%"
              @select="handleSelectAuther"
            />
          </el-form-item>
          <el-form-item label="用户ID">
            <el-input v-model="ruleForm.autherId" placeholder="请输入用户ID" />
          </el-form-item>
          <el-form-item label="审核人">
            <el-input v-model="ruleForm.operator" placeholder="请输入审核人" />
          </el-form-item>
          <!-- <el-button
            :type="topStatus === 1 ? 'success' : ''"
            @click="topStatus = topStatus === 1 ? '' : 1"
            >仅查看置顶评论</el-button
          > -->
          <el-form-item>
            <el-button
              :type="topStatus === 1 ? 'success' : ''"
              @click="changeTopStatus"
              >仅查看置顶评论</el-button
            >
          </el-form-item>
          <el-form-item>
            <el-button :type="isHot === 1 ? 'success' : ''" @click="changeIsHot"
              >仅查看热门评论</el-button
            >
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="ruleForm.status">
              <el-option
                v-for="(value, index) in replyAllStatusEnum"
                :key="index"
                :label="index"
                :value="value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              :default-time="
                ['00:00:00', '23:59:59'].map((d) =>
                  $dayjs(d, 'hh:mm:ss').toDate()
                )
              "
              :shortcuts="pickerOptions && pickerOptions.shortcuts"
              :disabled-date="pickerOptions && pickerOptions.disabledDate"
              :cell-class-name="pickerOptions && pickerOptions.cellClassName"
              v-model="createDateRange"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-button
            :type="ruleForm.blockStatus === 1 ? 'success' : ''"
            @click="changeBlockStatus(1)"
            >只看安全官屏蔽</el-button
          ><br />
          <br />
          <el-button
            :type="ruleForm.blockStatus === 2 ? 'success' : ''"
            @click="changeBlockStatus(2)"
            >只看作者屏蔽</el-button
          >
          <el-button
            :type="ruleForm.blockStatus === 3 ? 'success' : ''"
            @click="changeBlockStatus(3)"
            >仅看后台屏蔽</el-button
          >
        </el-form>
      </el-col>
      <el-col :span="19">
        <div>
          <el-table
            ref="replyList"
            :data="replyList"
            row-key="replyList"
            border
            style="width: 100%"
            height="78vh"
          >
            <el-table-column
              label="内容ID"
              prop="relatedId"
              align="center"
              width="120"
            />
            <el-table-column
              label="评论ID"
              prop="id"
              align="center"
              width="120"
            />
            <el-table-column
              label="评论人"
              prop="auther"
              align="center"
              width="120"
            />
            <el-table-column label="用户认证" align="center" width="120">
              <template v-slot="scope">
                <div
                  v-for="(item, index) in scope.row.certifyNames"
                  :key="index"
                >
                  {{ item.certifyName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="业务场景"
              prop="id"
              align="center"
              width="120"
            >
              <template v-slot="scope">
                {{ typeList[scope.row.type] }}
              </template>
            </el-table-column>
            <el-table-column
              label="评论内容"
              prop="content"
              align="center"
              width="300"
            >
              <template #default="{ row }">
                {{ row.content }}
                <img
                  v-if="row?.imageInfo?.imgUrl"
                  :src="row.imageInfo.imgUrl"
                  class="comment-img"
                  @click="openImg(row.imageInfo)"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="机审状态"
              prop=""
              align="center"
              width="120"
            >
              <template v-slot="scope">
                <div>{{ reviewStatus[scope.row.machineAuditStatus] }}</div>
              </template>
            </el-table-column>
            <el-table-column
              label="机审原因"
              prop="machineAuditFailReason"
              align="center"
              width="120"
            />
            <!-- <el-table-column label="操作" align="center" width="400">
              <template v-if="scope.row.status === 1" v-slot="scope">
                <el-button
                  type="success"
                  @click="singleUpdateReplyStatus(scope.row, 0, scope.$index)"
                  >通过</el-button
                >
                <el-button
                  style="background: #ff9933; color: white"
                  @click="singleUpdateReplyStatus(scope.row, 3, scope.$index)"
                  >不通过</el-button
                >
                <el-button
                  type="danger"
                  @click="singleUpdateReplyStatus(scope.row, 2, scope.$index)"
                  >系统删除</el-button
                >
              </template>
            </el-table-column> -->
            <!-- <el-table-column
              label="内容关联"
              prop="relatedId"
              align="center"
              width="120"
            /> -->
            <el-table-column
              label="发布时间"
              prop="createTime"
              align="center"
              width="120"
            >
              <template v-slot="scope">
                {{ $filters.timeFullS(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column
              label="当前状态"
              prop="status"
              align="center"
              width="140"
            >
              <template v-slot="scope">
                <el-cascader
                  :disabled="[2, 1, -1].includes(scope.row.status)"
                  :model-value="[scope.row.status]"
                  :options="
                    getOptions(scope.row.status, scope.row.operatorType)
                  "
                  @change="(val) => handleChange(val, scope.row)"
                ></el-cascader>
              </template>
            </el-table-column>
            <el-table-column
              label="置顶状态"
              prop="topStatus"
              align="center"
              width="120"
            >
              <template v-slot="scope">
                <el-switch
                  :value="scope.row.topStatus === 1"
                  @change="changeSwitch(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="热门"
              prop="isHot"
              align="center"
              width="120"
            >
              <template v-slot="scope">
                <el-checkbox
                  :value="scope.row.isHot === 1"
                  @change="changeCheckbox(scope.row)"
                  >不进热门</el-checkbox
                >
              </template>
            </el-table-column>
            <el-table-column
              label="屏蔽状态"
              prop="guardName"
              align="center"
              width="120"
            >
              <template #default="{ row }">
                <el-tag
                  v-if="commentShieldType[row.blockType]"
                  class="wd-mx-5px"
                  type="warning"
                  effect="plain"
                  >{{ commentShieldType[row.blockType] }}</el-tag
                >
              </template>
            </el-table-column>
            <!-- <el-table-column
              label="审核人"
              prop="operator"
              align="center"
              width="120"
            /> -->
            <!-- <el-table-column label="日志" align="center" width="120">
              <template v-slot="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="auditRecords(scope.row)"
                  >查看</el-button
                >
              </template>
            </el-table-column> -->
            <el-table-column label="操作" align="center" width="120">
              <template v-slot="scope">
                <div>
                  <el-button
                    type="primary"
                    link
                    @click="auditRecords(scope.row)"
                    >查看日志</el-button
                  >
                </div>
                <div>
                  <el-button type="primary" link @click="getToDetail(scope.row)"
                    >查看详情</el-button
                  >
                </div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-model:current-page="page"
            :page-size="limit"
            :page-sizes="[20, 50, 100]"
            :total="total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            class="el-pagination-center"
            @size-change="searchReplyList"
            @current-change="searchReplyListByPage"
          />
        </div>
      </el-col>
    </el-row>
    <SeeLog ref="SeeLog" />
    <commentDialog ref="commentDialog" />
    <ChooseShowImage ref="showImg" />
  </div>
</template>

<script>
import { forwardPickerOptions } from '@/utils/configData'
import commentDialog from '@/views/user/reportManagement/components/comment-dialog.vue'
import {
  getReplyList,
  batchUpdateReplyStatus,
  topReply
} from '@/api/articleModule'
import { mapGetters } from 'vuex'
import { getUserAccountCorrelationV2 } from '@/api/user'
import { getUserCertifyInfo } from '@/utils/service/index'
import { reviewStatus } from '@/utils/enum'
import { commentShieldType } from '@/utils/enum/comment'
import {
  recordOldData,
  recordBeforeAlter,
  batchRecordBeforeAlter
} from '@/utils/enum/logData'
import SeeLog from '@/components/Dialog/SeeLog.vue'
import { options, replyAllStatusEnum } from './enum'
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import { getMediaOriginal } from '@/api/article'

export default {
  name: 'reviewAll',
  components: {
    SeeLog,
    commentDialog,
    ChooseShowImage
  },
  data() {
    return {
      loading: false,
      reviewStatus: reviewStatus,
      options,
      replyAllStatusEnum,
      total: 0,
      authIds: [],
      replyList: [],
      pickerOptions: forwardPickerOptions,
      topStatus: '',
      isHot: '',
      commentShieldType,
      ruleForm: {
        relatedId: null, // 内容ID
        content: '', // 评论内容
        auther: '', // 评论人
        autherId: '', // 评论人ID
        operator: '', // 审核人
        status: '', // 状态
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        blockStatus: '',
        ids: '', // 评论id，多个以逗号分隔
        type: '' //内容场景
      },
      typeList: {
        '': '全部',
        used_car: '二手车',
        content: '内容',
        driving_topic_pid: '驾考'
      },
      page: 1,
      limit: 20
    }
  },
  computed: {
    ...mapGetters(['uid', 'name']),
    createDateRange: {
      get() {
        if (this.ruleForm.beginDate && this.ruleForm.endDate) {
          return [this.ruleForm.beginDate, this.ruleForm.endDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm['beginDate'] = value[0]
          this.ruleForm['endDate'] = value[1]
        } else {
          this.ruleForm.beginDate = ''
          this.ruleForm.endDate = ''
        }
      }
    }
  },
  // created() {
  //   this.searchReplyList()
  // },
  activated() {
    if (this.$route.query.isManagement) {
      this.ruleForm.operator = this.name
      this.ruleForm.status = ''
    }
    if (this.$route.query.autherId) {
      this.ruleForm.autherId = this.$route.query.autherId
    }
    this.searchReplyList()
  },
  methods: {
    handleChange(val, data) {
      let obj = {
        status: val[0],
        ids: data.id,
        operator: this.name
      }
      if (val.length > 1) {
        obj.message = val[1]
      }
      batchUpdateReplyStatus(obj)
        .then(() => {
          this.$message.success('操作成功')
          this.searchReplyList()
        })
        .catch((e) => {
          console.log(e)
        })
    },
    getOptions(status, operatorType) {
      const arr = JSON.parse(JSON.stringify(this.options))
      if (status === 0) {
        const canEditStatus = [3, 1, 2]
        arr.forEach((item) => {
          if (!canEditStatus.includes(item.value)) {
            item.disabled = true
          }
        })
      } else if (status === 3 || status === 2) {
        const canEditStatus = status === 3 ? [2, 1] : [2]
        arr.forEach((item) => {
          if (!canEditStatus.includes(item.value)) {
            item.disabled = true
          }
          if (item.value === status) {
            delete item.children
            // item.children.forEach(child => {
            //   child.disabled = true
            // })
          }
        })
      } else if (status === 4) {
        const canEditStatus = [1]
        arr.forEach((item) => {
          if (!canEditStatus.includes(item.value)) {
            item.disabled = true
          }
          // if (item.value === 4 && operatorType === 3) {
          //   item.label = '仅自己可见'
          // }
        })
      }
      return arr
    },

    isBackBlock(row) {
      let block = false
      if (row.status === 4) {
        block = row.operatorType === 3 || row.blockType === 2
      }
      return block
    },
    reset() {
      this.page = 1
      this.limit = 20
      this.authIds = []
      this.ruleForm = {
        relatedId: null, // 内容ID
        content: '', // 评论内容
        auther: '', // 评论人
        autherId: '', // 评论人ID
        operator: '', // 审核人
        status: '', // 状态
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        blockStatus: '', // 安全官屏蔽/作者屏蔽
        ids: '', // 评论id，多个以逗号分隔
        type: '' //内容场景
      }
      this.topStatus = ''
      this.isHot = ''
      this.searchReplyList()
    },
    // 审核人
    querySearchAsyncAuther(queryString, cb) {
      const me = this
      me.$tools.debounce(searchAsyncAuther, 300)()
      function searchAsyncAuther() {
        me.ruleForm.autherid = ''
        getUserAccountCorrelationV2({
          username: me.ruleForm.auther,
          page: 1,
          limit: 20
        }).then((response) => {
          if (response.data.code === 0) {
            const userNameList = []
            const result = response.data.data && response.data.data.list
            result.map(function (value) {
              const newObj = {
                value: value.username,
                uid: value.uid
              }
              userNameList.push(newObj)
            })
            cb(userNameList)
          }
        })
      }
    },
    handleSelectAuther(item) {
      this.ruleForm.autherId = item.uid
      this.ruleForm.auther = item.value
    },
    search() {
      this.page = 1
      this.searchReplyList()
    },
    searchReplyListByPage() {
      this.searchReplyList()
    },
    searchReplyList(size) {
      const me = this
      me.limit = size || me.limit
      me.loading = true
      getReplyList({
        relatedId: me.ruleForm.relatedId,
        auther: '', // 仅用户id查询，用户名不传递
        autherId: me.ruleForm.autherId,
        ids: me.ruleForm.ids,
        content: me.ruleForm.content,
        operator: me.ruleForm.operator,
        status: me.ruleForm.status,
        beginDate: me.ruleForm.beginDate,
        endDate: me.ruleForm.endDate,
        blockStatus: me.ruleForm.blockStatus,
        topStatus: me.topStatus,
        isHot: me.isHot,
        type: me.ruleForm.type,
        page: me.page,
        limit: me.limit
      })
        .then(async (response) => {
          const resData = response.data.data
          if (response.data.code === 0) {
            me.replyList =
              resData.listData.length > 0
                ? await getUserCertifyInfo(resData.listData)
                : []
            recordOldData(me.replyList)
            me.total = response.data.data.total
            me.getAllAuthReplyIds() // 计算当前待审核评论IDs
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    batchUpdateReplyStatus(status) {
      const me = this
      const ids = me.authIds.join(',')
      batchRecordBeforeAlter(me.replyList, ids)
      if (status === 2) {
        this.$confirm('确认全部不通过?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            me.updateReplyStatus(ids, status)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      } else {
        me.updateReplyStatus(ids, status)
      }
    },
    singleUpdateReplyStatus(data, status, index) {
      recordBeforeAlter(data, 'id')
      this.updateReplyStatus(data.id, status, index)
    },
    changeStatus(row, index) {
      recordBeforeAlter(row, 'id')
      this.updateReplyStatus(row.id, row.status, index)
    },
    // 更新数据
    updateReplyStatus(ids = '', status, index) {
      const me = this
      batchUpdateReplyStatus({
        ids,
        limit: (ids + '').split(',').length,
        operator: me.name,
        status: status
      }).then((response) => {
        if (response.data.code !== 0) {
          return me.$message.error(response.data.msg)
        }
        me.$message.success('操作成功')
        if (index !== undefined && this.ruleForm.status != '') {
          me.replyList.splice(index, 1)
        }
        ;(ids + '').indexOf(',') > -1
          ? me.searchReplyList()
          : me.getAllAuthReplyIds() // 重新计算当前待审核评论IDs
      })
    },
    // 获取所有评论ID
    getAllAuthReplyIds() {
      const me = this
      if (me.replyList) {
        me.authIds = me.replyList.map((_) => _.id)
      }
    },
    // 变更置顶
    changeSwitch(data) {
      // console.log(data)
      const me = this
      topReply({
        replyId: data.id,
        essayId: data.relatedId,
        status: data.topStatus === 1 ? 0 : 1,
        type: data.type
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('操作成功')
          data.topStatus = data.topStatus === 1 ? 0 : 1
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 不进热门
    changeCheckbox(data) {
      // console.log(`data`, data)
      const me = this
      topReply({
        replyId: data.id,
        essayId: data.relatedId,
        isCloseHot: data.isHot === 1 ? 0 : 1
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('操作成功')
          data.isHot = data.isHot === 1 ? 0 : 1
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    changeTopStatus() {
      this.topStatus = this.topStatus === 1 ? '' : 1
      this.isHot = this.topStatus === 1 ? '' : this.isHot
    },
    changeIsHot() {
      this.isHot = this.isHot === 1 ? '' : 1
      this.topStatus = this.isHot === 1 ? '' : this.topStatus
    },
    changeBlockStatus(num) {
      this.ruleForm.blockStatus = this.ruleForm.blockStatus === num ? '' : num
    },
    // 查看日志记录
    auditRecords(data) {
      // console.log(data)
      this.$refs.SeeLog.init(data.id, 1)
    },
    getToDetail(data) {
      const typeList = {
        content: 'details-article',
        used_car: 'used-car-detail'
      }
      const url = `https://wap.58moto.com/${typeList[data.type]}/${
        data.relatedId
      }?sourceType=C4CA4238A0B923820DCC509A6F75849B`
      this.$refs.commentDialog.init(url, data.id, data)
    },
    openImg(info) {
      getMediaOriginal({
        businessType: info.mediaCode,
        object: info.imgUrl
      }).then((res) => {
        if (res.data.code === 0) {
          const link = res.data?.data[info.imgUrl]
          this.$refs.showImg.init(link || info.imgUrl)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.review-all {
  // header {
  //   text-align: right;
  //   margin-bottom: 10px;
  //   margin-right: 10%;
  // }

  .comment-img {
    width: 50px;
    height: 50px;
    border-radius: 5px;
  }
  .main {
    .left {
      .replySearch {
        margin: 0 auto;
        text-align: left;
        padding: 0 20px;
        border-radius: 2px;
        border: 1px solid rgb(221, 221, 221);
        overflow: auto;
        height: 78vh;
        .el-form-item {
          .el-select {
            width: 100%;
          }
        }
        .action {
          margin: 10px auto;
        }
      }
    }
  }
}
</style>
