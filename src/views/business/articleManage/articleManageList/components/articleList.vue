<template>
  <div class="content">
    <div v-if="articleList?.length">
      <el-table
        ref="articleList"
        :data="articleList"
        row-key="articleList"
        border
        height="78vh"
        @sort-change="sortSearchList"
        @selection-change="handleSelectionChange"
        @row-click="rowClick"
      >
        <el-table-column type="selection" width="70" />
        <el-table-column label="ID" align="center" width="120">
          <template v-slot="scope">
            <div class="copy1">
              <span>{{ scope.row.id }}</span>
              <el-tooltip content="复制" placement="bottom" effect="light">
                <el-button
                  v-clipboard:copy="scope.row.id"
                  v-clipboard:success="clipboardSuccess"
                  type="text"
                >
                  <el-icon><IconCopyDocument /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
            <div style="color: #66b1ff">
              {{ scope.row.original === '1' ? '原创' : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="作者" align="center" width="150">
          <template v-slot="scope">
            <div class="copy2">
              <span>{{ scope.row.author }}</span>
              <el-tooltip content="复制" placement="bottom" effect="light">
                <el-button
                  v-clipboard:copy="scope.row.author"
                  v-clipboard:success="clipboardSuccess"
                  type="text"
                >
                  <el-icon><IconCopyDocument /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
            <div class="copy3">
              <span>{{ scope.row.authorId }}</span>
              <el-tooltip content="复制" placement="bottom" effect="light">
                <el-button
                  v-clipboard:copy="scope.row.authorId"
                  v-clipboard:success="clipboardSuccess"
                  type="text"
                >
                  <el-icon><IconCopyDocument /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
            <div v-if="scope.row.certifyNames">
              <div
                style="color: #909399"
                v-for="(item, index) in scope.row.certifyNames"
                :key="index"
              >
                <!--实名认证说没啥用，屏蔽掉-->
                {{ ['9'].includes(item.type) ? '' : item.certifyName }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="列表标题/内容"
          prop="type"
          align="center"
          width="450"
          sortable="custom"
        >
          <template #header>
            <div
              style="
                display: inline-flex;
                width: 90%;
                justify-content: flex-end;
                text-align: right;
              "
            >
              <div style="flex: 5">
                {{ '列表标题/内容' }}
              </div>
              <div style="flex: 4; color: #999; font-size: 12px">
                {{ sortableName || 'CTR排序' }}
              </div>
            </div>
          </template>
          <template v-slot="scope">
            <div style="display: flex">
              <div style="flex: 1; display: flex; justify-content: flex-start">
                <el-tag
                  v-if="scope.row.prime"
                  size="small"
                  type="warning"
                  effect="dark"
                >
                  {{ '优质' }}
                </el-tag>
              </div>
              <el-button
                :icon="IconView"
                type="primary"
                link
                @click="showH5Preview(scope.row)"
                >预览</el-button
              >
            </div>
            <c-feedList :card="scope.row" />
            <div class="circle-box">
              <div
                v-for="hoop in scope.row.esMotorHoopList"
                :key="hoop.id"
                class="box"
              >
                <el-icon class="icon"><IconHelp /></el-icon>{{ hoop.name }}
              </div>
            </div>
            <div class="content-foot">
              <div class="content-foot-box">
                <span>CTR {{ (scope.row.ctr * 100).toFixed(2) }}%</span>
                <span>阅读完成率 {{ scope.row.readPercent || '0.00%' }}</span>
                <span>曝光量 {{ scope.row.exposureTimes || 0 }}</span>
                <span>评论数 {{ scope.row.replycnt || 0 }} </span>
              </div>
              <div class="content-foot-box">
                <span>浏览量 {{ scope.row.viewNum || 0 }} </span>
                <span
                  >发布时间：
                  {{
                    $filters.timeFullS(
                      (scope.row.dateLine || scope.row.createTime) * 1000
                    )
                  }}</span
                >
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="预览" prop="address" align="center" width="70">
                        <template v-slot="scope">
                          <el-button type="primary" link size="small" @click="showH5Preview(scope.row)">查看</el-button>
                        </template>
                      </el-table-column> -->
        <el-table-column label="分类" prop="tagName" align="center" />
        <!-- <el-table-column label="评论数" prop="replycnt" align="center" /> -->
        <!-- <el-table-column label="是否优质" prop="levelName" align="center" width="100">
                        <template v-slot="scope">
                          <span>{{ scope.row.prime ? '优质' : '' }}</span>
                        </template>
                      </el-table-column> -->

        <el-table-column label="状态" prop="status" align="center" width="130">
          <template v-slot="scope">
            <div v-if="[1, 3].indexOf(scope.row.transcodeStatus) > -1">
              {{ scope.row.transcodeStatus === 1 ? '转码中' : '转码失败' }}
            </div>
            <!-- <div v-else>{{ articleStatusEnum[scope.row.status] }}</div> -->
            <!-- <el-select :disabled="[2, 0, -1].includes(scope.row.status)" v-else v-model="scope.row.status">
                            <el-option v-for="(value, index) in originArticleStatusEnum" :key="index" :label="index" :value="value" />
                          </el-select> -->
            <el-cascader
              v-else
              :disabled="[2, 0, 9, -1].includes(scope.row.status)"
              :model-value="[scope.row.status]"
              :options="getOptions(scope.row.status, scope.row.certifyNames)"
              @change="(val) => handleChange(val, scope.row)"
            ></el-cascader>
          </template>
        </el-table-column>
        <!-- <el-table-column label="状态" prop="status" align="center" width="130">
                        <template v-if="scope.row.type === 'essay_detail'" v-slot="scope">
                          <el-select
                            v-model="scope.row.status"
                            :class="{'is-choice-examine': scope.row.status === 2,'is-choice-delete': (scope.row.status === -1 || scope.row.status === 0)}"
                            @change="changeStatus(scope.row)"
                          >
                            <el-option
                              v-for="(value, index) in originArticleStatusEnum"
                              :key="index"
                              :label="index"
                              :value="value"
                              @click="scope.row.status=value"
                            />
                          </el-select>
                        </template>
                      </el-table-column> -->
        <!-- <el-table-column label="CTR" prop="ctr" align="center" sortable="custom" width="80">
                        <template v-slot="scope">{{ (scope.row.ctr * 100).toFixed(2) }}%</template>
                      </el-table-column> -->
        <!-- <el-table-column prop="readPercent" label="阅读完成率" align="center" width="110"> </el-table-column> -->
        <el-table-column
          label="平台"
          prop="platform"
          align="center"
          width="100"
        >
          <template v-slot="scope">{{
            getPlatFormDesc(scope.row.platform)
          }}</template>
        </el-table-column>
        <el-table-column
          label="推送库"
          prop="platform"
          align="center"
          width="100"
        >
          <template v-slot="scope">
            <el-switch
              v-if="
                scope.row.type !== 'moment_detail' && scope.row.status === 1
              "
              :model-value="scope.row.pushRepoStatus"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="editPushData(scope.row)"
            />
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="舆情监测" prop="type" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.publicOpinionMonitor"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              @change="changMonitor(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="110">
          <template v-slot="scope">
            <div>
              <el-button
                type="primary"
                link
                size="small"
                @click="rowDoubleClick(scope.row, true)"
                >编辑内容</el-button
              >
            </div>
            <div>
              <el-button
                type="primary"
                link
                size="small"
                @click="rowDoubleClick(scope.row, false)"
                >修改信息</el-button
              >
            </div>
            <div>
              <el-button
                v-if="scope.row.status !== 2"
                type="primary"
                link
                size="small"
                @click="clickComment(scope.row)"
                >评论管理</el-button
              >
            </div>
            <el-button
              type="primary"
              link
              size="small"
              @click="auditRecords(scope.row)"
              >查看日志</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        @size-change="currentChange"
        @current-change="currentChange"
      />
    </div>

    <el-dialog
      :model-value="showStatusDialog"
      title="状态批处理"
      width="300px"
      @close="closeDialog"
    >
      <el-form>
        <el-form-item>
          <el-select v-model="articleStatus" placeholder="请选择状态">
            <el-option
              v-for="(value, index) in articleStatusEnum"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="modifyEssayStatus">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <choose-iframe ref="ChooseIframe" />

    <el-dialog
      :model-value="showPushDialogStatus"
      title="设置"
      width="500px"
      center
      @close="closePushDialogStatus"
    >
      <el-form label-width="80px">
        <el-form-item label="推送标题" required>
          <el-input v-model.trim="pushData.pushTitle" type="text" />
        </el-form-item>
        <el-form-item label="推送内容" required>
          <el-input v-model.trim="pushData.pushContent" type="text" />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="closePushDialogStatus">取 消</el-button>
          <el-button type="primary" @click="setPushTitle">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <SeeLog ref="SeeLog" />
    <ChooseDetail ref="ChooseDetail" />
    <CommentManage ref="commentManage" />
  </div>
</template>

<script>
import { Help as IconHelp } from '@element-plus/icons-vue'
import { View as IconView } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import {
  articleStatusEnum,
  platformAllEnum,
  articleDisplayList
} from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import { laterPickerOptions } from '@/utils/configData'
import {
  searchArticleList, // 文章搜索
  updateEssayStatus, // 批量修改文章状态
  publishEssayToBJH, // 内容同步百家号
  updateSingleEssayStatus,
  postOpinionMonitor
} from '@/api/articleModule'
import { setPushTitle } from '@/api/user'
import CFeedList from '@/components/CFeedList/index.vue'
import ChooseIframe from '@/components/Dialog/ChooseIframe.vue'
import ChooseDetail from '@/components/CDetail/ChooseDetail.vue'
import SeeLog from '@/components/Dialog/SeeLog.vue'
import CommentManage from './CommentManage.vue'
import { getUserCertifyInfo } from '@/utils/service/index'
import { CopyDocument as IconCopyDocument } from '@element-plus/icons-vue'

import {
  recordOldData,
  recordBeforeAlter,
  batchRecordBeforeAlter
} from '@/utils/enum/logData'
export default {
  data() {
    return {
      localRuleForm: {},
      arr: [],
      sortableName: '',
      options: [
        {
          value: 2,
          label: '审核中'
        },
        {
          value: 1,
          label: '审核通过'
        },
        {
          value: 3,
          label: '审核不通过',
          children: [
            {
              value: 101,
              label: '广告'
            },
            {
              value: 102,
              label: '信息泄露'
            },
            {
              value: 103,
              label: '辱骂引战，人身攻击'
            },
            {
              value: 104,
              label: '色情'
            },
            {
              value: 105,
              label: '平台负面、谣言'
            },
            {
              value: 106,
              label: '二手车交易'
            },
            {
              value: 107,
              label: '装备交易'
            },
            {
              value: 108,
              label: '内容展示异常'
            }
          ]
        },
        {
          value: -1,
          label: '个人删除'
        },
        {
          value: 0,
          label: '永久删除',
          children: [
            {
              value: 201,
              label: '政治敏感、暴恐或其他违法信息'
            },
            {
              value: 202,
              label: '封建迷信'
            },
            {
              value: 203,
              label: '严重涉黄'
            },
            {
              value: 204,
              label: '抄袭'
            }
          ]
        },
        {
          value: 4,
          label: '仅自己可见'
        },
        {
          value: 9,
          label: '编辑审核中'
        }
      ],
      message: 'Copy These Text',
      // 默认状态1 正常
      articleStatus: 1,
      articleStatusEnum: convertKeyValueEnum(articleStatusEnum),
      originArticleStatusEnum: articleStatusEnum,
      platformEnum: convertKeyValueEnum(platformAllEnum),
      pickerOptions: laterPickerOptions,
      showQualityDialog: false,
      showtopDialog: false,
      // 默认列表集合
      articleDisplayList: articleDisplayList,
      // 显示列表集合
      showList: [],
      // 存储位置集合
      locationRidList: {},
      // 存储当前修改对象（优质、置顶）
      currentChangeObject: {},
      // 活动数据
      activeData: {},
      form: {
        qualityBeginDate: '',
        qualityEndDate: '',
        topBeginDate: '',
        topEndDate: '',
        locationRid: '', // 位置
        sort: '' // 排序
      },
      gradeClass: {
        P1: 'red',
        P2: '#e5e540',
        P3: 'green'
      },
      page: 1,
      total: 0,
      articleList: [],
      // 已选中的文章数据数组
      selectedList: [],
      // 推送库设置
      showPushDialogStatus: false,
      pushData: {
        pushTitle: '',
        pushContent: ''
      },
      IconView: markRaw(IconView)
    }
  },
  name: 'ArticleList',
  components: {
    CFeedList,
    ChooseIframe,
    SeeLog,
    ChooseDetail,
    CommentManage,
    IconHelp,
    IconCopyDocument
  },
  directives: {},
  props: {
    showStatusDialog: {
      type: Boolean,
      default: false
    },
    ruleForm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    qualityDateRange: {
      get() {
        if (this.form.qualityBeginDate && this.form.qualityEndDate) {
          return [this.form.qualityBeginDate, this.form.qualityEndDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.qualityBeginDate = value[0]
          this.form.qualityEndDate = value[1]
        } else {
          this.form.qualityBeginDate = ''
          this.form.qualityEndDate = ''
        }
      }
    },
    topDateRange: {
      get() {
        if (this.form.topBeginDate && this.form.topEndDate) {
          return [this.form.topBeginDate, this.form.topEndDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.topBeginDate = value[0]
          this.form.topEndDate = value[1]
        } else {
          this.form.topBeginDate = ''
          this.form.topEndDate = ''
        }
      }
    },
    formatCertifyName() {
      return function (data) {
        if (data && data.length) {
          return data
            .map((_) => _.certifyName)
            .filter((_) => _)
            .join(',')
        }
        return ''
      }
    }
  },
  watch: {
    ruleForm: {
      handler(newVal) {
        // Update localRuleForm when ruleForm changes
        this.localRuleForm = { ...newVal }
      },
      deep: true
    }
  },
  created() {
    // Initialize localRuleForm with a copy of the ruleForm prop
    this.localRuleForm = { ...this.ruleForm }
  },
  activated() {
    // const me = this
    // setTimeout(() => {
    //   me.getList()
    //   me.showList = JSON.parse(localStorage.articleDisplayList || '[]')
    //   me.showList = me.showList.length ? me.showList : me.articleDisplayList
    //   console.log('article list activated')
    // })
  },
  methods: {
    clipboardSuccess(text) {
      // 复制
      this.$message.success('复制成功')
    },
    handleChange(val, data) {
      let obj = {
        status: val[0],
        essayId: data.id
      }
      if (val?.length > 1) {
        obj.message = val[1]
      }
      updateSingleEssayStatus(obj)
        .then((res) => {
          data.status = val[0]
          this.$message.success('操作成功')
          // this.getSearchList()
          // setTimeout(() => {
          //   this.getSearchList()
          // }, 800)
        })
        .catch((e) => {
          console.log(e)
        })
    },

    getOptions(status, certifyNames) {
      const arr = JSON.parse(JSON.stringify(this.options))
      if (status === 1 || status === 4) {
        const canEditStatus = status === 1 ? [3, 2, 0, 4] : [2]
        arr.forEach((item) => {
          if (!canEditStatus.includes(item.value)) {
            item.disabled = true
          }
        })
      } else if (status === 3 || status === 0) {
        //厂家成员,商家认证, 官方运营账户 可修改通过
        const people = [
          '厂家成员',
          '商家认证',
          '官方运营账户',
          '创作者 | L3',
          '创作者 | L4'
        ]
        console.log(certifyNames, '--')
        const specialPeople =
          certifyNames?.findIndex((_) => people.includes(_.certifyName)) != -1
        const canEditStatus =
          status === 3 ? (specialPeople ? [2, 0, 1] : [2, 0]) : [0]
        arr.forEach((item) => {
          if (!canEditStatus.includes(item.value)) {
            item.disabled = true
          }
          if (item.value === status) {
            delete item.children
            // item.children.forEach(child => {
            //   child.disabled = true
            // })
          }
        })
      }
      return arr
    },
    // 同步百家号
    Synchro(item) {
      const me = this
      recordBeforeAlter(item, 'id')
      // 调用百家号同步接口
      publishEssayToBJH({
        id: item.id,
        originUrl: `https://wap.58moto.com/details-article/${item.id}`
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('同步成功')
          item.syncStatus = '1' // 已同步状态
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 更新显示列表
    updateShowList(list) {
      this.showList = []
      const templateArr = this.articleList
      this.articleList = []
      this.showList = list
      this.articleList = templateArr
    },
    showH5Preview(item) {
      console.log(item)
      const url = `https://wap.corp.mddmoto.com/details-article/${item.id}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
      const tagId = ['1811', '7852'] // 车辆评测id
      const status = !!(
        (item.tagList &&
          item.tagList.find((_) => {
            return tagId.indexOf(_.id.toString()) > -1
          })) ||
        {}
      ).id
      const data = {
        showSwitch: status,
        id: status ? item.id : '',
        switchStatus: !!item.evaluationStickStatus,
        itemId: item.id,
        sourceType: 'C4CA4238A0B923820DCC509A6F75849B'
      }
      console.log(data)
      this.$refs.ChooseIframe.init(url, data)
      // this.$refs.ChooseDetail.init(data, url)
    },
    closeDialog() {
      $emit(this, 'closeDialog')
    },
    // 确认状态批处理
    modifyEssayStatus() {
      const me = this
      if (me.selectedList && me.selectedList.length > 0) {
        const idsArray = []
        me.selectedList.map((value) => {
          idsArray.push(value.id)
        })
        batchRecordBeforeAlter(me.selectedList, idsArray.join(','))
        updateEssayStatus({
          ids: idsArray.join(','),
          status: me.articleStatus
        }).then((response) => {
          if (response.data.code === 0) {
            me.$message.success('修改成功')
            $emit(me, 'closeDialog')
            me.articleList.map((article, index) => {
              me.selectedList.map((select) => {
                if (article.id === select.id) {
                  article.status = me.articleStatus
                }
              })
            })
          }
        })
      }
    },
    // 切换状态
    changeStatus(obj) {
      recordBeforeAlter(obj, 'id')
      updateEssayStatus({
        ids: obj.id + '',
        type: obj.type,
        status: obj.status
      }).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('修改成功')
          return
        }
        this.$message.error(response.data.msg)
      })
    },
    getPlatFormDesc(platform) {
      // console.log(platform)
      const desc = this.platformEnum[platform]
      // if (desc === 'Android' || desc === 'iOS') {
      //   desc = 'APP'
      // }
      return desc
    },
    getList(paramsObj) {
      const articleManageList = JSON.parse(
        sessionStorage['articleManageList'] || '{}'
      )
      const requestParams = {
        ...this.ruleForm,
        ...paramsObj,
        levelIds: undefined // 清除不必要的数组，否则会报错
      }

      if (articleManageList && articleManageList.page) {
        this.page = articleManageList.page
        requestParams.page = articleManageList.page
      } else {
        const temArr = deepCopy(requestParams)
        sessionStorage.setItem('articleManageList', JSON.stringify(temArr))
      }
      $emit(this, 'loadedState', true)
      if (!requestParams.authBeginTime && !requestParams.authEndTime) {
        delete requestParams.authBeginTime
        delete requestParams.authEndTime
      }

      const request = { ...requestParams }
      // 只看文章
      // if (this.ruleForm.allType === 1) {
      //   request.propertyType = 0;
      // }
      // 只看视频
      // if (this.ruleForm.allType === 4) {
      //   delete request.allType
      //   // request.propertyType = 1
      //   request.pcAndVideoFlag = 1
      // }
      const typeList = {
        1: 'essay_detail',
        2: 'moment_detail',
        4: 'video_detail'
      }
      request.type = request.allType ? typeList[request.allType] : ''
      delete request.allType
      delete request.tempFromSource
      delete request.carName
      delete request.autherList

      searchArticleList(request).then(
        async (response) => {
          if (response.data.code === 0) {
            const res = response.data.data.listData || []
            recordOldData(res)
            res.map((value, index) => {
              // select 组件 v-model 只接受bool类型
              value.boolPrime = !!value.prime
              value.boolTop = !!value.top
              value.pushRepoStatus = !!value.pushRepoStatus
            })
            const resp = res.length ? await getUserCertifyInfo(res) : []
            this.articleList = resp
            this.total = response.data.data.total
            // console.log(this.articleList)
          }
          $emit(this, 'loadedState', false)
        },
        (reject) => {
          // this.$message.error(reject.message || '获取信息失败')
          $emit(this, 'loadedState', false)
        }
      )
    },
    getSearchList() {
      this.page = 1
      this.getList({
        page: 1
      })
    },
    currentChange(page) {
      this.page = page
      const articleManageList = JSON.parse(
        sessionStorage['articleManageList'] || '{}'
      )
      articleManageList.page = page
      sessionStorage.setItem(
        'articleManageList',
        JSON.stringify(articleManageList)
      )
      this.getList({
        page: page
      })
    },
    sortSearchList(item) {
      // console.log(item)
      const modeEnum = {
        ascending: 1,
        descending: 0,
        '': null
      }
      // const orderEnum = {
      //   真实浏览量: 1,
      //   CTR: 2,
      //   生效时间: 3
      // }
      // const label = item.column && item.column.label
      const order = item.order
      this.sortableName =
        modeEnum[order] === 0
          ? '按CTR由高到低'
          : modeEnum[order] === 1
          ? '按CTR由低到高'
          : ''
      this.page = 1

      // Create a copy of the orderType and orderMode values
      const orderType = order ? 2 : null
      const orderMode = modeEnum[order]

      // Emit an event to update the parent component's ruleForm
      $emit(this, 'updateRuleForm', { orderType, orderMode })

      // Use the values directly in getList without modifying the prop
      this.getList({
        page: this.page,
        orderType: order ? 2 : null, // 排序类型 1.真实浏览量 2.ctr 3.生效时间
        orderMode: modeEnum[order] // 排序方式 0.倒序 1.正序
      })
    },
    rowClick(row) {
      // 选中当前行
      // this.$refs.articleList.toggleRowSelection(row)
      // console.log(this.selectedList)
    },
    rowDoubleClick(article, type) {
      if (article.transcodeStatus === 1) {
        this.$message.error('视频转码中不能编辑')
        return
      }
      // 编辑内容
      this.$router.push({
        name: 'EditArticle',
        query: {
          id: article.id,
          propertyType: article.propertyType || 0,
          editMes: type ? 1 : 0
        }
      })
    },
    handleSelectionChange(val) {
      this.selectedList = val
      // 向父组件传值
      $emit(this, 'sendData', this.selectedList)
    },
    // 查看日志记录
    auditRecords(data) {
      this.$refs.SeeLog.init(data.id)
    },
    // 变动推送库
    editPushData(data) {
      if (data.pushRepoStatus) return // 已经加入推送库，不能关闭
      this.activeData = data
      this.pushData = {
        pushTitle: '',
        pushContent: ''
      }
      this.showPushDialogStatus = true
    },
    // 关闭推送库
    closePushDialogStatus() {
      this.activeData = {}
      this.showPushDialogStatus = false
    },
    setPushTitle() {
      if (!this.pushData.pushTitle) {
        return this.$message.error('推送标题不能为空')
      }
      if (!this.pushData.pushContent) {
        return this.$message.error('推送内容不能为空')
      }
      const params = {
        essayId: this.activeData.id, // Integer	是		文章id
        ...this.pushData
      }
      setPushTitle(params).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('设置成功')
          this.showPushDialogStatus = false
          this.getList({
            page: this.page
          })
        } else {
          this.$message.error('设置失败')
        }
      })
    },
    //开启 | 关闭舆情
    changMonitor(row) {
      postOpinionMonitor({
        essayId: row.id,
        publicOpinionMonitor: row.publicOpinionMonitor
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
          } else {
            row.publicOpinionMonitor = !row.publicOpinionMonitor
            this.$message.error(res.data.msg || '操作失败')
          }
        })
        .catch((e) => {
          row.publicOpinionMonitor = !row.publicOpinionMonitor
          console.log(e)
        })
    },
    //评论管理
    clickComment(item) {
      this.$refs['commentManage'].init(item, 'essay_detail')
    }
  },
  emits: ['loadedState', 'sendData', 'closeDialog', 'updateRuleForm']
}
</script>

<style lang="scss" scoped>
.content {
  .el-tag--medium {
    margin-top: 5px;
    cursor: pointer;
  }
  .el-pagination {
    text-align: center;
    margin-top: 20px;
  }
  .is-yellow {
    border: 1px solid #ffea00;
  }
  .is-del {
    border: 1px solid #000;
  }
}

.circle-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .box {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px;
    .icon {
      width: 15px;
      height: 15px;
    }
  }
}

.content-foot {
  border: 1px solid #ebeef5;
  border-radius: 5px;
  margin: 5px;
  padding: 5px;
  &-box {
    display: flex;
    justify-content: space-between;
    padding: 2px;
    color: #909399;
  }
}
</style>
