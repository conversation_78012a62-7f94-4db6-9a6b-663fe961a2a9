<template>
  <el-form
    ref="ruleForm"
    :model="ruleForm"
    label-width="100px"
    class="edit-right-top"
  >
    <div
      v-if="showEditMes"
      class="right-top"
      :style="{ pointerEvents: disable ? 'none' : 'auto' }"
    >
      <selectedUser
        ref="selectedUser"
        :disable="disable"
        @updateUser="updateUser"
      />

      <showDisplay
        v-if="isEassayDetail"
        ref="showDisplay"
        :disable="disable"
        @updateOperateSize="updateOperateSize"
      />

      <el-form-item label="摩友圈" class="pd10">
        <div>
          <el-select
            v-model="selectHoop"
            :remote-method="searchHoopList"
            placeholder="请输入摩友圈名称"
            filterable
            value-key="hoopId"
            :loading="hooploading"
            remote
            clearable
            style="width: 150px"
            @change="changeHoop"
          >
            <el-option
              v-for="(item, index) in hoopList"
              :key="index"
              :label="item.hoopName"
              :value="item"
            />
          </el-select>
          <div>
            <el-tooltip
              v-for="(item, index) in ruleForm.hoop"
              :key="index"
              :content="item.hoop"
              :disabled="true"
              effect="dark"
              placement="top-start"
            >
              <el-tag closable @close="deleteLable(index, item)">{{
                $filters.subString(item.hoopName, 20)
              }}</el-tag>
            </el-tooltip>
          </div>
        </div>
      </el-form-item>

      <el-form-item v-if="isMomentDetail" label="关联实拍车型" class="pd10">
        <div>
          <span>车型品牌</span>

          <el-select
            v-model="brandId"
            :remote-method="(query) => searchBrandList(query, true)"
            placeholder="请输入品牌名称"
            filterable
            :loading="brandloading"
            remote
            clearable
            style="width: 150px"
            @change="changeBrand"
          >
            <el-option
              v-for="(item, index) in brandList"
              :key="index"
              :label="item.labelName"
              :value="item.labelId"
            />
          </el-select>

          <span>车型名称</span>

          <el-select
            v-model="goodsId"
            :remote-method="searchBrandList"
            placeholder="请输入车型名称"
            :loading="brandloading"
            filterable
            remote
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in carList"
              :key="item.goodsId"
              :label="item.goodsCarName"
              :value="item.goodsId"
            />
          </el-select>

          <el-button type="primary" link @click="cancleCar">
            取消关联
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="同步至电摩范" class="pd10">
        <div>
          <el-button
            type="primary"
            :disabled="ruleForm.syncEssayFlag"
            @click="showClassify = true"
          >
            {{ ruleForm.syncEssayFlag ? '已同步' : '同步' }}
          </el-button>
        </div>
        <el-dialog v-model="showClassify" title="选择电摩分类">
          <el-tag
            class="mb10 mr10 btn"
            v-for="(item, index) in classifyList"
            :key="index"
            :type="chooseClassifyList.includes(item.id) ? 'primary' : 'info'"
            @click="chooseClassify(item)"
          >
            {{ item.name }}
          </el-tag>
          <template #footer>
            <el-button @click="showClassify = false">取消</el-button>
            <el-button type="primary" @click="syncEMotorEssay"
              >确认同步</el-button
            >
          </template>
        </el-dialog>
      </el-form-item>
      <el-form-item
        v-if="['moment_detail'].includes(type)"
        label="转为问答"
        class="pd10"
      >
        <div>
          <el-button
            type="primary"
            :disabled="!!ruleForm.answerFlag"
            @click="convertEssayToQuestion"
          >
            {{ !!ruleForm.answerFlag ? '已转换' : '转换' }}
          </el-button>
        </div>
      </el-form-item>
    </div>

    <div v-else class="right-top is-border">
      <p>内容信息</p>

      <el-form-item label="发布者：" label-width="90px">
        <span>{{ ruleForm.author }}</span>
      </el-form-item>

      <el-form-item label="" label-width="90px">
        <el-tag
          v-for="(item, index) in ruleForm.certifyList"
          :key="index"
          :content="item.certifyName"
          style="margin-right: 10px"
        >
          {{ $filters.subString(item.certifyName, 20) }}
        </el-tag>
      </el-form-item>

      <el-form-item label="发布时间：" label-width="90px">
        <span>{{ ruleForm.actTime }}</span>
      </el-form-item>

      <el-form-item
        label="状态："
        label-width="90px"
        :style="{ pointerEvents: disable ? 'none' : 'auto' }"
      >
        <el-select disabled v-model="ruleForm.status" style="width: 120px">
          <el-option
            v-for="(value, index) in articleStatusEnum"
            :key="index"
            :label="index"
            :value="value"
            @click="updateStatus(value)"
          />
        </el-select>

        <span v-if="[1, 3].indexOf(transcodeStatus) > -1" class="red">
          {{ transcodeStatus === 1 ? '转码中' : '转码失败' }}
        </span>
      </el-form-item>

      <div
        class="validate-article"
        v-if="['essay_detail', 'video_detail'].includes(type)"
      >
        <el-form-item label="查重检测：" label-width="90px">
          <span>{{ articleMessage }}</span>

          <span v-if="illegalityArticle" v-html="illegalityArticle" />
        </el-form-item>
      </div>

      <el-form-item label="摩友圈：" class="pd10" label-width="90px">
        <el-tooltip
          v-for="(item, index) in ruleForm.hoop"
          :key="index"
          :content="item.hoop"
          :disabled="true"
          effect="dark"
          placement="top-start"
        >
          <el-tag>{{ $filters.subString(item.hoopName, 20) }}</el-tag>
        </el-tooltip>
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
import showDisplay from './show-display.vue'
import selectedUser from './selected-user.vue'
import {
  getClassifyEmotofine,
  syncEMotorEssay,
  validateArticle,
  validateVideo,
  getArticleCarInfo,
  updateEssayHoop,
  convertEssayToQuestion
} from '@/api/article'
import { getCircleList } from '@/api/circle'
import { searchCarList } from '@/api/garage'
import { articleStatusEnum } from '@/utils/enum'
export default {
  name: 'CEditRightTop',
  components: {
    showDisplay,
    selectedUser
  },
  props: {
    id: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: ''
    },
    editMes: {
      type: Boolean,
      default: false
    },
    disable: {
      type: Boolean,
      default: false
    },
    status: {
      type: Number,
      default: 0
    },
    isEassayDetail: {
      type: Boolean,
      default: false
    },
    isMomentDetail: {
      type: Boolean,
      default: false
    },
    transcodeStatus: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showClassify: false, // 是否显示电摩分类弹框
      showEditMes: false, // editMes 传值
      articleStatusEnum,
      ruleForm: {},
      showImgList: [], // 显示img图片
      articleMessage: '检测中...', // 检测文章的状态
      illegalityArticle: '', // 检测相似的文章
      brandList: [],
      carList: [],
      brandId: '',
      goodsId: '',
      brandloading: false,
      orginCarList: [],
      selectHoop: '',
      hooploading: false,
      hoopList: [], //选择摩友圈列表
      classifyList: [], //分类列表
      chooseClassifyList: [] //分类
    }
  },
  computed: {},
  mounted() {},
  methods: {
    init(data) {
      console.log(data, 'data')
      const me = this
      let userList = []
      let ruleForm = {}
      me.showEditMes = me.editMes
      if (data.author || data.authorId) {
        ruleForm.author = data.author
        userList = [
          {
            uid: data.authorId,
            username: data.author
          }
        ]
      }
      me.ruleForm = data
      me.ruleForm.hoop = data.hoop.slice(0, 1)
      if (me.showEditMes) {
        me.getArticleCarInfo()
        setTimeout(() => {
          me.$refs.selectedUser.setValue(ruleForm, userList)
          me.$refs.showDisplay && me.$refs.showDisplay.init(data)
        }, 100)
      } else {
        setTimeout(() => me.validateArticle(), 100) // 异步判断，因为 props.type 还没更新好
      }
      this.getClassify()
    },
    getClassify() {
      const types = {
        essay_detail: '1',
        video_detail: '2',
        moment_detail: '3'
      }
      getClassifyEmotofine({
        type: types[this.type] || '2',
        status: '1' // 0 失效 1有效
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.classifyList = response.data.data
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    chooseClassify(current) {
      if (this.chooseClassifyList.includes(current.id)) {
        this.chooseClassifyList = this.chooseClassifyList.filter(
          (id) => id !== current.id
        )
      } else {
        this.chooseClassifyList.push(current.id)
      }
    },
    syncEMotorEssay() {
      syncEMotorEssay({
        essayId: this.id,
        tagIdListStr: this.chooseClassifyList.join(',')
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.$message.success('同步成功')
            this.ruleForm.syncEssayFlag = true
            this.showClassify = false
          } else {
            this.$message.error(response.data.msg || '同步失败')
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getArticleCarInfo() {
      const me = this
      me.brandId = ''
      me.goodsId = ''
      getArticleCarInfo({
        businessId: me.id
      })
        .then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data || {}
            if (data.goodsId) {
              const params = {
                goodsId: data.goodsId,
                isOnStatus: 1,
                page: 1,
                limit: 10
              }
              searchCarList(params)
                .then((response) => {
                  if (response.data.code === 0) {
                    const result =
                      (response.data.data && response.data.data.list) || []
                    if (!result.length) {
                      me.brandList = []
                      me.carList = []
                      // me.orginCarList = []
                      return
                    }
                    this.setCarList(result, true)
                    this.setCarList(result, false)
                    setTimeout(() => {
                      me.brandId = data.brandId
                      me.goodsId = data.goodsId
                    }, 100)
                  }
                })
                .finally(() => {
                  me.brandloading = false
                })
            }
          }
        })
        .catch((e) => {
          console.log(e)
        })
    },
    // 更新用户
    updateUser(data) {
      this.ruleForm = {
        ...this.ruleForm,
        ...data,
        autherid: data.authorId
      }
      this.$emit('updateEdit')
    },
    // 更新图片展示逻辑
    updateOperateSize(data) {
      this.ruleForm = {
        ...this.ruleForm,
        ...data
      }
      this.$emit('updateEdit')
    },
    // 删除数据
    deleteLable(index, item) {
      console.log(index, item)
      this.ruleForm.hoop.splice(index, 1)
      // this.postData()
      this.$emit('updateEdit')
      this.updateCircle(true)
      this.selectHoop = ''
      console.log('需要发送数据')
    },

    //更新文章关联圈子
    updateCircle(isDel) {
      const params = {
        essayId: this.id,
        hoopIds: this.ruleForm.hoop.map((_) => _.hoopId).join(',')
      }
      updateEssayHoop(params)
        .then((result) => {
          if (result.data.code === 0) {
            this.$message.success(isDel ? '移除成功' : '添加成功')
          } else {
            this.$message.error(result.data.msg || '操作失败')
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },

    validateArticle() {
      const me = this
      if (!me.id) return
      me.articleMessage = '检测中...'
      me.illegalityArticle = ''

      const requestMethod =
        me.type === 'video_detail' ? validateVideo : validateArticle //   list = JSON.parse(response.data.data || '[]')
      requestMethod({
        articleId: me.id
      })
        .then((response) => {
          if (response.data.code === 0) {
            // 0 含相似
            let list = []
            try {
              list = Array.isArray(response.data.data)
                ? response.data.data
                : JSON.parse(response.data.data || '[]')
            } catch (e) {
              return (me.articleMessage = response.data.data)
            }
            me.articleMessage = '检测到相似文章'
            me.illegalityArticle =
              '，链接：' +
              list
                .map((_ = {}) => {
                  return `<a href='#/audit/EditArticle?id=${
                    _.article_id || _
                  }&propertyType=0&editMes=0' target='_black' style='color: #559ff8;'> "${
                    _.article_id || _
                  }" </a>`
                })
                .join('，')
            return
          }
          me.articleMessage = response.data.data
        })
        .catch((err) => {
          me.articleMessage = err.message
          console.log(err.message)
        })
    },
    convertEssayToQuestion() {
      if (!this.id) return this.$message.error('内容Id不能为空')
      convertEssayToQuestion({
        essayId: this.id
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.ruleForm.answerFlag = 1
            return this.$message.success('转换成功')
          }
          this.ruleForm.answerFlag = 0
          this.$message.error(response.data.msg || '转换失败')
        })
        .catch((err) => {
          this.ruleForm.answerFlag = 0
          // this.$message.error(`转换异常：${err.message}`)
        })
    },
    // 验证数据
    verificationData() {
      const me = this
      if (me.isMomentDetail) {
        me.ruleForm.goodsId = me.goodsId
      }
      me.$emit('updataData', me.ruleForm)
    },

    changeBrand() {
      // this.carList = this.orginCarList.filter((car) => car.brandId == val)
      // this.goodsId = ''
      this.searchBrandList('', false)
    },

    cancleCar() {
      this.brandId = ''
      this.goodsId = ''
    },

    searchBrandList(query, isBrand) {
      this.brandloading = true
      if (!isBrand && !this.brandId) {
        return this.$message.error('请先选择车型品牌')
      }
      this.$tools.debounce(() => this.getBrandList(isBrand, '', query), 300)()
    },
    // 获取品牌列表
    getBrandList(isBrand, id, query = '') {
      const me = this
      const params = {
        brandId: isBrand ? '' : me.brandId || '',
        [isBrand ? 'brand' : 'name']: query || '',
        isOnStatus: 1,
        page: 1,
        limit: 10
      }
      if (id) {
        params.goodsId = id
      }
      searchCarList(params)
        .then((response) => {
          if (response.data.code === 0) {
            const result = (response.data.data && response.data.data.list) || []
            if (!result.length) {
              me.brandList = []
              me.carList = []
              // me.orginCarList = []
              return
            }
            this.setCarList(result, isBrand)
          }
        })
        .finally(() => {
          me.brandloading = false
        })
    },

    setCarList(result, isBrand) {
      const me = this
      if (isBrand) {
        const brandList = []
        result.map(function (value) {
          const newObj = {
            labelName: value.brandName,
            labelId: value.brandId
          }
          if (!brandList.some((_) => _.labelId === newObj.labelId)) {
            brandList.push(newObj)
          }
        })
        me.brandList = brandList
      } else {
        const carList = []
        result.map(function (value) {
          const newGoods = {
            brandId: value.brandId,
            goodsCarName: value.goodName,
            goodsId: value.goodId
          }
          carList.push(newGoods)
        })

        me.carList = carList
      }
    },

    searchHoopList(query) {
      this.hooploading = true
      this.$tools.debounce(() => this.getHoopList(query), 300)()
    },
    getHoopList(query) {
      getCircleList({
        hoopName: query,
        page: 1,
        limit: 10
      })
        .then((response) => {
          if (response.status === 200) {
            const hoopNameList = []
            const result =
              response.data && response.data.data && response.data.data.list
            result.map(function (value) {
              const newObj = {
                hoopName: value.name,
                hoopId: value.id
              }
              hoopNameList.push(newObj)
            })
            this.hoopList = hoopNameList
          }
        })
        .finally(() => {
          this.hooploading = false
        })
    },
    changeHoop(val) {
      console.log(val, 'oooo')
      if (!val.hoopId) return
      // if (this.ruleForm.hoop.some((_) => _.hoopId === val.hoopId)) {
      //   this.$message.warning(`已有${val.hoopName}`)
      //   return
      // }
      // if (this.ruleForm.hoop.length >= 1) {
      //   return this.$message.warning('仅可关联一个圈子')
      // }
      this.ruleForm.hoop = []
      this.ruleForm.hoop.push(val)
      this.$emit('updateEdit')
      this.updateCircle(false)
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-right-top {
  .right-top {
    margin-top: 20px;
  }
  .is-border {
    border: 1px solid #999;
    padding: 20px;
    border-radius: 20px;
    margin: 20px 10px 5px;
    p {
      margin: 0;
    }
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
