<template>
  <div v-loading="loading" class="c-new-article components-new">
    <el-row :gutter="24">
      <el-col :span="14">
        <articleDetailLeft
          v-if="type === 'essay_detail'"
          :showAddShop="showAddShop"
          :disable="disable"
          @updataFirstData="updataFirstData"
          @updateEdit="updateEdit"
          @updateAddVideo="updateAddVideo"
          ref="articleDetailLeft"
        />
        <videoDetailLeft
          v-else-if="type === 'video_detail'"
          :disable="disable"
          @updataFirstData="updataFirstData"
          @updateEdit="updateEdit"
          ref="videoDetailLeft"
        />
        <dynamicDetailLeft
          v-else
          :disable="disable"
          @updataFirstData="updataFirstData"
          @updateEdit="updateEdit"
          ref="dynamicDetailLeft"
        />
        <vote
          v-if="voteInfo"
          :vote-info="voteInfo"
          style="margin-left: 20px"
        ></vote>
      </el-col>
      <el-col :span="10">
        <editRightTop
          v-if="id && !isNoStatus"
          :id="id"
          :type="type"
          :editMes="editMes"
          :disable="disable"
          :isEassayDetail="isEassayDetail"
          :isMomentDetail="isMomentDetail"
          ref="editRightTop"
          @updateEdit="updateEdit"
          @updataData="updataData"
        />
        <editRight
          v-if="!editMes"
          :id="id"
          :isNoStatus="isNoStatus"
          :editMes="editMes"
          :propertyType="propertyType"
          :isMomentDetail="isMomentDetail"
          ref="editRight"
          @updataData="updataData"
        />
      </el-col>
    </el-row>
    <div class="modules footer" v-if="showButton">
      <el-button class="confirm-data" @click="readyPostData()">发布</el-button>
      <el-button
        v-if="id && !isNoStatus"
        class="confirm-data"
        @click="showH5Preview()"
        >预览</el-button
      >
      <el-button @click="goBack()">取消</el-button>
    </div>
    <ChooseDetail ref="ChooseDetail" />
  </div>
</template>

<script>
const enumList = {
  essay_detail: 'articleDetailLeft',
  video_detail: 'videoDetailLeft',
  moment_detail: 'dynamicDetailLeft'
}
import articleDetailLeft from './article-detail-left.vue'
import videoDetailLeft from './video-detail-left.vue'
import dynamicDetailLeft from './dynamic-detail-left.vue'
import editRight from './edit-right.vue'
import editRightTop from './edit-right-top.vue'
import vote from './vote/index.vue'
import { ElLoading as Loading } from 'element-plus'
import { timeFullS } from '@/filters'
import { mapGetters } from 'vuex'
import { AddAnswer } from '@/api/questionModule'
import { updateBusinessEssay, getRickText, postUpdCarInfo } from '@/api/article'
import ChooseDetail from '@/components/CDetail/ChooseDetail.vue'
export default {
  provide() {
    return { articleDetailLeft: this }
  },
  name: 'CNewArticle',
  components: {
    articleDetailLeft,
    videoDetailLeft,
    dynamicDetailLeft,
    editRight,
    editRightTop,
    ChooseDetail,
    vote
  },
  props: {},
  data() {
    return {
      loading: true,
      showAddShop: false,
      editMes: false, // 编辑信息
      disable: false, // 关闭编辑
      showButton: true, // 底部按钮展示
      isMomentDetail: true, // 是不是动态详情
      isEassayDetail: true, // 是不是文章详情
      isNoStatus: false, // 爬虫列表过来的
      editDataStatus: false, // 是否编辑过信息
      isPostDataStatus: false, // 发送中
      propertyType: 0, // 1=pgc内容 0=普通文章内容
      id: 0,
      ruleFrom: {},
      type: '',
      typeList: {
        0: 'essay_detail',
        1: 'video_detail',
        2: 'moment_detail'
      },
      transcodeStatus: 0, // 1=转码中 2=转码成功 3=转码失败  转码失败清除视频信息
      addVideo: false, // 是否存在新增视频
      voteInfo: null
    }
  },
  activated() {
    this.initData()
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  methods: {
    // 查询
    initData() {
      this.loading = true
      const me = this
      const name = me.$route.name
      me.isPostDataStatus = false
      me.id = parseInt(me.$route.query.id || 0)
      me.editMes = parseInt(me.$route.query.editMes) === 1 || false
      me.disable = parseInt(me.$route.query.editMes) === 0 && !!me.id
      if (name !== 'createArticle') return
      me.propertyType = parseInt(me.$route.query.propertyType || 0) // 1=pgc内容 0=普通文章内容
      me.type = me.typeList[me.propertyType] // 这段貌似有问题，type不是根据propertyType来的
      setTimeout(() => (me.loading = false), 1000)
      setTimeout(() => {
        me.$refs[enumList[me.type]].reset()
        me.$refs.editRight.reset()
      }, 100)
    },
    async getHtml(url) {
      const res = await getRickText(url)
      if (res.status === 200) {
        return res.data
      }
      return ''
    },
    // 设置data
    async init(data) {
      const me = this
      me.loading = false
      const query = me.$route.query || {}
      me.type = data.type
      me.propertyType = parseInt(data.propertyType)
      me.ruleFrom = data
      me.transcodeStatus = data.transcodeStatus
      me.addVideo = false
      me.disable = !(
        ((data.status === 1 || data.status === 3) && me.editMes) ||
        ((!data.status || [2, 9].includes(data.status)) && query.noStatus)
      )
      me.showButton =
        data.status === 1 ||
        (me.editMes && data.status === 3) ||
        ((!data.status || [2, 9].includes(data.status)) && query.noStatus)
      me.isMomentDetail = me.type === 'moment_detail'
      me.isEassayDetail = me.type === 'essay_detail'
      if (me.type) {
        let html = ''
        if (me.isEassayDetail) {
          html = await this.getHtml(data.webHtmlDownloadUrl)
        }
        const firstData = {
          html: html,
          content: data.content,
          title: data.title,
          spareTitle: data.spareTitle,
          shortContent: data.shortContent,
          id: data.id,
          essayPicRelVOList: data.essayPicRelVOList,
          carScoreFollowVO: data.carScoreFollowVO
        }
        this.voteInfo = data.voteInfo

        setTimeout(() => {
          // console.log(this.type, 99)
          this.$refs[enumList[this.type]].init(firstData)
        }, 10)
      }
      me.$refs.editRightTop.init({
        actTime: timeFullS(data.actTime || ''),
        author: data.author,
        authorId: data.authorId,
        answerFlag: data.answerFlag,
        certifyList: data.certifyList,
        hoop: data.hoop,
        status: data.status,
        mediaInfo: data.mediaInfo,
        essayPicRelVOList: data.essayPicRelVOList,
        operateType: data.operateType,
        operateSize: data.operateSize,
        syncEssayFlag: data.syncEssayFlag
      }) // 展示用户数据
      if (!me.editMes) {
        me.$refs.editRight.init(data)
      }
      if (me.$route.query && me.$route.query.noStatus) {
        me.editMes = false
        me.isNoStatus = true
        setTimeout(() => {
          me.$refs.editRight.disable = false
        }, 100)
      }
    },
    readyPostData() {
      this.$refs[enumList[this.type]].verificationData()
    },
    updataFirstData(data) {
      this.ruleFrom = {
        ...this.ruleFrom,
        ...data
      }
      if (this.id && this.editMes) {
        return this.$refs.editRightTop.verificationData()
      }
      this.$refs.editRight.verificationData()
    },
    updataData(data) {
      const me = this
      const postData = {
        ...me.ruleFrom,
        ...data,
        platform: '5',
        authUid: me.uid,
        authUser: me.name
      }
      postData.actTime = timeFullS(postData.actTime || '')
      if (me.id && me.editMes) {
        // 有id且编辑详情时
        postData.goodsList = postData.goodsList.map((item) => {
          return {
            ...item,
            goodsId: item.goodsId || item.id,
            channel: item.channel || item.goodsType
          }
        })
        if (me.isMomentDetail) {
          postUpdCarInfo({
            businessId: me.id,
            goodsId: postData.goodsId,
            hoopId: postData?.hoop[0]?.hoopId || ''
          })
            .then((res) => {
              if (res.data.code === 0) {
                console.log(res.data.message)
              }
            })
            .catch((e) => console.log(e))
        }
      }
      if (me.id) {
        postData.id = me.id
        postData.autherid = postData.authorId
        postData.editEassyType = me.editMes ? 0 : 1
        postData.goodsList = Array.isArray(postData.goodsList)
          ? JSON.stringify(postData.goodsList)
          : postData.goodsList
      }
      if (postData.hoop) {
        postData.hoopIds = postData.hoop.map((_) => _.hoopId).join(',')
      }
      delete postData.mediaInfo
      delete postData.carList
      delete postData.certifyList
      delete postData.contentTypeList
      delete postData.labelList
      delete postData.topics
      delete postData.hoop
      delete postData.certifyBrandList
      delete postData.tbGoodsList
      delete postData.jdGoodsList
      delete postData.zyGoodsList
      if (this.type === 'essay_detail') {
        delete postData.content
      }
      me.postNewData(postData)
    },
    // 新增或编辑文章
    postNewData(data) {
      const me = this

      // 验证视频是否上传完成
      // let videoUploading = false
      // content.map(_ => {
      //   if (_.type == '6') {
      //     if (_.link.includes('blob:')) {
      //       videoUploading = true
      //     }
      //     videoArr.push(_.id)
      //   }
      // })
      // if (videoUploading) {
      //   return me.$message.error('视频上传中，请稍后再试')
      // }

      // 1=转码中 2=转码成功 3=转码失败  转码失败清除视频信息
      // 转码失败 或 新增视频，传fileIds
      data.fileIds =
        data.fileIds ||
        ((me.transcodeStatus === 3 || me.addVideo) &&
          data.videoIds
            .filter((_) => _)
            .join(',')
            .replace(/^,+|,+$/gi, '')) ||
        ''
      console.log('data.fileIds', data.fileIds)

      if (me.isPostDataStatus) {
        return me.$message('正在发送中，请稍后')
      }
      me.isPostDataStatus = true
      const loadingInstance = Loading.service({
        fullscreen: true,
        text: '处理中'
      })
      me.editDataStatus = !me.editMes ? true : me.editDataStatus
      if (!me.editDataStatus) {
        loadingInstance.close()
        return me.goBack()
      }
      const url = me.id && !me.isNoStatus ? updateBusinessEssay : AddAnswer
      if (!me.editMes && !data.operateContent && me.type === 'essay_detail') {
        data.operateContent =
          me.$refs.editRight?.$refs.showDisplay?.getOperateContent?.() || ''
      }
      if (
        me.id &&
        !me.isNoStatus &&
        !data.operateContent &&
        me.type === 'essay_detail'
      ) {
        data.operateContent =
          me.$refs.editRightTop?.$refs.showDisplay?.getOperateContent?.() || ''
      }
      if (me.isNoStatus) {
        data.crawlerId = data.id
        delete data.id
      }

      url(data)
        .then((response) => {
          me.isPostDataStatus = false
          loadingInstance.close()
          if (response.data.code === 0) {
            me.$message.success({
              message: '发布成功',
              duration: 5000
            })
            me.$store.dispatch('setPageReloadStatus', true)
            me.$router.go(-1)
          }
        })
        .catch((e) => {
          me.isPostDataStatus = false
          // me.$message.error({
          //   showClose: true,
          //   message: e.message,
          //   duration: 0
          // })
          loadingInstance.close()
        })
    },
    // 取消
    goBack() {
      this.$router.go(-1)
    },
    // 更新详情变更状态
    updateEdit() {
      this.editDataStatus = true
    },
    updateAddVideo(add = false) {
      this.addVideo = add
      console.log('this.addVideo', this.addVideo)
    },
    showH5Preview() {
      const data = {
        itemId: this.id,
        sourceType: 'C4CA4238A0B923820DCC509A6F75849B'
      }
      const url = `https://wap.corp.mddmoto.com/details-article/${this.id}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
      this.$refs.ChooseDetail.init(data, url)
    }
  }
}
</script>

<style lang="scss" scoped>
.c-new-article {
  .footer {
    position: fixed;
    width: 40%;
    margin: 0 20% 0 40%;
    bottom: 20px;
    border-bottom: 0;
    text-align: center;
    padding: 10px;
    .confirm-data {
      background-color: #409eff;
      color: #fff;
    }
    .confirm-data:hover {
      background-color: #fff;
      color: #409eff;
    }
  }
}
</style>
