<template>
  <div class="content">
    <h3>驾考订单明细</h3>
    <el-form :inline="true" :model="ruleForm">
      <el-form-item label="订单号">
        <el-input
          v-model="ruleForm.orderNumber"
          type="text"
          placeholder="请输入订单号"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="会员类型">
        <el-select v-model="ruleForm.memberType" clearable>
          <el-option
            v-for="(key, value) in drivingTestVipEnum"
            :key="key"
            :label="key"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID">
        <el-input
          v-model="ruleForm.uid"
          type="text"
          placeholder="请输入用户ID"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <SeletedUser
        ref="selectUser"
        :name="'用户名'"
        :placeholder="'请输入用户名'"
        @sendData="setUid"
      />
      <el-form-item label="订单状态">
        <el-select v-model="ruleForm.status" clearable>
          <el-option
            v-for="(key, value) in orderStatus"
            :key="key"
            :label="key"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="系统">
        <el-select v-model="ruleForm.platform" clearable>
          <el-option
            v-for="(key, value) in platforms"
            :key="key"
            :label="key"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付时间" label-width="100px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="payTimeData"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button @click="initGetList">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="typeList" height="60vh" style="width: 100%" border>
      <el-table-column prop="orderNumber" label="订单号" align="center" />
      <el-table-column prop="value" label="会员类型" align="center">
        <template #default="{ row }">
          {{ drivingTestVipEnum[row.memberType] }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="金额" align="center" />
      <el-table-column prop="uid" label="用户ID" align="center" />
      <el-table-column prop="username" label="用户名" align="center" />
      <el-table-column prop="value" label="创单时间" align="center">
        <template #default="{ row }">
          {{ $filters.timeFullS(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="value" label="支付时间" align="center">
        <template #default="{ row }">
          {{ $filters.timeFullS(row.payTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="value" label="订单状态" align="center">
        <template #default="{ row }">
          {{ orderStatus[row.status] }}
        </template>
      </el-table-column>
      <el-table-column prop="payType" label="支付渠道" align="center">
        <template #default="{ row }">
          {{ payType[row.payType] }}
        </template>
      </el-table-column>
      <el-table-column label="系统" align="center">
        <template #default="{ row }">
          {{ platforms[row.platform] }}
        </template>
      </el-table-column>
      <el-table-column prop="value" label="操作" align="center">
        <template #default="{ row }">
          <el-button
            v-if="[4].includes(row.status)"
            type="primary"
            link
            @click="refund(row)"
            >退款</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="20"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="text-align: center; margin-top: 10px"
      @size-change="currentChange"
      @current-change="currentChange"
    />
    <PopRefund ref="popRefundRef" @updateList="getList" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
import PopRefund from './components/pop-refund.vue'
import { getDriverExamOrderList } from '@/api/driveTest'
import { drivingTestVipEnum } from '@/utils/enum/index'

const page = ref(1)
const total = ref(0)
const selectUser = ref(null)
const popRefundRef = ref(null)

const orderStatus = {
  '-4': '已取消',
  0: '待支付',
  4: '支付完成',
  10: '已退款'
}

const platforms = {
  1: '安卓',
  2: 'iOS',
  3: '鸿蒙'
}

const payType = {
  0: '未知',
  1: '微信支付',
  2: '支付宝支付',
  62: '支付宝（收钱吧）',
  61: '微信（收钱吧）',
  5: 'ApplePay'
}

const ruleForm = ref({
  orderNumber: '',
  uid: '',
  status: '',
  payBeginTime: '',
  payEndTime: '',
  memberType: '',
  platform: ''
})

onMounted(() => {
  getList()
})

const payTimeData = computed({
  get() {
    if (ruleForm.value.payBeginTime && ruleForm.value.payEndTime) {
      return [ruleForm.value.payBeginTime, ruleForm.value.payEndTime]
    }
    return []
  },
  set(value) {
    if (value) {
      ruleForm.value.payBeginTime = value[0]
      ruleForm.value.payEndTime = value[1]
    } else {
      ruleForm.value.payBeginTime = ''
      ruleForm.value.payEndTime = ''
    }
  }
})

const typeList = ref([])

const initGetList = () => {
  ruleForm.value = {
    orderNumber: '',
    uid: '',
    status: '',
    payBeginTime: '',
    payEndTime: '',
    platform: ''
  }
  selectUser.value.clearData()
  getList(1)
}

const getList = () => {
  getDriverExamOrderList({
    page: page.value,
    limit: 20,
    ...ruleForm.value
  })
    .then((res) => {
      if (res.data.code == 0) {
        typeList.value = res.data.data.listData || []
        total.value = res.data.data.total || 0
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

const refund = (item) => {
  popRefundRef.value.init({
    isApple: item.payType === 5,
    orderNumber: item.orderNumber,
    refundAmount: item.price // 退款金额
  })
}

const setUid = (val) => {
  ruleForm.value.uid = val
}

const currentChange = (val) => {
  page.value = val
  getList(page)
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}
</style>
