<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    :title="dialogTitle"
    append-to-body
    width="600px"
  >
    <p v-if="giftFlag" style="color: red; margin-top: 10px">
      商品存在赠品，退货时请告知用户需要归还赠品！
    </p>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      style="margin-top: 10px"
      @submit.prevent
    >
      <el-form-item label="售后类型:">
        <el-radio
          v-model="ruleForm.operateType"
          :disabled="!operateTypes.includes(TYPE_LIST.ONE)"
          :label="TYPE_LIST.ONE"
          >退货</el-radio
        >
        <el-radio
          v-model="ruleForm.operateType"
          :disabled="!operateTypes.includes(TYPE_LIST.TWO)"
          :label="TYPE_LIST.TWO"
          >换货</el-radio
        >
        <el-radio
          v-model="ruleForm.operateType"
          :disabled="!operateTypes.includes(TYPE_LIST.FOUR)"
          :label="TYPE_LIST.FOUR"
          @change="operateTypeChange"
          >仅退款</el-radio
        >
      </el-form-item>
      <el-form-item label="退款件数:">
        <el-input
          :min="0"
          v-model="ruleForm.applyNum"
          :disabled="ruleForm.operateType == TYPE_LIST.FOUR"
          placeholder="退款件数"
          type="number"
          style="width: 200px"
          @input="afterCount"
        />&nbsp;&nbsp;
        <span>最多可售后件数：{{ leftCount }}</span>
      </el-form-item>
      <el-form-item
        label="换新商品:"
        v-if="ruleForm.operateType == TYPE_LIST.TWO"
      >
        <el-select v-model="ruleForm.exchangeSkuid" style="width: 200px">
          <el-option
            v-for="item in exchangeSkuidList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="退款类型:"
        v-if="ruleForm.operateType == TYPE_LIST.FOUR"
      >
        <el-select
          v-model="ruleForm.refundType"
          style="width: 200px"
          @change="refundChange"
        >
          <el-option
            v-for="(value, index) in typeList"
            :key="index"
            :label="index"
            :value="value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="退款金额:"
        v-if="ruleForm.operateType != TYPE_LIST.TWO"
      >
        <el-input
          :min="0"
          :max="leftPrice"
          v-model="ruleForm.refundPrice"
          placeholder="退款金额"
          type="number"
          style="width: 200px"
          @input="afterPrice"
        />&nbsp;&nbsp;
        <span
          >{{
            ruleForm.operateType != TYPE_LIST.FOUR ? '剩余' : '最多'
          }}可退金额：{{ leftPrice }}</span
        >
      </el-form-item>
      <el-form-item label="退还能量:" v-if="ruleForm.operateType != 2">
        <el-input
          :disabled="ruleForm.operateType !== TYPE_LIST.FOUR"
          v-model="ruleForm.returnEnergy"
          type="number"
          :min="0"
          :max="leftEnergy"
          placeholder="请输入退还能量"
          style="width: 200px; display: inline-block"
        />&nbsp;&nbsp;
        <span>最多可退能量：{{ leftEnergy }}</span>
      </el-form-item>
      <el-form-item label="申请原因:">
        <el-input
          v-model="ruleForm.applyReason"
          placeholder="申请原因"
          type="textarea"
          :rows="4"
          style="width: 320px"
        />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <div class="text-center">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import {
  postAfterSale,
  GetNewGoods,
  postSelfRefundApply,
  postSelfExchangeRefundApply
} from '@/api/shop'
const TYPE_LIST = {
  ONE: 1,
  TWO: 2,
  FOUR: 4
}
export default {
  name: 'afterSale',
  props: {
    //换货后退款
    isChange: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      reqStatus: false,
      onlyShowStatus: false, // 是否仅展示
      ruleForm: {
        orderNum: '',
        skuid: '',
        operateType: '',
        applyNum: '',
        refundPrice: '',
        applyReason: '',
        returnEnergy: '',
        customerExpect: '',
        refundType: ''
      },
      customerExpectList: {
        1: '退货',
        2: '换货'
      },
      typeList: {
        发货前退款: 2,
        商品拒收退款: 3,
        商品差价补偿: 4,
        // 售后邮寄费补偿: 5,
        其他原因: 6
      },
      operateTypes: [], //可选button
      leftPrice: '',
      leftCount: '',
      leftEnergy: '',
      saveLeftPrice: '', //暂时保存的价格
      saveLeftCount: '', //暂时保存的件数
      dialogTitle: '申请售后',
      exchangeSkuid: '', //换货订单
      oldAfterSaleNum: '', //换货后退款当前售后单号
      giftFlag: 0, //是否赠品
      TYPE_LIST
    }
  },
  methods: {
    showData(data, title) {
      this.dialogVisible = true
      if (data) {
        console.log(data)
        this.dialogTitle = title || '申请售后'
        this.ruleForm.orderNum = this.$route.query.orderNum || ''
        this.ruleForm.skuid = data.skuid || ''
        this.leftPrice = data.leftPrice || ''
        this.saveLeftPrice = data.leftPrice || ''
        this.saveLeftCount = data.leftCount || ''
        this.leftCount = data.leftCount || ''
        this.leftEnergy = data.leftEnergy || ''
        this.operateTypes = data.operateTypes || []
        this.exchangeSkuid = data.skuid || ''
        this.oldAfterSaleNum = data.oldAfterSaleNum || ''
        this.giftFlag = data.giftFlag || 0
        //商品差价补偿 仅发货后可选
        this.typeList = {
          发货前退款: 2,
          商品拒收退款: 3,
          商品差价补偿: 4,
          // 售后邮寄费补偿: 5,
          其他原因: 6
        }
        if ([1, 2].includes(data.orderStatus)) {
          delete this.typeList['商品差价补偿']
        }
        // for (let i in this.customerExpectList) {
        //   if (this.operateTypes.includes(Number(i))) {
        //     delete this.customerExpectList[i]
        //   }
        // }
        this.ruleForm.operateType = this.operateTypes[0] || ''
        if (this.ruleForm.operateType === this.TYPE_LIST.FOUR) {
          this.ruleForm.applyNum = 0
        }
        this.getNewGoods(data.goodsId || '')
      }
    },

    afterCount() {
      if (this.ruleForm.refundType === 5) {
        return
      }
      if (this.ruleForm.applyNum > Number(this.leftCount)) {
        this.ruleForm.applyNum = this.leftCount
      }
      if (this.ruleForm.applyNum == this.leftCount) {
        this.ruleForm.refundPrice = this.leftPrice
        //仅退款下需手动填写
        if (this.ruleForm.operateType !== this.TYPE_LIST.FOUR) {
          this.ruleForm.returnEnergy = this.leftEnergy
        }
        return
      }
      //仅退款下需手动填写
      if (this.ruleForm.operateType !== this.TYPE_LIST.FOUR) {
        this.ruleForm.returnEnergy = parseInt(
          this.ruleForm.applyNum * (this.leftEnergy / this.leftCount)
        )
      }
    },
    afterPrice() {
      if (this.ruleForm.refundType === 5) {
        return
      }
      if (this.ruleForm.refundPrice > Number(this.leftPrice)) {
        this.ruleForm.refundPrice = this.leftPrice
      }
    },

    refundChange(val) {
      //邮费最高可退100
      if (val === 5) {
        this.leftPrice = ''
        this.leftCount = ''
      } else {
        this.leftCount = this.saveLeftCount
        this.leftPrice = this.saveLeftPrice
      }
    },
    getNewGoods(goodsId) {
      const me = this
      if (!this.operateTypes.includes(TYPE_LIST.TWO)) return
      GetNewGoods({ goodsId })
        .then((result) => {
          if (result.data.code === 0) {
            const data = result.data.data || []
            me.exchangeSkuidList = data.map((item, index) => {
              return {
                label: `${item.skuColor};${item.skuSize}(库存：${item.inventory})`,
                value: item.skuid
              }
            })
          } else {
            me.$message.error(result.data.msg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },

    // 发送数据
    confirm(type) {
      const me = this

      if (!me.ruleForm.operateType) {
        return me.$message.error('请选择售后类型')
      }
      if (
        !me.ruleForm.applyNum &&
        this.ruleForm.operateType != this.TYPE_LIST.FOUR
      ) {
        return me.$message.error('请输入退款件数')
      }
      if (
        me.ruleForm.applyNum &&
        me.ruleForm.applyNum > me.leftCount &&
        this.ruleForm.refundType !== 5 &&
        this.ruleForm.refundType !== 4
      ) {
        return me.$message.error('不可超过最多可售件数')
      }
      if (this.ruleForm.operateType !== TYPE_LIST.TWO) {
        if (!me.ruleForm.refundPrice) {
          return me.$message.error('请输入退款金额')
        }
        if (
          me.ruleForm.refundPrice &&
          me.ruleForm.refundPrice > me.leftPrice &&
          this.ruleForm.refundType !== 5
        ) {
          return me.$message.error('不可超过剩余可退金额')
        }
      }
      if (
        !this.ruleForm.refundType &&
        this.ruleForm.operateType == TYPE_LIST.FOUR
      ) {
        return me.$message.error('请选择退款类型')
      }
      if (this.ruleForm.operateType == TYPE_LIST.FOUR) {
        if (me.ruleForm.refundEnergy > Number(me.leftEnergy)) {
          return me.$message.error('不可超过剩余可退能量')
        }
      }

      if (!me.ruleForm.applyReason) {
        return me.$message.error('请输入申请原因')
      }
      const params = {
        ...me.ruleForm
      }
      if (this.ruleForm.applyNum == me.leftCount) {
        params.refundPrice = me.leftPrice
      }
      if (this.ruleForm.operateType == TYPE_LIST.FOUR) {
        return me.refundPrice()
      }
      if (me.isChange) {
        params.exchangeSkuid = me.exchangeSkuid
        params.oldAfterSaleNum = me.oldAfterSaleNum
      }
      const postData = me.isChange ? postSelfExchangeRefundApply : postAfterSale
      postData(params)
        .then((response) => {
          me.reqStatus = false
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            $emit(me, 'success')
            me.handleClose()
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          me.reqStatus = false
        })
    },

    //仅退款
    refundPrice() {
      const me = this
      postSelfRefundApply({
        refundType: me.ruleForm.refundType,
        refundCount: me.ruleForm.applyNum || 0,
        refundPrice: me.ruleForm.refundPrice || 0,
        ossRefundReason: me.ruleForm.applyReason,
        refundEnergy: me.ruleForm.returnEnergy,
        orderNum: me.ruleForm.orderNum,
        skuid: me.ruleForm.skuid
      })
        .then((response) => {
          me.reqStatus = false
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            $emit(me, 'success')
            me.handleClose()
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          me.reqStatus = false
        })
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      Object.assign(this.$data.ruleForm, this.$options.data().ruleForm)
    },
    operateTypeChange(val) {
      //仅退款的下 退货数量为0
      if (val === this.TYPE_LIST.FOUR) {
        this.ruleForm.applyNum = 0
      }
    }
  },
  emits: ['success']
}
</script>

<style lang="scss">
.no-margin {
  margin: 0;
}
.log-content {
  margin: 10px 0 15px;
}
.log-color {
  color: #999;
}
</style>
