<template>
  <div class="shopList" style="padding: 10px">
    <p>基本信息</p>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :inline="true"
      style="margin-top: 10px"
    >
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="订单号:">
            <span>{{ detail.orderNum }}</span> </el-form-item
          ><br />
          <el-form-item label="下单时间:">
            <span>{{
              $filters.timeFullS(detail.orderTime)
            }}</span> </el-form-item
          ><br />
          <el-form-item label="支付时间:">
            <span>{{ $filters.timeFullS(detail.payTime) }}</span> </el-form-item
          ><br />
          <el-form-item label="买家备注:">
            <span>{{ detail.customerRemark || '' }}</span> </el-form-item
          ><br />
          <el-form-item v-if="detail.saleMode == 2" label="尾款最晚支付时间:">
            <span>{{ $filters.timeFullS(detail.lastPayInternal) }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单状态:">
            <span>{{ orderStatusAll[detail.orderStatus] }}</span>
            <el-button
              v-if="[2].indexOf(detail.orderStatus) > -1"
              size="small"
              class="ml10"
              @click="sendLogistics()"
              >确认发货</el-button
            >
            <el-button
              v-if="[2].indexOf(detail.orderStatus) > -1"
              size="small"
              class="ml10"
              @click="cancleOrder()"
              >取消订单</el-button
            >
            <el-button
              v-if="[3, 4].indexOf(detail.orderStatus) > -1"
              size="small"
              @click="seeMore()"
              >查看物流信息</el-button
            > </el-form-item
          ><br />
          <el-form-item label="收货人:">
            <span>{{
              detail.deliveryAddress && detail.deliveryAddress.name
            }}</span>
            <span>{{
              detail.deliveryAddress && detail.deliveryAddress.mobile
            }}</span>
            <el-button
              v-if="detail.deliveryAddress && detail.deliveryAddress.mobile"
              size="small"
              style="margin-left: 10px"
              @click="seeMobile(detail.deliveryAddress.mobile)"
              >查看电话</el-button
            > </el-form-item
          ><br />
          <el-form-item label="配送地址:">
            <span>{{ detail.deliveryAddressContent }}</span>
            <el-button
              v-if="[1, 2].indexOf(detail.orderStatus) > -1"
              type="primary"
              @click="updateAddress(detail)"
              >修改邮寄地址</el-button
            > </el-form-item
          ><br />
          <el-form-item label="卖家备注:">
            <span>{{ detail.sellerRemark }}</span>
            <el-button
              @click="changeRemark"
              :icon="IconEditOutline"
              type="primary"
              link
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="支付方式:">
            <span>{{ detail.payType }}</span> </el-form-item
          ><br />
          <el-form-item v-if="detail.installmentNum" label="平台贴息:">
            <span>{{
              `${detail.installmentNum || ''}期+利息${detail.interest || ''}`
            }}</span> </el-form-item
          ><br v-if="detail.installmentNum" />
          <el-form-item label="邮费:">
            <span>{{ detail.freight }}</span> </el-form-item
          ><br />
          <el-form-item label="uid:">
            <span>{{ detail.uid || '' }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>商品信息</p>
    <el-table
      ref="shopList"
      :data="detail.skuInfo"
      highlight-current-row
      row-key="shopList"
      style="overflow-y: auto"
      border
    >
      <el-table-column
        prop="firstImg"
        label="商品图片"
        align="center"
        width="100px"
      >
        <template v-slot="scope">
          <span class="kill-active">
            <span v-if="scope.row.giftFlag" style="padding: 3px 5px">赠品</span>
          </span>
          <img
            :src="scope.row.firstImg"
            alt
            style="display: inline-block; width: 60px; height: 60px"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="skuid"
        label="SKUID"
        align="center"
        width="100px"
      />
      <el-table-column
        prop="skuName"
        label="商品名"
        align="center"
        width="400px"
      />
      <el-table-column
        prop="skuUnitPrice"
        label="商品单价"
        align="center"
        width="100px"
      >
        <template v-slot="scope">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <span>{{ scope.row.skuUnitPrice }}</span>
            <template v-if="scope.row.giftFlag">
              <sapn>0</sapn>
            </template>
            <template v-else>
              <el-icon
                v-if="
                  detail.orderStatus == 1 &&
                  !scope.row.energyDiscountPrice &&
                  scope.row.modifyPriceStatus !== 1
                "
                class="el-icon-edit"
                @click="showChangeDiag(scope.row.skuid)"
              >
                <IconEditOutline />
              </el-icon>
            </template>
          </div>
          <!-- {改价审核中} -->
          <div style="color: red" v-if="scope.row.modifyPriceStatus === 1">
            审核中单价：{{ scope.row.modifyPrice }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="purchasePrice"
        label="采购单价"
        align="center"
        width="100px"
      />
      <el-table-column
        prop="buyCount"
        label="购买份数"
        align="center"
        width="100px"
      />
      <el-table-column
        prop="couponPrice"
        label="优惠券抵扣"
        align="center"
        width="100px"
      />
      <el-table-column
        prop="energyDiscountPrice"
        label="能量抵扣金额"
        align="center"
        width="120px"
      />

      <el-table-column
        prop="refundStatus"
        label="退款状态"
        align="center"
        width="120px"
      >
        <template v-slot="scope">
          <span>{{ refundStatusAll[scope.row.refundStatus] || '' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="hasAfterSale"
        label="是否售后"
        align="center"
        width="100px"
      >
        <template v-slot="scope">
          <el-button
            v-if="scope.row.hasAfterSale"
            size="small"
            @click="goNext(scope.row)"
            >已售后</el-button
          >
          <span v-else>未售后</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="afterSaleFinishedCount"
        label="操作"
        align="center"
        width="120px"
      >
        <template v-slot="scope">
          <!-- <el-button type="primary" link v-if="scope.row.showOssRefundBtn == 1" @click="goRefundDetail(scope.row)">申请退款</el-button> -->
          <el-button
            type="primary"
            link
            v-if="scope.row.showApplyButton == 1"
            @click="goAfterSale(scope.row)"
            >申请售后</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <p class="order-price">
      商品：￥{{ detail.goodsTotalPrice }}&ensp;&ensp;&ensp;&ensp;
      <span v-if="detail.saleMode == 2"
        >预付款：{{ detail.preSalePrice || 0 }} &ensp;&ensp;&ensp;&ensp;</span
      >
      邮费：{{ detail.freight }}&ensp;&ensp;&ensp;&ensp;能量抵扣总金额:{{
        detail.energyDiscountPrice ? '-' + detail.energyDiscountPrice : 0
      }}
      &ensp;&ensp;&ensp;&ensp; 优惠券：
      {{ detail.totalCouponPrice && '-' + detail.totalCouponPrice }}&ensp;&ensp;
      &ensp;&ensp;订单总额：￥{{ detail.totalPrice }}
    </p>
    <p class="order-price">售后总金额：￥{{ detail.refundTotalPrice }}</p>
    <p>日志信息</p>
    <p>
      <el-input
        v-focus
        v-model="operateDetail"
        placeholder="请输入订单日志，按回车确认"
        type="text"
        style="width: 600px"
        clearable
        @change="postData()"
      />
    </p>
    <el-table
      ref="logList"
      :data="logList"
      highlight-current-row
      row-key="logList"
      style="height: 40vh; overflow-y: auto"
      max-height="40vh"
      border
    >
      <el-table-column
        prop="userName"
        label="操作人"
        align="center"
        width="80px"
      />
      <el-table-column
        prop="operateDate"
        label="操作时间"
        align="center"
        width="180px"
      />
      <el-table-column
        prop="operateType"
        label="操作类型"
        align="center"
        width="100px"
      />
      <el-table-column prop="remark" label="详细内容" align="center" />
    </el-table>
    <el-dialog
      v-model="showStatus"
      title="卖家备注"
      center
      width="400px"
      @close="initincrease()"
    >
      <el-input
        v-model="shopRemark"
        type="textarea"
        maxlength="50"
        placeholder="限制50个字"
        :rows="4"
      />
      <template v-slot:footer>
        <div>
          <el-button type="danger" @click="initincrease()">取消</el-button>
          <el-button type="success" @click="confirm()">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showChangePrice"
      title="修改商品单价"
      center
      width="400px"
      @close="closeChangePrice()"
    >
      <span>修改后的单价： </span>
      <el-input
        v-model="changePrice"
        type="number"
        placeholder="请输入修改的单价"
        style="width: 200px"
      />
      <template v-slot:footer>
        <div>
          <el-button type="danger" @click="closeChangePrice()">取消</el-button>
          <el-button type="success" @click="confirmChangePrice()"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <ChooseSeePhone ref="seePhone" />
    <logisticsDetail ref="logisticsDetail" :orderNum="detail.orderNum" />
    <refundDetail ref="afterDetail" @success="readyData" />
    <updateAddress
      ref="updateAddress"
      @success="readyData"
      :orderNum="detail.orderNum"
    />
    <afterSale ref="afterSale" @success="readyData" />
    <dialogAfterDetail
      ref="dialogAfterDetail"
      :isMore="true"
    ></dialogAfterDetail>
    <changePrice ref="changePriceRef" @success="readyData" />
    <replyMessage ref="replyMessage" @success="readyData" />
  </div>
</template>

<script>
import { Edit as IconEditOutline } from '@element-plus/icons-vue'
import {
  getSelfOrderDetail,
  getSelfOrderLog,
  saveSelfOrderListAdd,
  setSellerRemark,
  postChangePrice,
  postSpecialCancel,
  guaranteeApply
} from '@/api/shop'
import logisticsDetail from './components/logistics-detail.vue'
import refundDetail from './components/refund-detail.vue'
import ChooseSeePhone from '@/components/Dialog/ChooseSeePhone.vue'
import updateAddress from './components/update-address.vue'
import afterSale from './components/after-sale.vue'
import dialogAfterDetail from '../afterSalesList/components/dialog-after-detail.vue'
import changePrice from './components/change-price.vue'
import replyMessage from './components/reply-message.vue'

export default {
  data() {
    return {
      ruleForm: {
        limit: 20 // 数量
      },
      // 对应值
      orderStatusAll: {
        1: '待支付',
        2: '待发货',
        3: '待收货',
        4: '交易完成',
        5: '已取消',
        6: '预付款支付完成'
      },
      refundStatusAll: {
        0: '退款申请中',
        1: '同意退款',
        2: '拒绝退款'
      },
      //卖家备注
      shopRemark: '',
      showStatus: false,
      detail: {},
      children: {},
      shopList: [],
      logList: [],
      operateDetail: '',
      showChangePrice: false,
      changePrice: '',
      changePriceParams: {},
      IconEditOutline: markRaw(IconEditOutline)
    }
  },
  name: 'OrderDetail',
  components: {
    IconEditOutline,
    logisticsDetail,
    refundDetail,
    ChooseSeePhone,
    updateAddress,
    afterSale,
    dialogAfterDetail,
    changePrice,
    replyMessage
  },
  activated() {
    this.orderNum = (this.$route.query && this.$route.query.orderNum) || ''
    this.readyData()
  },
  mounted() {},
  methods: {
    readyData() {
      this.operateDetail = ''
      this.getOrderDetail()
      this.getLogList()
    },
    getOrderDetail() {
      const me = this
      getSelfOrderDetail({
        orderNum: me.orderNum,
        appSource: 1
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data
          me.detail = data
          const deliveryAddress = me.detail.deliveryAddress || {}
          me.detail.deliveryAddressContent =
            deliveryAddress.province || deliveryAddress.city
              ? `${deliveryAddress.province || ''}${
                  deliveryAddress.city || ''
                }${deliveryAddress.county || ''}${
                  deliveryAddress.address || ''
                }`
              : ''
        }
      })
    },
    // 查看物流
    seeMore() {
      this.$refs.logisticsDetail.showData({
        orderNum: this.orderNum || 'DEVSCO21080933F61',
        jdOrderId: this.jdOrderId || 216702971830
      })
    },
    // 获取日志
    getLogList() {
      const me = this
      getSelfOrderLog({
        businessId: me.orderNum,
        page: 1,
        limit: 100
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data
          me.logList = data.listData
        }
      })
    },
    // 发送数据
    postData() {
      const me = this
      if (!me.operateDetail.length) return
      saveSelfOrderListAdd({
        content: me.operateDetail,
        orderNum: me.orderNum
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('提交成功')
          me.getLogList()
          me.operateDetail = ''
        } else {
          me.$message.error('提交失败')
        }
      })
    },
    // 申请退款
    goRefundDetail(data) {
      this.$refs.afterDetail.showData({
        ...data,
        orderNum: this.orderNum
      })
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({ mobile: mobile, source: 'self-order' })
    },
    // 修改邮寄地址
    updateAddress(data) {
      this.$refs.updateAddress && this.$refs.updateAddress.showData(data)
    },
    changeRemark() {
      this.showStatus = true
    },
    confirm() {
      setSellerRemark({
        orderNum: this.detail.orderNum,
        remark: this.shopRemark
      })
        .then((res) => {
          this.showStatus = false
          this.detail.sellerRemark = this.shopRemark
        })
        .catch((e) => console.log(e))
    },
    initincrease() {
      this.showStatus = false
      this.shopRemark = ''
    },
    showChangeDiag(item) {
      // this.showChangePrice = true
      // this.changePrice = ''
      // this.changePriceParams = {
      //   skuid: item,
      //   orderNum: this.detail.orderNum
      // }
      this.$refs.changePriceRef.init(this.detail.skuInfo, 0)
    },
    closeChangePrice() {
      this.showChangePrice = false
    },

    confirmChangePrice() {
      const me = this
      postChangePrice({
        ...me.changePriceParams,
        skuUnitePrice: me.changePrice
      })
        .then((res) => {
          if (res.data.code == 0) {
            me.closeChangePrice()
            me.$message.success('操作成功')
            me.readyData()
          } else {
            me.$message.error(res.data.msg)
          }
        })
        .catch((e) => console.log(e))
    },
    //申请售后
    goAfterSale(data) {
      const row = {
        ...data,
        orderStatus: this.detail.orderStatus
      }
      this.$refs.afterSale.showData(row)
    },
    goNext(data) {
      this.$refs.dialogAfterDetail.init(data)
    },
    sendLogistics() {
      this.$refs.replyMessage.init({
        orderNum: this.orderNum || this.detail.orderNum
      })
    },
    cancleOrder() {
      this.$confirm('是否确定取消订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postSpecialCancel({
          orderNum: this.orderNum
        }).then((res) => {
          if (res.data.code === 0) {
            this.$message.success('订单已取消')
            this.readyData()
          } else {
            this.$message.error(res.data.msg || '取消订单失败')
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.order-price {
  text-align: right;
}
.kill-active {
  display: relative;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #fec68b;
  font-size: 13px;
}
</style>
