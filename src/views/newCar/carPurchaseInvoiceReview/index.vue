<template>
  <div class="invoice-review">
    <div style="max-height: 80vh" class="review-list">
      <div
        style="width: 100%; display: flex; align-items: center; padding: 20px"
      >
        <span class="review">待审核:</span>
        <span class="pendingReview takeSel">
          {{ waitAuditTotal || 0 }}
        </span>
        <el-button class="choose takeSelectDesc" @click="takeData()"
          >正取20条</el-button
        >
      </div>
      <el-table
        ref="table"
        :data="goodsList"
        class="table"
        highlight-current-row
        row-key="goodsList"
        border
        style="width: 100%; overflow-y: auto; height: 75vh"
        @row-dblclick="openDetails"
      >
        <el-table-column prop="userId" label="用户ID" align="center" />
        <el-table-column prop="orderNum" label="订单号" align="center" />
        <el-table-column prop="invoiceType" label="订单类型" align="center">
          <template v-slot="scope">
            <span>{{ convertInvoiceType[scope.row.orderType] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-show="showModule" class="coupon-invoiceAudit-detail">
      <div v-loading="loading" class="detail-content">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          style="min-width: 800px; max-width: 1200px; padding-left: 40px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <div style="margin-bottom: 30px">
                <p class="title" style="font-weight: bold">用户信息</p>
                <el-form-item label="用户ID：" prop="userId">
                  <p>{{ ruleForm.userId }}</p>
                </el-form-item>
                <el-form-item
                  label="用户名称："
                  prop="userName"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.userName }}</p>
                </el-form-item>
              </div>
              <div style="margin-bottom: 30px">
                <p class="title" style="font-weight: bold">
                  实名认证信息（需与发票名字一致）
                </p>
                <el-form-item label="实名认证状态：" prop="certifyStatus">
                  <p>{{ certificationStatusEnum[ruleForm.certifyStatus] }}</p>
                </el-form-item>
                <el-form-item
                  label="实名认证姓名："
                  prop="certifyRealName"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.certifyRealName }}</p>
                </el-form-item>
                <el-form-item
                  label="身份证号码:"
                  prop="idCard"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.idCard }}</p>
                </el-form-item>
                <el-form-item
                  label="支付宝姓名:"
                  prop="alibabaRealName"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.alibabaRealName }}</p>
                </el-form-item>
                <el-form-item
                  label="支付宝账号:"
                  prop="alibabaAccount"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.alibabaAccount }}</p>
                </el-form-item>
              </div>
              <div style="margin-bottom: 20px">
                <!--invoiceType(0:摩托范补贴，1：一口价)  -->
                <p class="title" style="font-weight: bold">
                  {{
                    ruleForm.invoiceType === 0
                      ? '优惠券信息'
                      : '一口价购车信息'
                  }}<span
                    v-if="ruleForm.invoiceType === 0"
                    style="color: red; margin-left: 30px"
                    >(购买优惠券日期需早于开票日期)</span
                  >
                </p>
                <el-form-item
                  label="订单号："
                  prop="orderNum"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.orderNum }}</p>
                </el-form-item>
                <el-form-item
                  label="经销商名称："
                  prop="shopName"
                  style="margin: 15px 0"
                >
                  <p>
                    {{ ruleForm.shopName
                    }}<span
                      v-if="ruleForm.isViolation"
                      style="color: red; margin-left: 10px"
                      >(有违规行为)</span
                    >
                  </p>
                </el-form-item>
                <el-form-item
                  label="经销商营业执照名称："
                  prop="registrationName"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.registrationName }}</p>
                </el-form-item>
                <el-form-item
                  label="车型名称："
                  prop="goodsCarName"
                  style="margin: 15px 0"
                >
                  <p v-if="ruleForm.invoiceType === 0">
                    {{ ruleForm.goodsCarName }}
                  </p>
                  <p v-if="ruleForm.invoiceType === 1">
                    {{ ruleForm.carName }}
                  </p>
                </el-form-item>
                <el-form-item
                  v-if="ruleForm.invoiceType === 0"
                  label="车辆价格"
                  prop="carPrice"
                  style="margin: 15px 0"
                >
                  <p>
                    {{ ruleForm.goodsMinPrice
                    }}{{
                      ruleForm.goodsMinPrice === ruleForm.goodsMaxPrice
                        ? ''
                        : '-' + ruleForm.goodsMaxPrice
                    }}
                  </p>
                </el-form-item>
                <el-form-item
                  label="补贴价格："
                  prop="couponDeductPrice"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.couponDeductPrice }}</p>
                </el-form-item>
                <el-form-item
                  v-if="ruleForm.invoiceType === 0"
                  label="券过期时间："
                  prop="overTime"
                  style="margin: 15px 0"
                >
                  <p>
                    {{ $filter.format(ruleForm.overTime, 'YYYY-MM-DD HH:mm') }}
                  </p>
                </el-form-item>
                <el-form-item
                  label=""
                  label-width="0px"
                  prop="buyTime"
                  style="margin: 15px 0"
                >
                  <p v-if="ruleForm.invoiceType === 0" style="color: red">
                    <span
                      style="
                        width: 140px;
                        padding-right: 12px;
                        font-weight: bold;
                      "
                      >购买优惠券时间：</span
                    >{{ $filter.format(ruleForm.buyTime, 'YYYY-MM-DD HH:mm') }}
                  </p>
                  <p v-if="ruleForm.invoiceType === 1">
                    <span
                      style="
                        width: 140px;
                        padding-right: 12px;
                        font-weight: bold;
                        color: #606266;
                      "
                      >下单时间：</span
                    >{{ $filter.format(ruleForm.buyTime, 'YYYY-MM-DD HH:mm') }}
                  </p>
                </el-form-item>
                <el-form-item
                  label="提交发票审核时间："
                  prop="applyTime"
                  style="margin: 15px 0"
                >
                  <p>
                    {{
                      $filter.format(ruleForm.applyTime, 'YYYY-MM-DD HH:mm')
                    }}
                  </p>
                </el-form-item>
                <el-form-item
                  label="审核失败原因"
                  prop="auditFailReason"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.auditFailReason }}</p>
                </el-form-item>
                <el-form-item
                  label="驳回原因"
                  prop="shopAuditFailReason"
                  style="margin: 15px 0"
                >
                  <p>{{ ruleForm.shopAuditFailReason }}</p>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="12">
              <!-- auditStatus:0未审核1通过2不通过 certifyStatus:0：申请中，1：成功，2：失败-->
              <el-form-item
                v-if="ruleForm.auditStatus === 0 && ruleForm.certifyStatus != 0"
                label=""
                prop=""
                style="margin: 30px 0"
              >
                <el-button type="primary" @click="pass()">通过</el-button>
                <el-button type="danger" @click="refuse()">不通过</el-button>
              </el-form-item>
              <el-form-item
                v-if="ruleForm.auditStatus === 1 && ruleForm.certifyStatus != 0"
                label=""
                prop=""
                style="margin: 30px 0"
              >
                <el-button type="primary" @click="modify()">修改</el-button>
              </el-form-item>
              <el-form-item
                label="发票代码"
                prop="invoiceHeadNum"
                style="margin: 15px 0"
              >
                <el-input
                  v-model="ruleForm.invoiceHeadNum"
                  :readonly="ruleForm.auditStatus !== 0"
                  type="text"
                  clearable
                  style="width: 300px"
                />
              </el-form-item>
              <el-form-item
                label="发票号码"
                prop="invoiceNo"
                style="margin: 15px 0"
              >
                <el-input
                  v-model="ruleForm.invoiceNo"
                  :readonly="ruleForm.auditStatus !== 0"
                  type="text"
                  clearable
                  style="width: 300px"
                />
              </el-form-item>
              <el-form-item
                label="发票图片"
                prop="couponImg"
                style="margin: 15px 0"
              >
                <div :data-key="dataTime" class="choose-show-img-content">
                  <img
                    v-if="ruleForm.couponImg"
                    :src="$filters.replaceImgUrl(ruleForm.couponImg)"
                    :data-key="dataTime"
                    class="choose-show-img-url"
                    alt=""
                    style="height: 400px; width: 400px; object-fit: cover"
                    @click="seeBigImg(ruleForm.couponImg)"
                  />
                </div>
                <div class="img-foot-fun text-center">
                  <img
                    src="../../../assets/image/<EMAIL>"
                    class="icon"
                    alt
                    @click="setRotate('left')"
                  />
                  <img
                    src="../../../assets/image/<EMAIL>"
                    class="icon"
                    alt
                    @click="setRotate('right')"
                  />
                </div>
              </el-form-item>
              <el-form-item
                label="购车凭证"
                prop="payEvidence"
                style="margin: 15px 0"
              >
                <div :data-key="dataTime" class="choose-show-img-content">
                  <img
                    v-if="ruleForm.payEvidence"
                    :src="$filters.replaceImgUrl(ruleForm.payEvidence)"
                    :data-key="dataTime"
                    class="choose-show-img-url"
                    alt=""
                    style="height: 400px; width: 400px; object-fit: cover"
                    @click="seeBigImg(ruleForm.payEvidence)"
                  />
                </div>
                <div
                  v-if="ruleForm.payEvidence"
                  class="img-foot-fun text-center"
                >
                  <img
                    src="../../../assets/image/<EMAIL>"
                    class="icon"
                    alt
                    @click="setRotate2('left')"
                  />
                  <img
                    src="../../../assets/image/<EMAIL>"
                    class="icon"
                    alt
                    @click="setRotate2('right')"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 信息重复提示Dialog，当发票编号+发票号码同时重复时，点击通过时弹窗提示“当前发票信息已存在”  点击去查看跳转重复的那条审核详情。点击取消停留在当前页-->
        <el-dialog
          v-model="repeatDialog"
          :before-close="handleRepeatClose"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          center
          width="500px"
        >
          <p class="text-center" style="font-weight: bold">
            当前发票信息已存在
          </p>
          <template v-slot:footer>
            <div>
              <el-button @click="repeatDialog = false">取 消</el-button>
              <el-button type="primary" @click="goToSee()">去查看</el-button>
            </div>
          </template>
        </el-dialog>
        <!-- 修改Dialog -->
        <el-dialog
          v-model="modifyDialog"
          :before-close="handleModifyClose"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          center
          width="500px"
        >
          <el-form ref="modifyForm" :model="modifyForm" label-width="100px">
            <el-form-item label="发票代码" prop="invoiceHeadNum">
              <el-input
                v-model="modifyForm.invoiceHeadNum"
                clearable
                type="text"
              />
            </el-form-item>
            <el-form-item label="发票号码" prop="invoiceNo">
              <el-input v-model="modifyForm.invoiceNo" clearable type="text" />
            </el-form-item>
          </el-form>

          <template v-slot:footer>
            <span>
              <el-button type="primary" @click="confirmModify">确认</el-button>
            </span>
          </template>
        </el-dialog>
      </div>
      <choose-show-image ref="showImage" />
    </div>
    <reject-notice ref="rejectNotice" @confirmRejection="confirmRejection" />
  </div>
</template>

<script>
import { certificationStatus } from '@/utils/enum'
import RejectNotice from '@/components/Notice/rejectNotice.vue'
import { couponInvoiceTypeAllEnum } from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import {
  GetCouponInvoiceList,
  AuditCoupon,
  EditCoupon,
  GetInvoiceReviewList,
  GetUpdatesDate,
  PostPendingReview,
} from '@/api/newCar'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import { replaceImgUrl } from '@/filters'
export default {
  name: 'CarPurchaseInvoiceReview',
  components: {
    ChooseShowImage,
    RejectNotice,
  },
  props: {},
  data() {
    return {
      certificationStatus: certificationStatus, // 实名认证状态
      certificationStatusEnum: convertKeyValueEnum(certificationStatus),
      ruleForm: {},
      dataTime: null,
      angle: 0,
      imgUrlList: [],
      imgContentList: [],
      repeatDialog: false, // 是否展示信息重复弹框
      repeatId: '', // 重复的发票详情id
      modifyDialog: false, // 是否显示修改弹框
      modifyForm: {
        invoiceHeadNum: '', // 发票代码
        invoiceNo: '', // 发票号码
      },
      couponInvoiceTypeAllEnum: couponInvoiceTypeAllEnum, // 订单类型
      convertInvoiceType: convertKeyValueEnum(couponInvoiceTypeAllEnum),
      goodsList: [],
      waitAuditTotal: '', // 待审核数
      loading: false,
      showModule: false, // 是否显示
      id: '',
      postStatus: false, // 是否正在拉取
      disSubmit: false,
    }
  },
  computed: {},
  watch: {},
  activated() {
    const me = this
    me.getPendingList()
    me.getBasicInformation()
  },
  methods: {
    // 购新车发票审核列表
    getPendingList() {
      const me = this
      me.loading = true
      GetInvoiceReviewList()
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('拉取成功')
            const data = response.data.data
            me.goodsList = data || []
            if (me.goodsList.length) {
              console.log(me.goodsList)
              me.id = me.goodsList[0].invoiceAuditId
              me.getDetail()
              me.showModule = true
            } else {
              me.showModule = false
            }
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    getBasicInformation() {
      // 实时更新基本信息
      const me = this
      me.loading = true
      GetUpdatesDate()
        .then((response) => {
          if (response.data.code === 0) {
            me.waitAuditTotal = response.data.data.waitAuditTotal
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 正取20条
    takeData() {
      const me = this
      if (me.waitAuditTotal === 0) {
        me.$message.error('当前待审条数为0,无法拉取')
        return
      }
      if (me.postStatus) {
        return
      }
      me.postPendingReview()
    },
    // 查询个人待审核 放到池子
    postPendingReview() {
      const me = this
      me.loading = true
      me.postStatus = true
      PostPendingReview({
        limit: 20,
        orderType: 1,
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.getBasicInformation()
            me.getPendingList()
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
          me.postStatus = false
        })
    },
    openDetails(row) {
      const me = this
      me.id = row.invoiceAuditId
      me.getDetail()
    },
    // 获取详情
    getDetail() {
      const me = this
      this.loading = true
      GetCouponInvoiceList({ id: me.id })
        .then((response) => {
          console.log(response)
          if (response.data.code === 0) {
            me.ruleForm = response.data.data.listData[0]
            me.initImg()
            this.loading = false
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 初始化图片设置
    initImg() {
      const me = this
      me.dataTime = Math.floor(new Date())
      setTimeout(function () {
        me.setClearPadding()
        me.setPading()
      }, 150)
    },
    // 清除图片padding
    setClearPadding() {
      const imgUrl = document.querySelectorAll(`.choose-show-img-url`)
      const imgContent = document.querySelectorAll(`.choose-show-img-content`)
      this.imgUrlList = Array.from(imgUrl)
      this.imgContentList = Array.from(imgContent)
      this.imgUrlList.map(function (value) {
        value.style.transform = `rotate(0deg)`
      })
      this.imgContentList.map(function (value) {
        value.style.paddingTop = 0
      })
    },
    // 设置图片padding
    setPading() {
      const me = this
      setTimeout(function () {
        const content = me.imgContentList.filter(
          (_) => parseInt(_.dataset.key) === me.dataTime
        )
        const url = me.imgUrlList.filter(
          (_) => parseInt(_.dataset.key) === me.dataTime
        )
        if (!url.length) {
          return
        }
        if (url[0].offsetWidth < url[0].offsetHeight) {
          return
        }
        content[0].style.paddingTop = `${
          (url[0].offsetWidth - url[0].offsetHeight) / 2
        }px`
      }, 100)
    },
    // 设置旋转
    setRotate(type) {
      this.angle = type === 'left' ? this.angle - 90 : this.angle + 90
      const url = this.imgUrlList.filter(
        (_) => parseInt(_.dataset.key) === this.dataTime
      )
      url[0].style.transform = `rotate(${this.angle}deg)`
    },
    // 设置旋转
    setRotate2(type) {
      this.angle = type === 'left' ? this.angle - 90 : this.angle + 90
      const url = this.imgUrlList.filter(
        (_) => parseInt(_.dataset.key) === this.dataTime
      )
      url[1].style.transform = `rotate(${this.angle}deg)`
    },
    // 大图查看图片
    seeBigImg(img) {
      this.$refs.showImage.init(replaceImgUrl(img))
    },
    // 通过
    pass() {
      const me = this
      if (!me.ruleForm.invoiceHeadNum) {
        return me.$message.error('请填写发票编号')
      }
      if (!me.ruleForm.invoiceNo) {
        return me.$message.error('请填写发票号码')
      }
      me.$confirm('确认是否通过？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          AuditCoupon({
            id: me.id,
            status: 1,
            invoiceNo: me.ruleForm.invoiceNo,
            invoiceHeadNum: me.ruleForm.invoiceHeadNum,
          })
            .then((response) => {
              me.$message.success('操作成功')
              me.getPendingList()
              me.getBasicInformation()
            })
            .catch((err) => {
              if (err.response.data.code === 10024) {
                me.repeatDialog = true
                me.repeatId = err.response.data.data
              }
            })
        })
        .catch()
    },
    // 不通过
    refuse() {
      this.$confirm('是否不通过？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$refs.rejectNotice.init()
        })
        .catch()
    },
    // 确认拒绝
    confirmRejection(mes) {
      const me = this
      if (!mes) {
        return me.$message.error('请填写失败理由')
      }
      if (me.disSubmit) {
        return
      }
      me.disSubmit = true
      AuditCoupon({
        id: me.id,
        status: 2,
        auditFailReason: mes,
      }).then((response) => {
        me.disSubmit = false
        if (response.data.code === 0) {
          me.$message.success('操作成功')
          me.getPendingList()
          me.getBasicInformation()
        } else {
          me.$alert(response.data.msg, {
            confirmButtonText: '确定',
            showClose: false,
            type: 'error',
          })
        }
      })
    },
    // 关闭信息重复弹框
    handleRepeatClose() {
      this.repeatDialog = false
    },
    // 去查看重复的那条审核详情
    goToSee() {
      this.repeatDialog = false
      this.modifyDialog = false
      this.$router.push({
        name: 'CouponInvoiceAuditDetail',
        query: { id: this.repeatId },
      })
    },
    modify() {
      this.modifyForm.invoiceHeadNum = this.ruleForm.invoiceHeadNum
      this.modifyForm.invoiceNo = this.ruleForm.invoiceNo
      this.modifyDialog = true
      console.log(this.modifyForm)
    },
    // 关闭修改弹框
    handleModifyClose() {
      this.modifyDialog = false
    },
    // 确认修改
    confirmModify() {
      const me = this
      if (!me.modifyForm.invoiceHeadNum) {
        return me.$message.error('请填写发票编号')
      }
      if (!me.modifyForm.invoiceNo) {
        return me.$message.error('请填写发票号码')
      }
      me.$confirm('确认是否修改？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          EditCoupon({
            id: me.$route.query.id,
            invoiceNo: me.modifyForm.invoiceNo,
            invoiceHeadNum: me.modifyForm.invoiceHeadNum,
          })
            .then((response) => {
              me.$message.success('修改成功')
              me.modifyDialog = false
              me.getPendingList()
              me.getBasicInformation()
            })
            .catch((err) => {
              if (err.response.data.code === 10024) {
                me.repeatDialog = true
                me.repeatId = err.response.data.data
              }
            })
        })
        .catch()
    },
  },
}
</script>

<style lang="scss" scoped>
.invoice-review {
  display: flex;
  .review-list {
    width: 20vw;

    .review {
      font-size: 12px;
      color: #333;
      font-weight: normal;
    }

    .pendingReview {
      display: inline-block;
      font-size: 26px;
      color: #333;
      margin-right: 40px;
      margin-left: 2px;
    }

    .takeSel {
      color: red;
      font-weight: bold;
    }
  }

  .coupon-invoiceAudit-detail {
    width: 70vw;
    padding: 20px 10px;

    .title {
      padding: 10px 0;
    }

    .el-form-item {
      margin-bottom: 5px;
    }

    p {
      margin: 0;
    }

    .choose-show-img-content {
      margin-bottom: 20px;

      .choose-show-img-url {
        margin: 0 auto;
        display: block;
        transform: rotate(0deg);
        max-height: 800px;
        max-width: 800px;
        animation: mymove 1s linear;
      }

      @keyframes mymove {
        0% {
          opacity: 0;
        }

        100% {
          opacity: 1;
        }
      }
    }

    .img-foot-fun {
      .icon {
        width: 30px;
      }

      .icon:nth-of-type(1) {
        margin-right: 10px;
      }

      .icon:nth-of-type(2) {
        margin-left: 10px;
      }
    }
  }
}
</style>
