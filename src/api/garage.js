import request from '@/utils/request'
import { APIURL, EMOTOFINEMANAGERURL } from '@/utils/configData/config'
// 上传图片-暗水印
export function darkWaterUploadImage(data) {
  return request({
    url: `/pirate/media/oss/uploadImage`,
    method: 'post',
    transformRequest(data) {
      const formData = new FormData()
      for (const item in data) {
        formData.append(item, data[item])
      }
      return formData
    },
    contentType: false,
    data
  })
}
// 上传图片-暗水印
export function multiTransferImage(data) {
  return request({
    url: `/pirate/media/oss/uploadMultiImage`,
    method: 'post',
    transformRequest(data) {
      const formData = new FormData()
      for (const item in data) {
        formData.append(item, data[item])
      }
      return formData
    },
    contentType: false,
    data
  })
}

export function getImageList(query) {
  return request({
    url: '/carport/oss/goods/info/images',
    method: 'get',
    params: query
  })
}
export function deleteImage(data) {
  return request({
    url: '/carport/oss/goods/del/image',
    method: 'post',
    data
  })
}
export function getColorList(query) {
  return request({
    url: '/carport/oss/color/listAll',
    method: 'get',
    params: query
  })
}
export function saveColor(data) {
  return request({
    url: '/carport/oss/color/saveOrUpdate',
    method: 'post',
    params: data
  })
}
// 删除颜色和图片的关联关系
export function delColorRelation(data) {
  return request({
    url: '/carport/oss/car/delColorRelation',
    method: 'post',
    params: data
  })
}
// 编辑款型颜色
export function saveCarInfoColor(data) {
  return request({
    url: '/carport/oss/car/info/color',
    method: 'post',
    data
  })
}
// 查询车型下所有的颜色
export function getSearchAllByGoodId(query) {
  return request({
    url: '/carport/oss/color/searchAllByGoodId',
    method: 'get',
    params: query
  })
}

// 删除车型下指定颜色
export function delGoodsColorRelation(data) {
  return request({
    url: '/carport/oss/car/delGoodsColorRelation',
    method: 'post',
    params: data
  })
}
export function SearchTipsList(query) {
  return request({
    url: '/carport/oss/mg/goods/tips/list',
    method: 'get',
    params: query
  })
}
export function AddTipInfo(data) {
  // const data = {
  //   type, keywords, status
  // }
  return request({
    url: '/carport/oss/mg/goods/tips/info',
    method: 'post',
    data
  })
}
export function UpdateTipInfo(data) {
  // const data = {
  //   id, type, keywords, status
  // }
  return request({
    url: '/carport/oss/mg/goods/tips/info',
    method: 'post',
    data
  })
}
export function DeleteTipList(data) {
  // const data = {
  //   ids
  // }
  return request({
    url: '/carport/oss/mg/goods/tips/delete',
    method: 'post',
    data
  })
}

export function SearchTipsKeywordsList(query) {
  return request({
    url: '/carport/oss/mg/goods/tips/keywords/list',
    method: 'get',
    params: query
  })
}

export function AddTipKeywordsInfo(data) {
  // const data = {
  //   tipId, keywords
  // }
  return request({
    url: '/carport/oss/mg/goods/tips/keywords/info',
    method: 'post',
    data
  })
}
export function UpdateTipKeywordsInfo(data) {
  // const data = {
  //   id, tipId, keywords
  // }
  return request({
    url: '/carport/oss/mg/goods/tips/keywords/info',
    method: 'post',
    data
  })
}

export function DeleteTipKeywordsList(data) {
  // const data = {
  //   ids
  // }
  return request({
    url: '/carport/oss/mg/goods/tips/keywords/delete',
    method: 'post',
    data
  })
}

// 新增推荐模块
export function AddHomeModule(data) {
  return request({
    url: '/forum/oss/homeModuleController/saveHomeModule',
    method: 'post',
    data
  })
}

// 更新推荐模块
export function UpdateHomeModule(data) {
  return request({
    url: '/forum/oss/homeModuleController/updateHomeModule',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}
// 更新推荐模块
export function DeleteHomeModule(data) {
  return request({
    url: '/forum/oss/homeModuleController/deleteHomeModule',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

// 推荐模块列表
export function GetHomeModuleList(data) {
  return request({
    url: '/forum/oss/homeModuleController/listHomeModuleInfo',
    method: 'post',
    data
  })
}
// 查询模块关联
export function GetHomeModuleRelationList(data) {
  return request({
    url: '/forum/oss/homeModuleController/listHomeModuleRelation',
    method: 'post',
    data
  })
}
// 删除子模块
export function DeleteHomeModuleRelationList(data) {
  return request({
    url: '/forum/oss/homeModuleRelationController/deleteHomeModuleRelation',
    beforeAlterTwo: false,
    method: 'post',
    data
  })
}
// 清空子模块
export function ResetHomeModuleRelationList(data) {
  return request({
    url: '/forum/oss/homeModuleRelationController/deleteAllHomeModuleRelation',
    method: 'post',
    beforeAlterTwo: false,
    data
  })
}
// 排序子模块
export function UpdateHomeModuleRelationSort(data) {
  return request({
    url: '/forum/oss/homeModuleRelationController/changeHomeModuleRelationSort',
    method: 'post',
    data
  })
}
// 新增子模块
export function AddHomeModuleRelation(data) {
  return request({
    url: '/forum/oss/homeModuleRelationController/saveHomeModuleRelation',
    method: 'post',
    data
  })
}

// 排序模块列表
export function UpdateHomeModuleSort(data) {
  return request({
    url: '/forum/oss/homeModuleController/changeHomeModule',
    method: 'post',
    data
  })
}

// 推荐生效
export function EnableHomeModule(data) {
  return request({
    url: '/forum/oss/homeModuleController/enableHomeModule',
    method: 'post',
    data
  })
}

// 车辆列表
export function GetCarList(query) {
  return request({
    url: '/carport/oss/goods/all/list',
    method: 'get',
    params: query
  })
}

// 品牌列表
export function GetBrandAllList() {
  return request({
    url: `${APIURL}carport/brand/v2/all/list?brandEnergyType=3`,
    method: 'get'
  })
}

// 入驻列表(商家入驻)
export function ShopApplyList(query) {
  return request({
    url: '/carport/oss/merchant/apply/list',
    method: 'get',
    params: query
  })
}

// 入驻列表V2(商家入驻)
export function ShopApplyListV2(query) {
  return request({
    url: '/shop/oss/v2/shopInfo/applyList',
    method: 'get',
    params: query
  })
}

// 资质审核列表
export function ShopQualificationList(query) {
  return request({
    url: '/shop/oss/v2/shopInfo/qualification/findList',
    method: 'get',
    params: query
  })
}

// 入驻详情(商家入驻)
export function ShopApplyDetail(query) {
  return request({
    url: '/carport/oss/merchant/apply/detailV2',
    method: 'get',
    beforeAlterFirst: true,
    params: query
  })
}
// 入驻详情V2(商家入驻)
export function ShopApplyDetailV2(query) {
  return request({
    url: '/shop/oss/v2/shopInfo/apply/detail',
    method: 'get',
    beforeAlterFirst: true,
    params: query
  })
}

// 资质申请详情
export function ShopQualificationDetail(query) {
  return request({
    url: '/shop/oss/v2/shopInfo/qualification/detail',
    method: 'get',
    params: query
  })
}

// 通过申请(商家入驻)
export function ShopApplyAgree(data) {
  return request({
    url: '/carport/oss/merchant/apply/agree',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10603',
    data
  })
}

// 审核通过V2(商家入驻)
export function ShopApplyAgreeV2(data) {
  return request({
    url: '/shop/oss/v2/shopInfo/approve',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10628',
    data
  })
}

// 资质审核 提交
export function ShopQualificationApprove(data) {
  return request({
    url: '/shop/oss/v2/shopInfo/qualification/submit',
    method: 'post',
    data
  })
}
// 新增资质
export function addQualification(data) {
  return request({
    url: '/shop/oss/v2/shopInfo/qualification/save',
    method: 'post',
    data
  })
}
// 不通过申请(商家入驻)
export function ShopApplyUnagree(data) {
  return request({
    url: '/carport/oss/merchant/apply/unagree',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10603',
    data
  })
}

// 审核拒绝V2(商家入驻)
export function ShopApplyUnagreeV2(data) {
  return request({
    url: '/shop/oss/v2/shopInfo/refuse',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10628',
    data
  })
}

// 资质审核不通过
export function ShopQualificatioRefuse(data) {
  return request({
    url: '/shop/oss/v2/shopInfo/qualification/refuse',
    method: 'post',
    data
  })
}

// 编辑商铺销售品牌
export function EditShopBrand(data) {
  return request({
    url: '/carport/oss/shop/apply/editShopBrand',
    method: 'post',
    data
  })
}

// 车辆管理  刷新缓存
export function clearCache() {
  return request({
    url: '/carport/oss/goods/clear/cache',
    method: 'post'
  })
}

/**
 * 车辆过滤(车辆管理->车辆属性列表)
 * @param query
 */
export function searchCarList(query) {
  return request({
    url: '/carport/oss/goods/search/list',
    method: 'get',
    params: query
  })
}

/**
 * 新车配置---优惠券配置（车型列表查询）
 * @param query
 */
export function searchCarListV2(query) {
  return request({
    url: '/carport/oss/goods/search/list/v2',
    method: 'get',
    params: query
  })
}

/**
 * 车辆删除(车辆管理->车辆属性列表删除)
 * @param data
 */
export function deleteCar(data) {
  return request({
    url: '/carport/oss/goods/info/delete',
    method: 'post',
    data
  })
}

/**
 * 编辑车辆,根据品牌id获取所属车系
 * @param data
 */
export function brandseriesAllList(data) {
  return request({
    url: '/carport/oss/brandseries/all/list',
    method: 'post',
    data
  })
}

/**
 * 车辆详情
 * @param query
 */
export function getCarDetail(query) {
  return request({
    url: '/carport/oss/goods/info/detail',
    method: 'get',
    beforeAlterFirst: true,
    params: query
  })
}

/**
 * 车辆信息保存
 * @param data
 */
export function updateCarDetail(data) {
  return request({
    url: '/carport/oss/goods/info',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1050102',
    data
  })
}

/**
 * 车辆状态更新
 * @param data
 */
export function updateRecAndSaleStatus(data) {
  return request({
    url: '/carport/oss/goods/updateRecAndSaleStatus',
    method: 'post',
    data
  })
}

/**
 * 车辆同步款型
 * @param data
 */
export function syncCarManual(data) {
  return request({
    url: '/carport/oss/goods/syncCarManual',
    method: 'post',
    data
  })
}
/**
 * 编辑降价价格
 * @param data
 */
export function editPriceReduction(data) {
  return request({
    url: '/carport/oss/shop/car/rel/price/update',
    method: 'post',
    data
  })
}

/**
 * 设置车型排序
 * @param data
 */
export function SetModelOrder(data) {
  return request({
    url: 'carport/oss/car/sortCar',
    method: 'post',
    data
  })
}

/**
 * 经销商活动列表
 * @param query
 */
export function getActivityList(query) {
  return request({
    url: '/activity/oss/admin/activity/list',
    method: 'get',
    params: query
  })
}

/**
 * 模糊查询店铺信息
 * @param query
 */
export function getlistShopsByKeywords(query) {
  return request({
    // url: '/activity/oss/admin/activity/listShopsByKeywords',
    url: '/shop/oss/shop/apply/listShopsByKeywords',
    method: 'get',
    params: query
  })
}

/**
 * 经销商活动详情信息
 * @param query
 */
export function getActivityDetail(query) {
  return request({
    url: `/activity/oss/admin/activity/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 用户下的经销商列表
 * @param query
 */
export function getMerchantByUid(query) {
  return request({
    url: `/carport/oss/shop/apply/merchantByUid`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商活动审核通过
 * @param data
 */
export function activityAgree(data) {
  return request({
    url: '/activity/oss/admin/activity/agree',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1060501',
    data
  })
}

/**
 * 经销商活动审核不通过
 * @param data
 */
export function activityUnAgree(data) {
  return request({
    url: '/activity/oss/admin/activity/unagree',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1060501',
    data
  })
}

/**
 * 直通车业务状态开关
 */
export function directTrainStatus(data) {
  return request({
    url: '/shop/oss/merchant/info/service/open',
    method: 'post',
    data
  })
}

export function directTradeStatus(params) {
  return request({
    url: '/trade/oss/car/final/price/updateOpenStatus',
    method: 'post',
    data: params
  })
}

/**
 * 经销商列表
 * @param data
 */
export function searchMerchantList(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/list',
    url: '/shop/oss/merchant/info/list',
    method: 'get',
    params: query
  })
}

/**
 * 经销商订单列表
 * @param data
 */
export function searchDealerOrderList(query) {
  return request({
    // url: '/carport/oss/order/list',
    url: '/shop/oss/member/order/list',
    method: 'get',
    params: query
  })
}

export function ShopAppplyList(query) {
  return request({
    url: '/shop/oss/settlement/shop/apply/list',
    method: 'get',
    params: query
  })
}

export function shopApplyRemark(data) {
  return request({
    url: '/shop/oss/settlement/shop/apply/remark',
    method: 'post',
    data
  })
}
export function DealWith(data) {
  return request({
    url: '/shop/oss/settlement/shop/apply/handle',
    method: 'post',
    data
  })
}

// 新增经销商订单
export function addDealerOrderList(data) {
  return request({
    url: '/shop/oss/member/level/order/create',
    method: 'post',
    data
  })
}

/**
 * 经销商降价列表
 * @param data
 */
export function dealerPriceReductionList(query) {
  return request({
    url: '/carport/oss/shop/car/rel/list',
    method: 'get',
    params: query
  })
}
/**
 * 车口碑审核列表
 * @param data
 */
export function carReputationList(query) {
  return request({
    url: '/carport/oss/car/carScoreList',
    method: 'get',
    params: query
  })
}
/**
 * 商家口碑审核列表
 * @param data
 */
export function dealerReputationList(query) {
  return request({
    url: '/carport/oss/shop/apply/shopScoreList',
    method: 'get',
    params: query
  })
}
/**
 * 经销商口碑列表
 * @param data
 */
export function distributorWordOfMouthList(query) {
  return request({
    url: '/shop/oss/shop/score/shopScoreList2',
    method: 'get',
    params: query
  })
}
/**
 * 车辆口碑列表
 * @param data
 */
export function vehiclewordOfMouthList(query) {
  return request({
    url: '/carport/oss/goods/userGoodScoreList',
    method: 'get',
    params: query
  })
}
/**
 * 车辆口碑删除
 * @param data
 */
export function vehicleDeleteWordOfMouth(data) {
  return request({
    url: '/carport/oss/goods/deleteScore',
    method: 'post',
    data
  })
}
/**
 * 经销商口碑删除
 * @param data
 */
export function dealerDeletesWordOfMouth(data) {
  return request({
    url: '/shop/oss/shop/score/deleteShopComment',
    method: 'post',
    data
  })
}
/**
 * 经销商口碑置顶
 * @param data
 */
export function dealerReputation(data) {
  return request({
    url: '/shop/oss/shop/score/topShopComment',
    method: 'post',
    data
  })
}

/**
 * 经销商口碑折叠
 * @param data
 */
export function foldShopComment(data) {
  return request({
    url: '/shop/oss/shop/score/foldShopComment',
    method: 'post',
    data
  })
}

/**
 * 车辆口碑置顶
 * @param data
 */
export function vehicleWordOfMouth(data) {
  return request({
    url: '/carport/oss/goods/top',
    method: 'post',
    data
  })
}

/**
 * 车辆恶评折叠
 * @param data
 */
export function foldCarComment(data) {
  return request({
    url: '/carport/oss/goods/foldCarComment',
    method: 'post',
    data
  })
}

/**
 * 经销商详情
 * @param data
 */
export function merchantDetail(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/detail',
    url: '/shop/oss/merchant/info/detail',
    method: 'get',
    beforeAlterFirst: true,
    params: query
  })
}

/**
 * 查看经销商虚拟号
 * @param query
 */
export function virtualMobile(query) {
  return request({
    url: '/shop/oss/merchant/info/virtual/mobile',
    method: 'get',
    params: query
  })
}

/**
 * 绑定经销商虚拟号
 * @param data
 */
export function bindVirtualMobile(data) {
  return request({
    url: '/shop/oss/merchant/info/bind/virtual/mobile',
    method: 'post',
    data
  })
}

/**
 * 绑定经销商虚拟号-指定虚拟号
 * @param data
 */
export function bindTargetVirtualMobile(data) {
  return request({
    url: '/shop/oss/merchant/info/bind/target/virtual/mobile',
    method: 'post',
    data
  })
}

/**
 * 经销商运营标签保存
 * @param data
 */
export function saveTagList(data) {
  return request({
    url: '/shop/oss/merchant/tags/' + data.shopId + '/save',
    method: 'post',
    data
  })
}

/**
 * 添加/编辑活动（无需审核）
 * @param data
 */
export function activitySave(data) {
  return request({
    url: '/activity/oss/admin/activity/saveOrUpdate',
    method: 'post',
    data
  })
}
/**
 * 失效降价信息
 * @param data
 */
export function invalidPriceReductionInformation(data) {
  return request({
    url: '/carport/oss/shop/car/rel/price/invalid',
    method: 'post',
    data
  })
}
/**
 * 车辆审核通过
 * @param data
 */
export function examinationPassed(data) {
  return request({
    url: '/carport/oss/car/carScoreAuditPass',
    method: 'post',
    data
  })
}
/**
 * 经销商审核通过
 * @param data
 */
export function verifyPassed(data) {
  return request({
    url: '/carport/oss/shop/apply/shopScoreAuditPass',
    method: 'post',
    data
  })
}
/**
 * 经销商审核不通过
 * @param data
 */
export function verifyNotPassed(data) {
  return request({
    url: '/carport/oss/shop/apply/shopScoreAuditRefuse',
    method: 'post',
    data
  })
}
/**
 * 车辆审核未通过
 * @param data
 */
export function auditNotPassed(data) {
  return request({
    url: '/carport/oss/car/carScoreAuditRefuse',
    method: 'post',
    data
  })
}
/**
 * 恢复降价信息
 * @param data
 */
export function restorePriceReductionInformation(data) {
  return request({
    url: '/carport/oss/shop/car/rel/price/un/invalid',
    method: 'post',
    data
  })
}
/**
 * 编辑减低价格
 * @param data
 */
export function lowerThePrice(data) {
  return request({
    url: '/carport/oss/shop/car/rel/price/update',
    method: 'post',
    data
  })
}
/**
 * OSS经销商编辑
 * @param data
 */
export function merchantUpdate(data) {
  return request({
    url: '/shop/oss/merchant/info/update',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10601',
    data
  })
}

/**
 * OSS经销商服务开通
 * @param data
 */
export function merchantServiceOpen(data) {
  return request({
    url: '/shop/oss/merchant/info/service/open',
    method: 'post',
    data
  })
}
/**
 * OSS经销商服务开通2
 * @param data
 */
export function merchantServiceOpen2(data) {
  return request({
    url: '/shop/oss/merchant/info/service/open/button/punish',
    method: 'post',
    data
  })
}
/**
 * OSS经销商角色开通
 * @param data
 */
export function merchantTagsOpen(data) {
  return request({
    url: '/shop/oss/merchant/tags/open',
    method: 'post',
    data
  })
}

/**
 * OSS经销商-直播角色开通
 * @param data
 */
export function livrInfoAttrOpen(data) {
  return request({
    url: '/shop/oss/info/attr/open',
    method: 'post',
    data
  })
}

/**
 * 查询品牌审核详情
 * @param data
 */
export function brandReviewDetails(query) {
  return request({
    // url: '/carport/oss/shop/apply/getShopBrandRelById',
    url: '/shop/oss/shopBrand/apply/getShopBrandRelById',
    method: 'get',
    beforeAlterFirst: true,
    params: query
  })
}

/**
 * 查询年代款信息
 * @param query
 */
export function searchAgePattern(query) {
  return request({
    url: '/carport/oss/goods/carNameList',
    method: 'get',
    params: query
  })
}

/**
 * 新增年代款
 * @param data
 */
export function addAgePattern(data) {
  return request({
    url: '/carport/oss/goods/carName/add',
    method: 'post',
    data
  })
}

/**
 * 编辑年代款
 * @param data
 */
export function updateAgePattern(data) {
  return request({
    url: '/carport/oss/goods/carName/modify',
    method: 'post',
    data
  })
}

/**
 * 删除年代款
 * @param data
 */
export function delAgePattern(data) {
  return request({
    url: '/carport/oss/goods/carName/delete',
    method: 'post',
    data
  })
}

/**
 * 款型列表
 * @param data
 */
export function getAgeList(query) {
  return request({
    url: '/carport/oss/car/search/list',
    method: 'get',
    params: query
  })
}

/**
 * 款型详情
 * @param data
 */
export function getAgeDetail(query) {
  return request({
    url: '/carport/oss/car/info/detail',
    method: 'get',
    beforeAlterFirst: true,
    params: query
  })
}

/**
 * 修改车款所属年代款
 * @param data
 */
export function updatePattern(data) {
  return request({
    url: '/carport/oss/car/modifyCarName',
    method: 'post',
    data
  })
}

/**
 * 删除款型
 * @param data
 */
export function delPattern(data) {
  return request({
    url: '/carport/oss/car/info/delete',
    method: 'post',
    data
  })
}

/**
 * 款型信息保存
 * @param data
 */
export function updatePatternDetail(data) {
  return request({
    url: '/carport/oss/car/info',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1050103',
    data
  })
}

/**
 * 年代款排序
 * @param data
 */
export function updateAgeList(data) {
  return request({
    url: '/carport/oss/goods/carName/order',
    method: 'post',
    data
  })
}

/**
 * 车辆款型排序
 * @param data
 */
export function updatePatternList(data) {
  return request({
    url: '/carport/oss/car/order',
    method: 'post',
    data
  })
}

/**
 * 认证车辆列表
 * @param query
 */
export function getGarageApproveList(query) {
  return request({
    url: '/carport/oss/carport/certifi/v2/list',
    method: 'get',
    params: query
  })
}

/**
 * 认证车辆详情
 * @param query
 */
export function getGarageApproveDetail(query) {
  return request({
    url: '/carport/oss/carport/certifi/detail',
    method: 'get',
    params: query
  })
}

/**
 * 更新审核状态 通过/不通过
 * @param data
 */
export function updateApproveStatus(data) {
  return request({
    url: '/carport/oss/carport/certifi/v2/info',
    method: 'post',
    data
  })
}

/**
 * 修改认证信息(修改车辆认证时使用)
 * @param data
 */
export function modifyGarageApprove(data) {
  return request({
    url: '/carport/oss/carport/certifi/v2/recommend/info',
    method: 'post',
    data
  })
}

// 车辆认证审列表/待审核列表
export function getAuthList(query) {
  return request({
    url: '/carport/oss/carport/certifi/carCertAuthList',
    method: 'get',
    params: query
  })
}

/**
 * 正取/倒取 车辆认证50条审核，到审核池
 * @param data
 */
export function addAuthList(data) {
  return request({
    url: '/carport/oss/carport/certifi/pullAuthList',
    method: 'post',
    data
  })
}

// 获取审核基本信息
export function getAuthInfo(query) {
  return request({
    url: '/carport/oss/carport/certifi/authProgress',
    method: 'get',
    params: query
  })
}

// 车辆认证刷新缓存
export function clearGarageApproveCache() {
  return request({
    url: '/carport/oss/carport/certifi/clear',
    method: 'post'
  })
}

// 经销商详情更新是否展示
export function updateAttrShow(data) {
  return request({
    url: '/carport/oss/shop/apply/merchant/updateAttr',
    method: 'post',
    data
  })
}

// 附近经销商列表
export function nearbyShopList(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/list/nearBy',
    url: '/shop/oss/settlement/shop/apply/nearList',
    method: 'get',
    params: query
  })
}
// 同营业执照经销商
export function registrationShopList(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/list/same/resisterNumber',
    url: '/shop/oss/merchant/info/list/same/resisterNumber',
    method: 'get',
    params: query
  })
}
// 同身份证的经销商
export function identificationShopList(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/list/same/identificationNumber',
    url: '/shop/oss/merchant/info/list/same/identificationNumber',
    method: 'get',
    params: query
  })
}

// 同营业执照经销商(给业务资质详情用)
export function registrationShopListV2(query) {
  return request({
    url: '/shop/oss/v2/shopInfo/same/registerNumber',
    method: 'get',
    params: query
  })
}
// 同身份证的经销商(给业务资质详情用)
export function identificationShopListV2(query) {
  return request({
    url: '/shop/oss/v2/shopInfo/same/identificationNumber',
    method: 'get',
    params: query
  })
}

// 款型保存图片
export function updataCarImg(data) {
  return request({
    url: '/carport/oss/car/img',
    method: 'POST',
    data
  })
}

// 查询款型属性
export function getTypeAttr(query) {
  return request({
    url: '/carport/oss/car/queryAttribute',
    method: 'GET',
    params: query
  })
}

// 修改款型上架状态
export function updateCarStatus(data) {
  return request({
    url: '/carport/oss/car/updateCarStatus',
    method: 'POST',
    params: data
  })
}

// 新增经销商
export function saveNewShop(data) {
  return request({
    url: '/carport/oss/shop/apply/merchant/save',
    method: 'POST',
    params: data
  })
}

// OSS经销商新增
export function saveMerchantInfo(data) {
  return request({
    url: '/shop/oss/merchant/info/save',
    method: 'POST',
    data
  })
}

// 校验店铺名称
export function queryShopInfoByName(query) {
  return request({
    url: `${APIURL}carport/business/shop/queryShopInfoByName`,
    method: 'GET',
    params: query
  })
}

// 经销商审核通过（根据ID）
export function ShopApplyAgreeById(data) {
  return request({
    url: '/carport/oss/merchant/apply/agreeByApplyId',
    method: 'post',
    data
  })
}

/**
 * 推送车辆
 * @param data
 */
export function goodsPushCars(data) {
  return request({
    url: '/carport/oss/goods/pushCar',
    method: 'post',
    data
  })
}

/**
 * 纠错列表
 * @param data
 */
export function getFeedbackList(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/feedback/list',
    url: '/shop/oss/merchant/feedback/list',
    method: 'GET',
    params: query
  })
}

/**
 * 纠错列表
 * @param data
 */
export function getFeedbackDetail(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/feedback/detail',
    url: '/shop/oss/merchant/feedback/detail',
    method: 'GET',
    beforeAlterFirst: true,
    params: query
  })
}

/**
 * 纠错更新
 * @param data
 */
export function feedbackUpdate(data) {
  return request({
    // url: '/carport/oss/shop/apply/merchant/feedback/update',
    url: '/shop/oss/merchant/feedback/update',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10607',
    data
  })
}

/**
 * 更新品牌授权状态
 * @param data
 */
export function updateBrandAuthStatus(data) {
  return request({
    url: '/carport/oss/shop/apply/updateBrandAuthStatus',
    method: 'post',
    data
  })
}

/**
 * 查询经销商与款型配置
 * @param query
 */
export function GetShopCarFind(query) {
  return request({
    url: 'trade/oss/shop/car/find',
    method: 'get',
    params: query
  })
}

/**
 * 保存经销商与款型配置
 * @param data
 */
export function SaveTradeOssShopCar(data) {
  return request({
    url: 'trade/oss/shop/car/save',
    method: 'post',
    data
  })
}

/**
 * 品牌审核详情编辑
 * @param data
 */
export function updateBrandAuthorization(data) {
  return request({
    // url: 'carport/oss/shop/apply/update',
    url: '/shop/oss/shopBrand/apply/update',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10604',
    data
  })
}

/**
 * 删除商铺销售品牌
 * @param data
 */
export function deleteShopBrand(data) {
  return request({
    url: '/shop/oss/shop/apply/deleteShopBrand',
    method: 'post',
    data
  })
}

/**
 * 添加商铺销售品牌
 * @param data
 */
export function addShopBrand(data) {
  return request({
    url: '/carport/oss/shop/apply/addShopBrand',
    method: 'post',
    data
  })
}

/**
 * 商家可销售车辆列表
 * @param query
 */
export function listGoodsForSaleBrand(query) {
  return request({
    url: '/carport/oss/goods/listGoodsForSaleBrand',
    method: 'GET',
    params: query
  })
}

/**
 * 添加品牌下所有可售车辆
 * @param query
 */
export function addAllBrandGoods(query) {
  return request({
    url: '/shop/oss/shop/apply/addAllBrandGoods',
    method: 'post',
    params: query
  })
}

/**
 * 编辑商铺销售车辆
 * @param data
 */
export function editShopGoods(data) {
  return request({
    url: '/shop/oss/merchant/editShopGoods',
    method: 'post',
    data
  })
}

// 询价列表
export function priceQueryList(query) {
  return request({
    url: '/shop/oss/query/price/list',
    method: 'get',
    params: query
  })
}

// 询价业务处理任务列表
export function InquiryBusinessProcessingList(query) {
  return request({
    url: '/shop/oss/query/price/task/list',
    method: 'get',
    params: query
  })
}

/*
 * 领取任务
 * @param data
 */
export function postPendingReview(data) {
  return request({
    url: '/shop/oss/query/price/receive',
    method: 'post',
    data
  })
}

// 询价列表
export function getDetailsList(query) {
  return request({
    url: '/shop/oss/query/price/new/list',
    method: 'get',
    params: query
  })
}

// 个人询价列表
export function getPersonalInquiry(query) {
  return request({
    url: '/shop/oss/query/price/userList',
    method: 'get',
    params: query
  })
}

// 获取经销商信息
export function getDealerInformation(query) {
  return request({
    url: '/shop/oss/query/price/shopList',
    method: 'get',
    params: query
  })
}

// 提交询价业务处理
export function submissionProcessing(data) {
  return request({
    url: '/shop/oss/query/price/process',
    method: 'post',
    data
  })
}

// 线索赠送记录
export function GiftRecord(query) {
  return request({
    url: '/shop/oss/query/price/give/record',
    method: 'get',
    params: query
  })
}

// 微信线索赠送记录
export function wechatGiftRecord(query) {
  return request({
    url: '/shop/oss/shop/clue/settings/record/list',
    method: 'get',
    params: query
  })
}

// 赠送线索
export function GiveClues(data) {
  return request({
    url: '/shop/oss/query/price/give',
    method: 'post',
    data
  })
}

// 线索赠送记录
export function getReturnVisitRecords(query) {
  return request({
    url: '/carport/oss/price/query/returnRecord',
    method: 'get',
    params: query
  })
}

// 获取用户的标签
export function getUserLabel(query) {
  return request({
    url: '/carport/oss/price/query/userTag/get',
    method: 'get',
    params: query
  })
}

// 询价excel文档下载
export function downloadExcel(query) {
  return request({
    url: '/carport/oss/price/query/downloadExcel',
    method: 'get',
    params: query
  })
}

/*
 * 更改商家响应状态
 * @param data
 */
export function updateShopResponseStatus(data) {
  return request({
    url: '/shop/oss/query/price/update/status',
    method: 'post',
    data
  })
}

/*
 * 运维人员修改询价状态
 * @param data
 */
export function updateInquiryStatus(data) {
  return request({
    url: '/shop/oss/query/price/update/ownStatus',
    method: 'post',
    data
  })
}

/*
 * 运营人员添加备注
 * @param data
 */
export function updateAddNotes(data) {
  return request({
    url: '/shop/oss/query/price/update/description',
    method: 'post',
    data
  })
}

/*
 * 处理线索
 * @param data
 */
export function ClueProcessing(data) {
  return request({
    url: '/shop/oss/query/price/handle/complaints',
    method: 'post',
    data
  })
}

/*
 * 重新发送询价短信
 * @param data
 */
export function priceQueryResend(data) {
  return request({
    url: '/shop/oss/query/price/resend',
    method: 'post',
    data
  })
}

// 品牌审核列表
export function merchantBrandAuthList(query) {
  return request({
    // url: '/carport/oss/shop/apply/merchantBrandAuthList',
    url: '/shop/oss/shopBrand/apply/merchantBrandAuthList',
    method: 'get',
    params: query
  })
}

// 品牌审核通过
export function agreeMerchantBrandAuth(data) {
  return request({
    // url: '/carport/oss/shop/apply/agreeMerchantBrandAuth',
    url: '/shop/oss/shopBrand/apply/agreeMerchantBrandAuth',
    method: 'post',
    data
  })
}

// 品牌审核拒绝
export function unagreeMerchantBrandAuthList(data) {
  return request({
    // url: '/carport/oss/shop/apply/unagreeMerchantBrandAuthList',
    url: '/shop/oss/shopBrand/apply/unagreeMerchantBrandAuthList',
    method: 'post',
    data
  })
}

// 删除经销商缓存
export function clearShopCache() {
  return request({
    url: '/shop/oss/shop/apply/clear/cache',
    method: 'post'
  })
}

// 活动管理更新状态
export function updateStatus(data) {
  return request({
    url: '/activity/oss/admin/activity/updateStatus',
    method: 'post',
    data
  })
}

export function updateShopStatus(data) {
  return request({
    url: '/carport/oss/shop/apply/updateShopStatus',
    method: 'post',
    data
  })
}

// 纠错一级页面 goodsName page limit
export function errorRecovery1(params) {
  return request({
    url: '/carport/oss/goods/feedback/modelList',
    method: 'get',
    params
  })
}
// 纠错二级页面 goodsId page limit
export function errorRecovery2(params) {
  return request({
    url: '/carport/oss/goods/feedback/carInfoList',
    method: 'get',
    params
  })
}
// 纠错三级页面 falsId
export function errorRecovery3(params) {
  return request({
    url: '/carport/oss/goods/feedback/attributeList',
    method: 'get',
    params
  })
}
// 纠错是否处理 falsId；isSolved 是否处理： 1已处理
export function errorRecoveryHandler(data) {
  return request({
    url: '/carport/oss/goods/feedback/user/batch/solved',
    method: 'post',
    data
  })
}
// 纠错是否有效 falsId；isEffective 是否有效：0无效，1有效 (未见使用，废弃)
export function errorRecoveryEffective(data) {
  return request({
    url: '/carport/oss/goods/feedback/updateIsEffective',
    method: 'post',
    data
  })
}
// 品牌审核列表（二手车）
export function getMerchantUsedCarAuthList(query) {
  return request({
    url: '/carport/oss/shop/apply/merchantUsedCarAuthList',
    method: 'get',
    params: query
  })
}
// 授权品牌详情（二手车）
export function getMerchantUsedCarDetail(params) {
  return request({
    url: '/carport/oss/shop/apply/detail',
    method: 'get',
    beforeAlterFirst: true,
    params
  })
}
// 授权品牌批量通过（二手车）
export function postErrorRecoveryEffective(data) {
  return request({
    url: '/carport/oss/shop/apply/agreeUsedCarBrandAuthList',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10608',
    data
  })
}
// 授权品牌驳回通过（二手车）
export function postUnagreeUsedCarAuthList(data) {
  return request({
    url: '/carport/oss/shop/apply/unagreeUsedCarAuthList',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10608',
    data
  })
}
// 二手车列表
export function postUsedCarList(params) {
  return request({
    url: '/carport/oss/usedCar/list',
    method: 'get',
    params
  })
}
// 盲水印 图片管理
export function getBlindImage(query) {
  return request({
    url: '/carport/oss/car/listImageByFolder',
    method: 'get',
    params: query
  })
}
// 批量上架（二手车）
export function postUsedCarFrame(data) {
  return request({
    url: '/carport/oss/usedCar/lot',
    method: 'post',
    data
  })
}
// 批量下架（二手车）
export function postUndercarriageUsedCar(data) {
  return request({
    url: '/carport/oss/usedCar/batch',
    method: 'post',
    data
  })
}
// 根据品牌查车型
export function getBrandCheckModel(params) {
  return request({
    url: '/carport/oss/goods/listGoodsForSaleBrand',
    method: 'get',
    params
  })
}

// 二手车管理详情
export function getUsedCarDetail(params) {
  return request({
    url: '/carport/oss/usedCar/detail',
    method: 'get',
    params
  })
}

// 编辑二手车管理详情
export function postUpdateUsedCar(data) {
  return request({
    url: '/carport/oss/usedCar/update',
    method: 'post',
    data
  })
}

// 违章列表查询
export function getIllegalList(params) {
  return request({
    url: '/carport/oss/check/illegal/list',
    method: 'get',
    params
  })
}

// 新车购买列表
export function getBuyCarList(params) {
  return request({
    url: '/carport/oss/new/car/purchase/list',
    method: 'get',
    params
  })
}

// 购买新车修改状态
export function addBuyCar(data) {
  return request({
    url: '/carport/oss/new/car/purchase/user/save',
    method: 'post',
    data
  })
}

// 购买新车修改状态
export function updateBuyCarStatus(data) {
  return request({
    url: '/carport/oss/new/car/purchase/user/update/status',
    method: 'post',
    data
  })
}

// 车辆下视频列表
export function getVideosList(query) {
  return request({
    url: '/carport/oss/goods/info/videos',
    method: 'get',
    params: query
  })
}

// 二手车列表(二手车板块)
export function getListCars(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/listCars',
    method: 'get',
    params: query
  })
}

// 二手车优质列表(二手车板块)
export function getApplyListCars(query) {
  return request({
    url: '/transaction/shop/quality/apply/list',
    method: 'get',
    params: query
  })
}

// 二手车优质修改状态
export function applyHandle(data) {
  return request({
    url: '/transaction/shop/quality/apply/handle',
    method: 'post',
    data
  })
}

// 二手车修改状态
export function updateUsedCarStatus(data) {
  return request({
    url: '/transaction/adminMotorUsedCarController/updateStatus',
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    data
  })
}

// 二手车审核列表(二手车审核列表)
export function getAuditList(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/auditList',
    method: 'get',
    params: query
  })
}

// 二手车修改状态
export function updateAuditStatus(data) {
  return request({
    url: '/transaction/adminMotorUsedCarController/audit',
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    data
  })
}

/**
 * 根据经销商信息查询经销商
 * @param query
 */
export function GetShopApplyByName(query) {
  return request({
    // url: '/carport/oss/shop/apply/findShopByName',
    url: '/shop/oss/merchant/info/findShopByName',
    method: 'get',
    params: query
  })
}

/**
 * 根据经销商ID获取经销商
 * @param query
 */
export function SearchShopByShopId(shopId) {
  return request({
    url: `/shop/oss/business/merchant/info/${shopId}`,
    method: 'get'
  })
}
/**
 * 根据经销商ID获取经销商
 * @param query
 */
export function SearchShopByShopName(query) {
  return request({
    url: `/shop/oss/business/merchant/searchShop`,
    method: 'get',
    params: query
  })
}

/**
 * 发票列表
 * @param query
 */
export function GetInvoiceManageList(query) {
  return request({
    url: '/invoice/oss/manage/list',
    method: 'get',
    params: query
  })
}

/**
 * 发票详情
 * @param query
 */
export function GetInvoiceManageDetail(query) {
  return request({
    url: '/invoice/oss/manage/detail',
    method: 'get',
    params: query
  })
}

// 修改发票状态
export function updateInvoiceStatus(data) {
  return request({
    url: '/invoice/oss/manage/updateStastus',
    method: 'post',
    data
  })
}

export function InvoiceTop(data) {
  return request({
    url: '/invoice/oss/manage/expedited',
    method: 'post',
    data
  })
}

// 录入发票
export function SaveInvoiceEnter(data) {
  return request({
    url: '/invoice/oss/manage/enter',
    method: 'post',
    data
  })
}

/**
 * 经销商服务费发票列表
 * @param query
 */
export function GetInvoiceOrderList(query) {
  return request({
    // url: '/carport/oss/order/findInvoiceOrderList',
    url: '/shop/oss/member/order/findInvoiceOrderList',
    method: 'get',
    params: query
  })
}

/**
 * 经销商订单退款
 * @param data
 */
export function ReFundShopOrder(data) {
  return request({
    url: `/shop/oss/member/level/order/refund`,
    method: 'post',
    data
  })
}

/**
 * 经销商订单作废
 * @param data
 */
export function cancelShopOrder(data) {
  return request({
    url: `/shop/oss/member/level/order/cancel`,
    method: 'post',
    data
  })
}

/**
 * 经销商订单作废-新
 * @param data
 */
export function cancelShopOrderInvalid(data) {
  return request({
    url: `/shop/oss/member/level/order/invalid`,
    method: 'post',
    data
  })
}
/**
 *  * 经销商订单修改付费方式
 * @param data
 */
export function updateMemberType(data) {
  return request({
    // url: `/carport/oss/order/updateMemberType`,
    url: `/shop/oss/member/order/updateMemberType`,
    method: 'post',
    data
  })
}

/**
 * 二手车审核列表，未审核数
 */
export function getUnAuditCount() {
  return request({
    url: `/transaction/adminMotorUsedCarController/audit/unAuditCount`,
    method: 'get'
  })
}

/**
 * 二手车审核列表，待审核列表
 * @param query
 */
export function getUsedAuditList(query) {
  return request({
    url: `/transaction/adminMotorUsedCarController/audit/list`,
    method: 'get',
    params: query
  })
}

/**
 * 二手车审核列表,拉取数据
 * @param data
 */
export function getPullList(data) {
  return request({
    url: `/transaction/adminMotorUsedCarController/audit/pull/data`,
    method: 'post',
    data
  })
}

/**
 * 官方私信发送记录列表(二手车)
 * @param query
 */
export function getMessageList(query) {
  return request({
    url: `/uic/oss/private/message/list`,
    method: 'get',
    params: query
  })
}

/**
 * 官方私信发送接口
 * @param data
 */
export function postMessage(data) {
  return request({
    url: `/uic/oss/private/message/create`,
    method: 'post',
    data
  })
}

/**
 * 经销商订单修改合同编号
 * @param data
 */
export function orderUpdateContract(data) {
  return request({
    // url: `/carport/oss/order/updateContract`,
    url: `/shop/oss/member/order/updateContract`,
    method: 'post',
    data
  })
}

/**
 * 经销商订单修改订单备注
 * @param data
 */
export function orderUpdateDetail(data) {
  return request({
    // url: `/carport/oss/order/updateDetail`,
    url: `/shop/oss/member/order/updateRemark`,
    method: 'post',
    data
  })
}

/**
 * 查询热搜词或品牌名
 * @param query
 */
export function getListKeyWord(query) {
  return request({
    url: `/transaction/adminMotorSecondHandCarKeyWordController/listKeyWord`,
    method: 'get',
    params: query
  })
}

/**
 * 模糊下拉品牌
 * @param query
 */
export function getListBrandByName(query) {
  return request({
    url: `/transaction/adminMotorSecondHandCarKeyWordController/listBrandByName`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商申请在线售车列表
 * @param query
 */
export function getShopTradeApplyList(query) {
  return request({
    url: `/trade/oss/shop/trade/apply/list`,
    method: 'get',
    params: query
  })
}
/**
 * 保存关键词
 * @param data
 */
export function saveKeyWord(data) {
  return request({
    url: `/transaction/adminMotorSecondHandCarKeyWordController/saveKeyWord`,
    method: 'post',
    data
  })
}

/**
 * 经销商申请在线售车列表"处理"操作
 * @param data
 */
export function updateOnlineCarSalesStatus(data) {
  return request({
    url: `/carport/oss/shop/apply/updateOnlineCarSalesStatus`,
    method: 'post',
    data
  })
}

/**
 * 私信发送者列表
 * @param query
 */
export function getAccountList(query) {
  return request({
    url: `/uic/oss/private/message/account/list`,
    method: 'get',
    params: query
  })
}

/*
 * 审核统计汇总(二手车)
 * @param query
 */
export function getStatistics(query) {
  return request({
    url: `/transaction/adminMotorUsedCarController/audit/statistics`,
    method: 'get',
    params: query
  })
}

/**
 * 款型列表，同步新车型到在售车辆列表
 * @param data
 */
export function syncNewCar(data) {
  return request({
    url: `/carport/oss/car/syncNewCar`,
    method: 'post',
    data
  })
}

/**
 * 经销商提现申请列表
 * @param query
 */
export function getShopWithDrawList(query) {
  return request({
    url: `/shop/oss/withdraw/list`,
    method: 'get',
    params: query
  })
}

/**
 * OSS经销商提现申请流水
 * @param query
 */
export function getShopSerialsWithdraw(query) {
  return request({
    url: `/pirate/user/credit/oss/shopSerials/withdraw/list`,
    method: 'get',
    params: query
  })
}

/**
 * 提现审核
 * @param data
 */
export function postWithdrawApply(data) {
  return request({
    url: `/pirate/user/credit/oss/shopSerials/withdraw/apply`,
    method: 'post',
    data
  })
}

/**
 * 完成打款
 * @param data
 */
export function postWithdrawComplete(data) {
  return request({
    url: `/pirate/user/credit/oss/shopSerials/withdraw/complete`,
    method: 'post',
    data
  })
}

/**
 * 提现详情
 * @param query
 */
export function getShopSerialsWithdrawInfo(query) {
  return request({
    url: `/pirate/user/credit/oss/shopSerials/withdraw/info`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商提现申请列表---当前批次提现总金额
 * @param query
 */
export function getShopWithDrawTotalMoney(query) {
  return request({
    url: `/shop/oss/withdraw/totalMoney`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商提现申请详情页
 * @param query
 */
export function getShopWithDrawDetail(query) {
  return request({
    url: `/shop/oss/withdraw`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商提现申请详情页--->确认打款
 * @param data
 */
export function approvePay(data) {
  return request({
    url: `/shop/oss/withdraw/pay/approve`,
    method: 'post',
    data
  })
}

/**
 * 经销商提现申请详情页--->拒绝提现
 * @param data
 */
export function denyPay(data) {
  return request({
    url: `/shop/oss/withdraw/pay/deny`,
    method: 'post',
    data
  })
}

/**
 * 经销商提现申请详情页--->拒绝提现
 * @param data
 */
export function savePayUser(data) {
  return request({
    // url: `/carport/oss/order/payUser/save`,
    url: `/shop/oss/member/order/payUser/save`,
    method: 'post',
    data
  })
}

/**
 * 线索业务付费订单页--->保存付款人
 * @param data
 */
export function saveCluePayUser(data) {
  return request({
    url: `/shop/oss/clue/payUser/save`,
    method: 'post',
    data
  })
}

/**
 * 二手车订单--->订单列表
 * @param query
 */
export function getUsedListOrder(query) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/listOrder`,
    method: 'get',
    params: query
  })
}

/**
 * 二手车订单--->日志列表
 * @param query
 */
export function getUsedListRecord(query) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/listRecord`,
    method: 'get',
    params: query
  })
}
/**
 * 二手车订单--->退款协商记录
 * @param query
 */
export function GetRefundTalkRecording(query) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/refundRecord/list`,
    method: 'get',
    params: query
  })
}
/**
 * 二手车订单--->同意退款
 * @param data
 */
export function postUsedOrderAgree(data) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/agree`,
    method: 'post',
    data
  })
}

/**
 * 二手车订单--->退款_取消订单
 * @param data
 */
export function postOrderRefund(data) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/order/refund`,
    method: 'post',
    data
  })
}

/**
 * 询价业务处理--->我的工作
 * @param query
 */
export function getmyWorkList(query) {
  return request({
    url: '/shop/oss/query/price/myWork',
    method: 'get',
    params: query
  })
}

/**
 * 二手车订单--->拒绝退款
 * @param data
 */
export function postUsedOrderDisAgree(data) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/disAgree`,
    method: 'post',
    data
  })
}

/**
 * 二手车订单--->保存订单日志
 * @param data
 */
export function postSaveRemark(data) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/saveRemark`,
    method: 'post',
    data
  })
}

/**
 * 经销商权益表列表查询
 * @param query
 */
export function getShopEquityList(query) {
  return request({
    url: `/shop/oss/equity/list`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商权益表--->更改到期时间
 * @param data
 */
export function UpdateEquityTime(data) {
  return request({
    url: `/shop/oss/equity/update`,
    method: 'post',
    data
  })
}

/**
 * 经销商权益表----根据订单号模糊查询shopId
 * @param query
 */
export function getEquityFindOrderList(query) {
  return request({
    url: `/shop/oss/equity/findOrderList`,
    method: 'get',
    params: query
  })
}

/**
 * 二手车审核，我的审核列表
 * @param query
 */
export function getMyTodayList(query) {
  return request({
    url: `/transaction/adminMotorUsedCarController/audit/myTodayList`,
    method: 'get',
    params: query
  })
}

/**
 * 车库问题反馈列表
 * @param query
 */
export function getGarageList(query) {
  return request({
    url: `/carport/oss/goods/feedback/garage/list`,
    method: 'get',
    params: query
  })
}

/**
 * 车库问题反馈列表--->车库反馈处理
 * @param data
 */
export function updateGarageHandle(data) {
  return request({
    url: `/carport/oss/goods/feedback/garage/handle`,
    method: 'post',
    data
  })
}

/**
 * 二手车订单---> 手动退款
 * @param data
 */
export function updateUsedCarOrderRefund(data) {
  return request({
    url: `/transaction/adminMotorUsedCarOrderController/refund`,
    method: 'post',
    data
  })
}

// 试驾记录
export function driveRecord(query) {
  return request({
    url: '/shop/oss/trial/list',
    method: 'get',
    params: query
  })
}

// 赠送试驾
export function GiveDrive(data) {
  return request({
    url: '/shop/oss/trial/give',
    method: 'post',
    data
  })
}

// 微信赠送配置
export function GiveWechatClue(data) {
  return request({
    // url: '/shop/oss/wechat/give',
    url: '/shop/oss/shop/clue/settings/config',
    method: 'post',
    data
  })
}

// 手动录入线索订单
export function createManuallyClueOrder(data) {
  return request({
    url: `/shop/oss/clue/order/createManually`,
    method: 'post',
    data
  })
}

// 手动录入直通车订单
export function createThroughTrainClueOrder(data) {
  return request({
    url: `/shop/oss/account/order/createManually`,
    method: 'post',
    data
  })
}

// 有赞报表列表查询
export function GetGoingBlackList(params) {
  return request({
    url: '/transaction/adminMotorSecondHandCarBlackLicenseController/listBlackLicense',
    method: 'get',
    params
  })
}

// 保存黑名单
export function SaveBlackListReport(data) {
  return request({
    url: '/transaction/adminMotorSecondHandCarBlackLicenseController/saveMotorSecondHandCarBlackLicense',
    method: 'post',
    data
  })
}

// 删除黑名单
export function updateBlack(data) {
  return request({
    url: '/transaction/adminMotorSecondHandCarBlackLicenseController/deleteById',
    method: 'post',
    data
  })
}

// 行驶证黑名单有效性操作
export function validityOperation(data) {
  return request({
    url: '/transaction/adminMotorSecondHandCarBlackLicenseController/update/status',
    method: 'post',
    data
  })
}

// 行驶证黑名单查看相关账户
export function viewRelatedAccounts(query) {
  return request({
    url: '/transaction/adminMotorSecondHandCarBlackLicenseController/related/user',
    method: 'get',
    params: query
  })
}

// 行驶证黑名单查看日志
export function blacklistViewLog(query) {
  return request({
    url: '/transaction/adminMotorSecondHandCarBlackLicenseController/operate/log',
    method: 'get',
    params: query
  })
}
// 手动录入金币充值订单
export function createGoldOrderSave(data) {
  return request({
    url: `/shop/oss/gold/balance/order/save`,
    method: 'post',
    data
  })
}

// 金币赠送记录
export function GoldRecord(query) {
  return request({
    url: '/shop/oss/gold/give/record/list',
    method: 'get',
    params: query
  })
}

// 金币赠送线索
export function GiveGold(data) {
  return request({
    url: '/shop/oss/gold/give/record/save',
    method: 'post',
    data
  })
}

// 一口价线索记录
export function getOnePriceClueList(query) {
  return request({
    url: '/trade/oss/car/listSubscribe',
    method: 'get',
    params: query
  })
}

// 获取经销商驾校信息
export function GetDrivingSchoolInfo(params) {
  return request({
    url: '/shop/oss/shop/driving/school/info',
    method: 'get',
    params
  })
}

// 全量保存经销商驾考信息
export function SaveShopDrivingSchool(data) {
  return request({
    url: '/shop/oss/shop/driving/save/school',
    method: 'post',
    data
  })
}

//驾考报名日志-保存日志
export function SaveDrivingLogger(data) {
  return request({
    url: '/shop/oss/shop/driving/school/log/save',
    method: 'post',
    data
  })
}

//驾考报名日志-获取列表
export function GetDrivingLoggerList(params) {
  return request({
    url: '/shop/oss/shop/driving/school/log/list',
    method: 'get',
    params
  })
}

// 二手车更新数据
export function UpdateUsedCarData(data) {
  return request({
    url: '/transaction/adminMotorUsedCarController/updateData',
    method: 'post',
    data
  })
}

// 驾考报名列表
export function GetDrivingSchoolList(params) {
  return request({
    url: '/shop/oss/shop/driving/school/list',
    method: 'get',
    params
  })
}
// 驾考报名列表详情
export function GetDrivingSchoolListDetail(params) {
  return request({
    url: '/shop/oss/shop/driving/school/list/detail',
    method: 'get',
    params
  })
}

/**
 * 城市官任务审批, 回填店铺id
 * @param data
 */
export function saveNewShopId(data) {
  return request({
    url: '/carport/oss/adminCityOfficerController/fill/shopId',
    method: 'post',
    data
  })
}

/**
 * 城市官任务审批, 创建新店
 * @param data
 */
export function createNewShop(data) {
  return request({
    url: '/carport/oss/shop/apply/createNewShop',
    method: 'post',
    data
  })
}
// oss查询热门价格词
export function GetListHotPrice(query) {
  return request({
    url: '/transaction/adminMotorSecondHandCarHotPriceController/listHotPrice',
    method: 'get',
    params: query
  })
}

// 保存热门价格词
export function SaveHotPrice(data) {
  return request({
    url: '/transaction/adminMotorSecondHandCarHotPriceController/saveHotPrice',
    method: 'post',
    data
  })
}

export function activityExpire(data) {
  return request({
    url: '/activity/oss/admin/activity/expire',
    method: 'post',
    data
  })
}

export function activityAudit(data) {
  return request({
    url: '/activity/oss/admin/activity/audit',
    method: 'post',
    data
  })
}

// oss查询所有的热门品牌词
export function GetListHotBrand(query) {
  return request({
    url: '/transaction/adminMotorSecondHandCarHotPriceController/listHotBrand',
    method: 'get',
    params: query
  })
}

// 保存热门价格词
export function SaveHotBrand(data) {
  return request({
    url: '/transaction/adminMotorSecondHandCarHotPriceController/saveHotBrand',
    method: 'post',
    data
  })
}

// 置顶冲突时间查询
export function GetConflictList(params) {
  return request({
    url: '/activity/oss/admin/activity/getConflictList',
    method: 'get',
    params
  })
}

export function ListByShopId(params) {
  return request({
    url: '/shop/oss/contacts/listByShopId',
    method: 'get',
    params
  })
}

// 删除图片v2
export function deleteImageV2(data) {
  return request({
    url: '/carport/oss/goods/del/imageV2',
    method: 'post',
    data
  })
}

// oss二手车置顶订单列表
export function GetStickOrderList(query) {
  return request({
    url: '/transaction/stick/order/list',
    method: 'get',
    params: query
  })
}
// oss租车置顶订单列表
export function GetRentStickOrderList(query) {
  return request({
    url: '/rental/oss/stick/order/list',
    method: 'get',
    params: query
  })
}

// oss 金币流转
export function GetGoldChangeList(query) {
  return request({
    url: '/shop/oss/gold/change/list',
    method: 'get',
    params: query
  })
}

// 厂家列表
export function GetBrandFactoryList(query) {
  return request({
    url: '/factory/oss/brand/factory/list',
    method: 'get',
    params: query
  })
}

// 订单列表
export function GetActivityOrderList(query) {
  return request({
    url: '/activity/oss/activity/order/list',
    method: 'get',
    params: query
  })
}
// OSS广告订单列表
export function GetAdvertOrderList(query) {
  return request({
    // url: '/advert/oss/order/list',
    url: '/shop/oss/advert/order/list',
    method: 'get',
    params: query
  })
}

// 厂商冷却基础量配置
export function BrandFactoryCoolConfig(data) {
  return request({
    url: '/factory/oss/brand/factory/cool/config',
    method: 'post',
    data
  })
}

// 赠送厂商次数
export function BrandFactoryCoolGive(data) {
  return request({
    url: '/factory/oss/brand/factory/cool/give',
    method: 'post',
    data
  })
}

// 配置记录
export function GetBrandFactoryCoolConfigRecord(query) {
  return request({
    url: '/factory/oss/brand/factory/cool/config/record',
    method: 'get',
    params: query
  })
}
// 赠送厂商操作记录
export function GetBrandFactoryCoolGiveRecord(query) {
  return request({
    url: '/factory/oss/brand/factory/cool/give/record',
    method: 'get',
    params: query
  })
}

// OSS广告订单详情
export function GetAdvertOrderDetail(query) {
  return request({
    // url: '/advert/oss/order/detail',
    url: '/shop/oss/advert/order/detail',
    method: 'get',
    params: query
  })
}

// 品牌舆情开关
export function UpdateOpinionStatus(data) {
  return request({
    url: '/carport/oss/brand/updateOpinionStatus',
    method: 'post',
    data
  })
}

// 人员列表
export function GetBrandUserList(query) {
  return request({
    url: '/factory/oss/account/brand/user/list',
    method: 'get',
    params: query
  })
}

// 负面词查询
export function getNegativeWord(query) {
  return request({
    url: '/factory/oss/negative/word/query',
    method: 'get',
    params: query
  })
}
// 负面词插入
export function postInsertNegativeWord(data) {
  return request({
    url: '/factory/oss/negative/word/insert',
    method: 'post',
    data
  })
}
// 插入负面词（批量）
export function postInsertBatNegativeWord(data) {
  return request({
    url: '/factory/oss/negative/word/batchInsert',
    method: 'post',
    data
  })
}
// 负面词删除
export function postDeleteNegativeWord(data) {
  return request({
    url: '/factory/oss/negative/word/deleteById',
    method: 'post',
    data
  })
}
// 负面词清空
export function postDeleteAllNegativeWord(data) {
  return request({
    url: '/factory/oss/negative/word/deleteAll',
    method: 'post',
    data
  })
}

// OSS广告订单,消耗金币数
export function GetAdvertOrderSumGold(query) {
  return request({
    // url: '/advert/oss/order/sumGold',
    // url: '/shop/oss/advert/order/sumGold',
    url: '/shop/oss/advert/order/sumGoldAndMoney',
    method: 'get',
    params: query
  })
}

// OSS广告订单,修改支付人
export function updatePayUser(data) {
  return request({
    // url: '/advert/oss/order/update/payUser',
    url: '/shop/oss/advert/order/update/payUser',
    method: 'post',
    data
  })
}

// OSS广告订单,支持完善订单信息
export function orderCompleteOrder(data) {
  return request({
    // url: '/advert/oss/order/complete/order',
    url: '/shop/oss/advert/order/complete/order',
    method: 'post',
    data
  })
}
// 新增人员
export function AddBrandUser(data) {
  return request({
    url: '/factory/oss/account/brand/user/add',
    method: 'post',
    data
  })
}

// 编辑人员
export function EditBrandUser(data) {
  return request({
    url: '/factory/oss/account/brand/user/edit',
    method: 'post',
    data
  })
}

// 删除人员
export function DelBrandUser(data) {
  return request({
    url: '/factory/oss/account/brand/user/del',
    method: 'post',
    params: data
  })
}

// 冷却管理,文章标题模糊匹配
export function GetCoolUserFuzzyQueryByTitle(query) {
  return request({
    url: '/factory/oss/cool/fuzzyQueryByTitle',
    method: 'get',
    params: query
  })
}

// 冷却管理
export function GetCoolUserRecordList(query) {
  return request({
    url: '/factory/oss/cool/record/list',
    method: 'get',
    params: query
  })
}

// 投诉维权列表
export function GetComplainList(query) {
  return request({
    url: '/shop/oss/complain/list',
    method: 'get',
    params: query
  })
}

// 投诉维权详情
export function complainDetail(query) {
  return request({
    url: '/shop/oss/complain/detail',
    method: 'get',
    params: query
  })
}

// 投诉维权修改
export function updateComplain(data) {
  return request({
    url: '/shop/oss/complain/update',
    method: 'post',
    data
  })
}

// 投诉要求类型列表
export function GetComplainTypeList(query) {
  return request({
    url: '/shop/oss/complain/typeList',
    method: 'get',
    params: query
  })
}

// 投诉要求类型列表
export function GetComplainRequireTypeList(query) {
  return request({
    url: '/shop/oss/complain/requireTypeList',
    method: 'get',
    params: query
  })
}

// 投诉维权统计
export function GetComplainAuditStatistics(query) {
  return request({
    url: '/shop/oss/complain/audit/statistics',
    method: 'get',
    params: query
  })
}

// 投诉维权未审核列表
export function GetComplainAuditUnAuditList(query) {
  return request({
    url: '/shop/oss/complain/audit/unAudit/list',
    method: 'get',
    params: query
  })
}

// 投诉维权拉取数据
export function GetComplainPullData(data) {
  return request({
    url: '/shop/oss/complain/audit/pullData',
    method: 'post',
    data
  })
}

// 投诉维权审核列表
export function GetComplainAuditRecord(query) {
  return request({
    url: '/shop/oss/complain/audit/auditRecord',
    method: 'get',
    params: query
  })
}
// 投诉维权审核接口
export function updateComplainAudit(data) {
  return request({
    url: '/shop/oss/complain/audit',
    method: 'post',
    data
  })
}

// 电话询价线索
export function queryPriceCallList(query) {
  return request({
    url: '/shop/oss/query/price/call/list',
    method: 'get',
    params: query
  })
}

// 微信询价线索
export function queryPriceWechatList(query) {
  return request({
    url: '/shop/oss/query/price/wechat/list',
    method: 'get',
    params: query
  })
}

// 商家端成长计划-获取列表接口
export function growthEssayList(query) {
  return request({
    url: '/shop/oss/growth/essay/list',
    method: 'get',
    params: query
  })
}
// 线索赠送列表
export function getGuestResourcelist(query) {
  return request({
    url: '/factory/oss/guest/resource/list',
    method: 'get',
    params: query
  })
}

// 商家端成长计划-删除文章
export function growthEssayDel(data) {
  return request({
    url: '/shop/oss/growth/essay/del',
    method: 'post',
    data
  })
}
// 赠送线索
export function getGuestResourceGift(data) {
  return request({
    url: '/factory/oss/guest/resource/gift',
    method: 'post',
    data
  })
}

// 商家端成长计划-保存文章
export function growthEssaySave(data) {
  return request({
    url: '/shop/oss/growth/essay/save',
    method: 'post',
    data
  })
}
// 厂商线索赠送明细
export function getGiftConsumeDetail(query) {
  return request({
    url: '/carport/oss/price/query/gift/consume/detail',
    method: 'get',
    params: query
  })
}

// 商家线索禁用
export function resourceUpdateStatus(data) {
  return request({
    url: '/factory/oss/guest/resource/updateStatus',
    method: 'post',
    data
  })
}

// 获取类型管理操作日志记录
export function getOperateLog(query) {
  return request({
    url: '/shop/oss/merchant/info/getlistBusinessOperateLog',
    method: 'get',
    params: query
  })
}

/**
 * 获取线索赠送在售车型的经销商
 * @param query
 */
export function getListShopForGoodIds(query) {
  return request({
    url: '/shop/oss/merchant/listShopForGoodIds',
    method: 'get',
    params: query
  })
}

/**
 * 获取线索赠送编辑详细
 * @param query
 */
export function getResourceDetail(id) {
  return request({
    url: `/factory/oss/guest/resource/detail/${id}`,
    method: 'get'
  })
}

/**
 * 保存编辑线索赠送
 * @param query
 */
export function saveResourceEdit(data) {
  return request({
    url: '/factory/oss/guest/resource/edit',
    method: 'post',
    data
  })
}
/**
 * 口碑管理操作
 * @param query
 */
export function MouthOperate(data) {
  return request({
    url: '/carport/oss/brand/updateHighQualityStatus',
    method: 'post',
    data
  })
}
/**
 * 商家受限检查
 * @param params
 */
export function MerchantsLimited(params) {
  return request({
    url: '/transaction/shopApp/motorUsedCarController/shop/limit',
    method: 'get',
    params
  })
}

/* 添加店铺人员
 * @param query
 */
export function addShopContacts(data) {
  return request({
    url: '/shop/oss/contacts/addContact',
    method: 'post',
    data
  })
}
/**
 * 查看k口碑操作日志
 * @param query
 */
export function viewLogs(params) {
  return request({
    url: `/carport/oss/goods/getWordOfMouthLog`,
    method: 'get',
    params
  })
}
/**
 * 经销商二手车受限编辑
 * @param data
 */
export function MerchantsLimitedCompile(data) {
  return request({
    url: '/shop/oss/merchant/info/second/limit',
    method: 'post',
    data
  })
}
/**
 * 二手车个人模板开启
 * @param data
 */
export function MerchantInfoUpdateBenefit(data) {
  return request({
    url: '/shop/oss/merchant/info/update/benefit',
    method: 'post',
    data
  })
}

/* 修改店铺人员
 * @param query
 */
export function updateShopContact(data) {
  return request({
    url: '/shop/oss/contacts/updateContact',
    method: 'post',
    data
  })
}

// 微信线索开关
export function setCanReceiveWechatClue(data) {
  return request({
    url: '/shop/oss/contacts/canReceiveWechatClue',
    method: 'post',
    data
  })
}

// 微信线索开关
export function setCanReceiveMobileClue(data) {
  return request({
    url: '/shop/oss/contacts/canReceiveMobileClue',
    method: 'post',
    data
  })
}

// 驾考线索开关
export function setDrivingTestClue(data) {
  return request({
    url: '/shop/oss/contacts/canReceiveDrivingSchoolClue',
    method: 'post',
    data
  })
}

// 租车线索开关
export function setCanReceiveRentalClue(data) {
  return request({
    url: '/shop/oss/contacts/canReceiveRentalClue',
    method: 'post',
    data
  })
}

// 收车线索开关
export function setCanReceiveColletionCarClue(data) {
  return request({
    url: '/shop/oss/contacts/canReceiveColletionCarClue',
    method: 'post',
    data
  })
}

/**
 * 删除店铺人员
 * @param query
 */
export function delShopContact(data) {
  return request({
    url: '/shop/oss/contacts/delContact',
    method: 'post',
    data
  })
}
/**
 * 车型数据开关
 * @param query
 */
export function updateGoodsAnalyse(data) {
  return request({
    url: '/carport/oss/brand/updateGoodsAnalyse',
    method: 'post',
    data
  })
}
/**
 * 品牌商家数据开关
 * @param query
 */
export function updateShopDataAnalyse(data) {
  return request({
    url: '/carport/oss/brand/updateShopDataAnalyse',
    method: 'post',
    data
  })
}
/**
 * 广告数据开关
 * @param query
 */
export function updateAdvertDataAnalyse(data) {
  return request({
    url: '/carport/oss/brand/updateAdvertDataAnalyse',
    method: 'post',
    data
  })
}
/**
 * 内容宝开关
 * @param query
 */
export function updateContentManage(data) {
  return request({
    url: '/carport/oss/brand/updatecontentManage',
    method: 'post',
    data
  })
}
/**
 * 投放宝开关
 * @param query
 */
export function updateBrandLaunchTreasureStatus(data) {
  return request({
    url: '/carport/oss/brand/updateBrandLaunchTreasureStatus',
    method: 'post',
    data
  })
}

/**
 * 投诉维权
 * @param query
 */
export function updateComplaintFeedback(data) {
  return request({
    url: '/carport/oss/brand/updateComplaintFeedback',
    method: 'post',
    data
  })
}

/**
 * 获取用户现有处罚信息
 * @param params
 */
export function getExistPunish(params) {
  return request({
    // url: '/carport/oss/punish/getExistPunish',
    url: '/shop/oss/violation/punish/shop/info',
    method: 'get',
    params
  })
}

/**
 * 二手车解冻
 * @param query
 */
export function unfreezeCar(data) {
  return request({
    url: '/carport/oss/punish/unfreezeCar',
    method: 'post',
    data
  })
}

/**
 * 线索订单退款
 * @param
 */
export function clueOrderRefund(data) {
  return request({
    url: '/shop/oss/clue/order/refund',
    method: 'post',
    data
  })
}

/**
 * 经销商线索业务付费订单作废
 * @param
 */
export function clueOrderCancelManually(data) {
  return request({
    url: '/shop/oss/clue/order/cancelManually',
    method: 'post',
    data
  })
}

/** 结款
 * @param query
 */
export function transferComplete(data) {
  return request({
    url: '/pirate/user/credit/oss/shopSerials/transfer/complete',
    method: 'post',
    data
  })
}

/**
 * 线索订单总金额
 * @param
 */
export function clueTotalAmount(params) {
  return request({
    url: '/shop/oss/clue/totalAmount',
    method: 'get',
    params
  })
}

/* 获取转账订单的流水
 * @param query
 */
export function transferSerials(params) {
  return request({
    url: '/pirate/user/credit/oss/shopSerials/transfer/serials',
    method: 'get',
    params
  })
}

// 租车举报详情
export function getReportDetail(query) {
  return request({
    url: '/rental/oss/admin/car/report/detail',
    method: 'get',
    params: query
  })
}

// 二手车异常用户规则管理列表页
export function getUniqueRuleList(query) {
  return request({
    url: '/uic/oss/exception/rule/list',
    method: 'get',
    params: query
  })
}
// 待迁移列表
export function listToBeMigrated(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/move/listCars',
    method: 'get',
    params: query
  })
}

// 二手车操作日志-查询
export function getOperationLog(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/car/operate/business/log',
    method: 'get',
    params: query
  })
}

/**
 * 二手车异常用户编辑规则
 * @param
 */
export function editUniqueRule(data) {
  return request({
    url: '/uic/oss/exception/rule/edit',
    method: 'post',
    data
  })
}

/** 结款
 * @param query
 */
export function addOperateLog(data) {
  return request({
    url: '/transaction/adminMotorUsedCarController/add/operate/log',
    method: 'post',
    data
  })
}
// 迁移
export function getMigration(data) {
  return request({
    url: '/transaction/adminMotorUsedCarController/move/cars',
    method: 'post',
    data
  })
}

// 场景日志查询
export function getUniqueLog(query) {
  return request({
    url: '/uic/oss/exception/rule//queryUserLimitLog',
    method: 'get',
    params: query
  })
}

// 场景编辑
export function updateScene(data) {
  return request({
    url: '/uic/oss/exception/rule/updateScene',
    method: 'post',
    data
  })
}

// 异常用户列表页
export function getUniqueUserList(query) {
  return request({
    url: '/uic/oss/exception/rule/audit/list',
    method: 'get',
    params: query
  })
}
// 二手车异常用户详情页
export function getUniqueUserDetail(query) {
  return request({
    url: '/uic/oss/exception/rule/audit/detail',
    method: 'get',
    params: query
  })
}

/**
 * 异常处理
 * @param
 */
export function editUniqueUser(data) {
  return request({
    url: '/uic/oss/exception/rule/manage',
    method: 'post',
    data
  })
}
// 历史处理记录
export function getHistoryRecord(query) {
  return request({
    url: '/uic/oss/exception/rule/history/audit/log',
    method: 'get',
    params: query
  })
}
// 厂家-管理的操作日志
export function getOperateBrandLogList(query) {
  return request({
    url: '/carport/oss/brand/getOperateBrandLogList',
    method: 'get',
    params: query
  })
}
/**
 * OSS商家账户明细汇总_金额合计
 * @param query
 */
export function GetShopSerialsAccountTotal(query) {
  return request({
    url: `/pirate/user/credit/oss/shop/wallet/record/accountTotal`,
    method: 'get',
    params: query
  })
}

// 厂家-黑名单列表
export function getBrandBlackList(query) {
  return request({
    url: '/factory/oss/brand/black/list',
    method: 'get',
    params: query
  })
}

/** 厂家-新增黑名单
 * @param query
 */
export function postBrandBlackSave(data) {
  return request({
    url: '/factory/oss/brand/black/save',
    method: 'post',
    data
  })
}

/** 厂家-删除黑名单
 * @param query
 */
export function postBrandBlackDelete(data) {
  return request({
    url: '/factory/oss/brand/black/delete',
    method: 'post',
    data
  })
}

/** 持久化oss文件
 * @param data
 */
export function aliyunPersist(data) {
  return request({
    url: `${APIURL}pirate/media/aliyun/persist`,
    method: 'post',
    data
  })
}

// AXN虚拟号管理
export function getAxnList(query) {
  return request({
    url: '/pretty/number/oss/info/axnList',
    method: 'get',
    params: query
  })
}

// 手机号状态查询 (拨测)
export function getQueryMobileStatus(query) {
  return request({
    url: '/pretty/number/oss/info/queryMobileStatus',
    method: 'get',
    params: query
  })
}

/** 解绑AXN
 * @param query
 */
export function unbindAxn(data) {
  return request({
    url: `/pretty/number/oss/info/unbindAxn`,
    method: 'post',
    data
  })
}

/** 经销商-解绑AXN
 * @param query
 */
export function shopUnbindVirtualMobile(data) {
  return request({
    url: `/shop/oss/merchant/info/unbind/virtual/mobile`,
    method: 'post',
    data
  })
}

/** 更新AXN的可用状态
 * @param query
 */
export function updateAxnStatus(data) {
  return request({
    url: '/pretty/number/oss/info/updateAxnStatus',
    method: 'post',
    data
  })
}

/** AXN虚拟号总数
 * @param query
 */
export function getAxnCount(query) {
  return request({
    url: '/pretty/number/oss/info/axnCount',
    method: 'get',
    params: query
  })
}

/** 虚拟号换绑
 * @param query
 */
export function updateVirtualMobile(data) {
  return request({
    url: '/shop/oss/merchant/info/updateVirtualMobile',
    method: 'post',
    data
  })
}

// 虚拟号操作日志
export function getSecretNoLog(query) {
  return request({
    url: '/pretty/number/oss/info/secretNoLog',
    method: 'get',
    params: query
  })
}

// AXB虚拟号管理
export function getAxbList(query) {
  return request({
    url: '/pretty/number/oss/info/axbList',
    method: 'get',
    params: query
  })
}

/** 经销商详情更改商家品牌销售渠道
 * @param query
 */
export function updateShopBrandChannel(data) {
  return request({
    url: '/shop/oss/shop/apply/updateShopBrandChannel',
    method: 'post',
    data
  })
}

/** 经销商详情更改商家品牌销售渠道
 * @param query
 */
export function updateChannelOnMidMax(data) {
  return request({
    url: '/shop/oss/shop/apply/update/brand/channelOnMidMax',
    method: 'post',
    data
  })
}

/** 获取 商家渠道配置
 * @param query
 */
export function getBrandChannelCfg(params) {
  return request({
    url: '/carport/oss/brand/channel/cfg',
    method: 'get',
    params
  })
}

// 厂家订单列表
export function getFactoryOrderList(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderList',
    method: 'get',
    params: query
  })
}
// 二手车列表(二手车板块)
export function getNewListCars(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/ossSecondHandCarList',
    method: 'get',
    params: query
  })
}

// 线索客户列表
export function getClueCustomerList(query) {
  return request({
    url: '/shop/oss/clue/customer/list',
    method: 'get',
    params: query
  })
}

// 厂家订单详情
export function getFactoryOrderDetail(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderDetail',
    method: 'get',
    params: query
  })
}
// 保存厂家订单
export function saveFactoryOrderInfo(data) {
  return request({
    url: '/factory/oss/factory/order/saveOrderInfo',
    method: 'post',
    data
  })
}

// 厂家订单操作日志
export function getFactoryOrderOperateRecord(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderOperateRecord',
    method: 'get',
    params: query
  })
}

// 更新订单详情
export function updateFactoryOrderInfo(data) {
  return request({
    url: '/factory/oss/factory/order/updateOrderInfo',
    method: 'post',
    data
  })
}

// 订单项目列表
export function getFactoryOrderProjectList(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderProjectList',
    method: 'get',
    params: query
  })
}
// 保存订单项目
export function saveFactoryOrderProjectInfo(data) {
  return request({
    url: '/factory/oss/factory/order/saveOrderProjectInfo',
    method: 'post',
    data
  })
}

// 订单项目详情
export function getFactoryOrderProjectDetail(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderProjectDetail',
    method: 'get',
    params: query
  })
}

// 编辑订单项目详情
export function editFactoryOrderProjectInfo(data) {
  return request({
    url: '/factory/oss/factory/order/editOrderProjectInfo',
    method: 'post',
    data
  })
}
// 订单支付记录列表
export function getFactoryOrderPayRecordList(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderPayRecordList',
    method: 'get',
    params: query
  })
}
// 保存订单支付记录
export function saveFactoryOrderPayRecordInfo(data) {
  return request({
    url: '/factory/oss/factory/order/saveOrderPayRecordInfo',
    method: 'post',
    data
  })
}
// 废弃厂家订单收款记录
export function cancelFactoryPayRecord(data) {
  return request({
    url: '/factory/oss/factory/order/cancelPayRecord',
    method: 'post',
    data
  })
}

// 废弃订单
export function updateAbandOrderInfo(data) {
  return request({
    url: '/factory/oss/factory/order/abandOrderInfo',
    method: 'post',
    data
  })
}

// 厂家订单详情,条件查询
export function getOrderListByParam(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderListByParam',
    method: 'get',
    params: query
  })
}

// 线索客户列表
export function getClueCustomerHistoryList(query) {
  return request({
    url: '/shop/oss/clue/customer/change/history/list',
    method: 'get',
    params: query
  })
}

// 租车线索列表
export function getRentalClueList(query) {
  return request({
    url: '/rental/oss/admin/car/clue/list',
    method: 'get',
    params: query
  })
}
// 获取新二手车详情
export function getNewCarDetail(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/car/detail',
    method: 'get',
    params: query
  })
}
// 判断手机号是否注册
export function checkRegister(query) {
  return request({
    url: '/factory/oss/account/brand/checkRegister',
    method: 'get',
    params: query
  })
}

// 更新厂家订单订单价格
export function updateOrderPrice(data) {
  return request({
    url: '/factory/oss/factory/order/updateOrderPrice',
    method: 'post',
    data
  })
}

// axn 城市获取
export function getCityList(data) {
  return request({
    url: '/pretty/number/oss/info/city/list',
    method: 'post',
    data
  })
}

// 厂家订单 新增合同
export function addOrderContract(data) {
  return request({
    url: '/factory/oss/factory/order/addOrderContract',
    method: 'post',
    data
  })
}

// 厂家订单 删除合同
export function cancelOrderContract(data) {
  return request({
    url: '/factory/oss/factory/order/cancelOrderContract',
    method: 'post',
    data
  })
}

// 厂家订单 新增发票
export function applyInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/applyInvoice',
    method: 'post',
    data
  })
}

// 更新原始标签中对应的说明-关联到文章
export function carReleateEassy(data) {
  return request({
    url: '/carport/oss/car/releate/eassy',
    method: 'post',
    data
  })
}

// 厂家订单 通过开票
export function passInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/passInvoice',
    method: 'post',
    data
  })
}

// 厂家订单 拒绝开票
export function rejectInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/rejectInvoice',
    method: 'post',
    data
  })
}

// 厂家订单 废弃开票
export function abandonInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/abandonInvoice',
    method: 'post',
    data
  })
}

// 厂家订单 取消开票
export function cancelApplyInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/cancelApplyInvoice',
    method: 'post',
    data
  })
}

// 厂家订单 发票列表页
export function getFactoryInvoicePage(query) {
  return request({
    url: '/factory/oss/factory/order/invoicePage',
    method: 'get',
    params: query
  })
}

// 厂家订单 发票详情
export function getFactoryInvoiceDetail(query) {
  return request({
    url: '/factory/oss/factory/order/invoiceDetail',
    method: 'get',
    params: query
  })
}

// 厂家订单 流水列表页
export function getPagePayRecord(query) {
  return request({
    url: '/factory/oss/factory/order/PagePayRecord',
    method: 'get',
    params: query
  })
}

// 厂家订单 流水总金额
export function getSumReceivedPrice(query) {
  return request({
    url: '/factory/oss/factory/order/sumReceivedPrice',
    method: 'get',
    params: query
  })
}
// 获取原始标签中对应的说明-关联到文章
export function getCarReleateEassy(query) {
  return request({
    url: '/carport/oss/car/releate/eassyList',
    method: 'get',
    params: query
  })
}
// 判断手机号是否注册
export function getQESaleCarCnt(query) {
  return request({
    url: '/shop/oss/shop/apply/getQESaleCarCnt',
    method: 'get',
    params: query
  })
}

// 车辆列表-统计数据
export function goodsSearchListCarNum(query) {
  return request({
    url: '/carport/oss/goods/search/list/carNum',
    method: 'get',
    params: query
  })
}
// 厂家订单 编辑发票
export function editInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/editInvoice',
    method: 'post',
    data
  })
}
// 厂家订单 编辑发票(财务)
export function editFinanceInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/finance/editInvoice',
    method: 'post',
    data
  })
}
// 通话录音列表
export function getMemberMobileRecordList(query) {
  return request({
    url: '/shop/oss/v2/shopInfo/getMemberMobileRecordList',
    method: 'get',
    params: query
  })
}

// OSS经销上广告订单,修改订单信息
export function updateShopOrder(data) {
  return request({
    url: '/shop/oss/advert/order/update/order',
    method: 'post',
    data
  })
}

// 同品牌下存在相同的车型名称
export function goodsCheckCarmodelExist(query) {
  return request({
    url: '/carport/oss/goods/check/carmodel/exist',
    method: 'get',
    params: query
  })
}

// 厂家订单详情,查询合同列表
export function getOrderProjectListByParam(query) {
  return request({
    url: '/factory/oss/factory/order/getOrderProjectListByParam',
    method: 'get',
    params: query
  })
}

// 更新允许厂家查看
export function updateFactoryCheck(data) {
  return request({
    url: '/shop/oss/merchant/brand/updateFactoryCheck',
    method: 'post',
    data
  })
}

// 设置了免打扰
export function setclueCustomerDisturbFree(data) {
  return request({
    url: '/shop/oss/clue/customer/setclueCustomerDisturbFree',
    method: 'post',
    data
  })
}

// 款型图片复制
export function updateCopyCarImgs(data) {
  return request({
    url: '/carport/oss/car/copyCarImgs',
    method: 'post',
    data
  })
}

// 厂家详情
export function getFactoryDetail(query) {
  return request({
    url: '/factory/oss/brand/factory/detail',
    method: 'get',
    params: query
  })
}

// 关注车型列表
export function getGoodsAnalyseList(query) {
  return request({
    url: '/carport/oss/goods/analyse/list',
    method: 'get',
    params: query
  })
}

// 新增关注车型
export function addAnalyse(data) {
  return request({
    url: '/factory/oss/brand/factory/addAnalyse',
    method: 'post',
    data
  })
}

// 移除车型
export function delAnalyse(data) {
  return request({
    url: '/factory/oss/brand/factory/delAnalyse',
    method: 'post',
    data
  })
}

// 冷却操作(不扣减次数)
export function addCoolRecord(data) {
  return request({
    url: '/factory/oss/cool//essay/cool',
    method: 'post',
    data
  })
}

// 冷却操作
export function addCoolCool(data) {
  return request({
    url: '/factory/oss/cool/cool',
    method: 'post',
    data
  })
}

// 冷却列表
export function getEssayCoolList(query) {
  return request({
    url: '/factory/oss/cool/essay/list',
    method: 'get',
    params: query
  })
}

// 根据动态id置顶
export function topByMomentId(data) {
  return request({
    url: '/carport/oss/goods/topByMomentId',
    method: 'post',
    data
  })
}

// 厂家详情下评论置顶操作记录
export function getReplyFactoryTopList(query) {
  return request({
    url: '/reply/oss/auth/replyFactoryTopList',
    method: 'get',
    params: query
  })
}

// oss置顶评论
export function topReply(data) {
  return request({
    url: '/reply/oss/auth/topReply',
    method: 'post',
    data
  })
}

// 厂商冷却基础量配置
export function topReplyConfig(data) {
  return request({
    url: '/factory/oss/brand/factory/topReply/config',
    method: 'post',
    data
  })
}

// 开关评论置顶
export function updateTopReplyStatus(data) {
  return request({
    url: '/carport/oss/brand/updateTopReplyStatus',
    method: 'post',
    data
  })
}

// 投放宝开关
export function updateLaunchPool(data) {
  return request({
    url: '/carport/oss/brand/updateLaunchPool',
    method: 'post',
    data
  })
}

// 厂家关联的经销商列表
export function getBrandRelatedShopList(query) {
  return request({
    url: '/shop/oss/merchant/info/brandRelatedShopList',
    method: 'get',
    params: query
  })
}

// 更新口碑置顶次数
export function updateTopGoodsScoreNum(data) {
  return request({
    url: '/factory/oss/brand/updateTopGoodsScoreNum',
    method: 'post',
    data
  })
}

//  厂家订单 重建发票
export function reapplyInvoice(data) {
  return request({
    url: '/factory/oss/factory/order/reapplyInvoice',
    method: 'post',
    data
  })
}

// 商家订单服务-根据单号获取退款详情
export function getRefundOrderDesc(query) {
  return request({
    url: '/unite/order/oss/refundOrderDesc',
    method: 'get',
    params: query
  })
}

// 根据单号获取退款详情
export function getRefundDesc(query) {
  return request({
    url: '/unite/order/oss/refundDesc',
    method: 'get',
    params: query
  })
}

//  OA流程发起退款
export function refundByOA(data) {
  return request({
    url: '/shop/oss/service/order/refundNewOrderByOA',
    method: 'post',
    data
  })
}

//  款型删除
export function deleteImageV3(data) {
  return request({
    url: '/carport/oss/goods/del/imageV3',
    method: 'post',
    data
  })
}

// 变更状态
export function updateLaunchStatus(data) {
  return request({
    url: '/factory/oss/launch/updateStatus',
    method: 'post',
    data
  })
}

// 投放中已使用的车型id
export function getLaunchUsedGoodsIds(data) {
  return request({
    url: '/factory/oss/launch/getLaunchUsedGoodsIds',
    method: 'post',
    data
  })
}

// 日志
export function getPageTreasuresLog(query) {
  return request({
    url: '/forum/oss/placingTreasuresController/pageTreasuresLog',
    method: 'get',
    params: query
  })
}

// 内容池列表
export function getPagePlacingTreasures(query) {
  return request({
    url: '/forum/oss/placingTreasuresController/pagePlacingTreasures',
    method: 'get',
    params: query
  })
}

// 内容池列表
export function pagePlacingTreasuresWithAllGoodsIds(query) {
  return request({
    url: '/forum/oss/placingTreasuresController/pagePlacingTreasuresWithAllGoodsIds',
    method: 'get',
    params: query
  })
}

// 根据车型获取关联文章数据
export function getRelationNum(query) {
  return request({
    url: '/forum/oss/placingTreasuresController/getRelationNum',
    method: 'get',
    params: query
  })
}

// 新建或保存内容池
export function saveOrUpdatePlacingTreasures(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/saveOrUpdatePlacingTreasures',
    method: 'post',
    data
  })
}

// 内容池内容列表
export function getPagePlacingTreasuresEssays(query) {
  return request({
    url: '/forum/oss/placingTreasuresController/pagePlacingTreasuresEssays',
    method: 'get',
    params: query
  })
}

// 新经销商主订单接口
export function getServiceOrderList(query) {
  return request({
    url: '/shop/oss/service/order/list',
    method: 'get',
    params: query
  })
}

// 审核内容池文章
export function auditPlacingEssay(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/auditPlacingEssay',
    method: 'post',
    data
  })
}

// 移除投放
export function removePlacing(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/removePlacing',
    method: 'post',
    data
  })
}

// 停止投放
export function stopPlacing(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/stopPlacing',
    method: 'post',
    data
  })
}

// 新增投放内容
export function addPlacing(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/addPlacing',
    method: 'post',
    data
  })
}

// 暂停投放
export function pausePlacing(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/pausePlacing',
    method: 'post',
    data
  })
}

// 恢复暂停的投放
export function continuePlacing(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/continuePlacing',
    method: 'post',
    data
  })
}

// 投放列表
export function getLaunchList(query) {
  return request({
    url: '/factory/oss/launch/list',
    method: 'get',
    params: query
  })
}

// 新经销商主订单-详情
export function getServiceOrderDetail(query) {
  return request({
    url: '/shop/oss/service/order/detail',
    method: 'get',
    params: query
  })
}

// 投放详情
export function getLaunchDetail(query) {
  return request({
    url: '/factory/oss/launch/detail',
    method: 'get',
    params: query
  })
}

// 服务订单列表
export function getServiceList(query) {
  return request({
    url: '/shop/oss/service/order/getServiceList',
    method: 'get',
    params: query
  })
}

// 新增投放
export function addLaunchData(data) {
  return request({
    url: '/factory/oss/launch/add',
    method: 'post',
    data
  })
}

// 更新订单付款人
export function editServiceOrderPayUser(data) {
  return request({
    url: '/shop/oss/service/order/payUser/save',
    method: 'post',
    data
  })
}

// 编辑投放
export function editLaunchData(data) {
  return request({
    url: '/factory/oss/launch/edit',
    method: 'post',
    data
  })
}

export function getIlleagelImage(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/illeagel/image',
    method: 'get',
    params: query
  })
}
// 摩豆明细列表
export function motoBeanDetailList(data) {
  return request({
    url: '/pirate/user/credit/oss/motoBeans/getSerialsList',
    method: 'post',
    data
  })
}

// 编辑订单生效时间
export function editMemberExpireTime(data) {
  return request({
    url: '/shop/oss/service/order/editMemberExpireTime',
    method: 'post',
    data
  })
}

// 订单服务取消
export function serviceCancel(data) {
  return request({
    url: '/shop/oss/service/order/serviceCancel',
    method: 'post',
    data
  })
}

// 批量发放摩豆
export function batchReleaseMotoBean(data) {
  return request({
    url: '/supply/oss/bean/batchSend',
    method: 'post',
    data
  })
}
// 完善订单（线下）（ehr审核通过，仅业务转移订单）
export function serviceOrderComplete(data) {
  return request({
    url: '/shop/oss/service/order/complete',
    method: 'post',
    data
  })
}

// 图片去水印
export function getOriginalUrl(query) {
  return request({
    url: '/carport/oss/goods/getOriginalUrl',
    method: 'get',
    params: query
  })
}
// 虚拟号黑名单
export function getBlackList(query) {
  return request({
    url: '/shop/oss/shop/apply/getShopVirtualBlackList',
    method: 'get',
    params: query
  })
}

// 删除经销商店铺，提醒
export function deleteShopNotice(query) {
  return request({
    url: '/shop/oss/shop/apply/delete/shop/notice',
    method: 'get',
    params: query
  })
}

// 完善订单-二维码（ehr审核通过，仅业务转移订单）
export function serviceOrderCompleteGenQrCode(data) {
  return request({
    url: '/shop/oss/service/order/complete/genQrCode',
    method: 'post',
    data
  })
}

// 订单服务作废
export function serviceOrderInvalide(data) {
  return request({
    url: '/shop/oss/service/order/serviceInvalid',
    method: 'post',
    data
  })
}

// 订单服务退款
export function serviceOrderRefund(data) {
  return request({
    url: '/shop/oss/service/order/serviceRefund',
    method: 'post',
    data
  })
}

// 获取订单服务-转移单差价
export function serviceOrderActualPrice(query) {
  return request({
    url: '/shop/oss/service/order/actual/price',
    method: 'get',
    params: query
  })
}

// 经销商删除列表-日志
export function getBusinessOperateLoglist(query) {
  return request({
    url: '/shop/oss/merchant/info/getBusinessOperateLoglist',
    method: 'get',
    params: query
  })
}

// 虚拟号黑名单
export function getBlackListLog(query) {
  return request({
    url: '/shop/oss/merchant/info/getBusinessOperateLoglist',
    method: 'get',
    params: query
  })
}

// 获取经销商订单-多张发票日志
export function orderInvoiceRecordlist(query) {
  return request({
    url: '/invoice/oss/manage/orderInvoiceRecordlist',
    method: 'get',
    params: query
  })
}
// 添加 经销商 虚拟号 黑名单
export function addBlackList(data) {
  return request({
    url: '/shop/oss/shop/apply/addVirtualMobileBlack',
    method: 'post',
    data
  })
}

// 删除经销商 虚拟号 黑名单
export function delBlackList(data) {
  return request({
    url: '/shop/oss/shop/apply/deleteVirtualMobileBlack',
    method: 'post',
    data
  })
}

// 商家订单列表-生成支付二维码
export function serviceOrderGenPayQrCode(query) {
  return request({
    url: 'shop/oss/service/order/genPayQrCode',
    method: 'get',
    params: query
  })
}
// 上传代付凭证
export function uploadT(data) {
  return request({
    url: '/shop/oss/service/order/add/payOtherVoucher',
    method: 'post',
    data
  })
}
// 新经销商主订单接口导出
export function exportServiceOrderList(query) {
  return request({
    url: '/shop/oss/service/order/listExport',
    method: 'get',
    params: query
  })
}

// 经销商订单充值明细列表
export function orderRechargeGetList(data) {
  return request({
    url: '/report/shop/order/recharge/getList',
    method: 'post',
    data
  })
}

// 经销商订单充值明细列表-导出
export function orderRechargeExportList(data) {
  return request({
    url: '/report/shop/order/recharge/exportList',
    method: 'post',
    data
  })
}

// 经销商订单充值汇总信息
export function orderRechargeGetSummaryInfo(data) {
  return request({
    url: '/report/shop/order/recharge/getSummaryInfo',
    method: 'post',
    data
  })
}

// 获取全部订单列表,附带合作项目id
export function getSelectAllOrderWithProjectId(query) {
  return request({
    url: '/factory/oss/factory/order/selectAllOrderWithProjectId',
    method: 'get',
    params: query
  })
}

// 收车线索列表
export function getSecondHandClueList(query) {
  return request({
    url: '/shop/oss/second/hand/clue/list',
    method: 'get',
    params: query
  })
}

// 获取支付渠道
export function getPayChannel(query) {
  return request({
    url: '/shop/oss/service/order/pay/account/detail',
    method: 'get',
    params: query
  })
}

// 提交维修、救援图片审核
export function subShopCarImageApplyInfo(data) {
  return request({
    url: '/shop/oss/merchant/info/subShopCarImageApplyInfo',
    method: 'post',
    data
  })
}
// 删除维修、救援图片审核
export function deleteCarImage(data) {
  return request({
    url: '/shop/oss/merchant/info/deleteCarImage',
    method: 'post',
    data
  })
}

// 新建分组
export function postAddGroup(data) {
  return request({
    url: '/carport/oss/car/addGroup',
    method: 'post',
    data
  })
}

// 提交维修、救援图片审核 ----拒绝
export function refuseCarImageApply(data) {
  return request({
    url: '/shop/oss/merchant/info/refuseCarImageApply',
    method: 'post',
    data
  })
}

// 提交维修、救援图片审核 ----通过
export function agreeCarImageApply(data) {
  return request({
    url: '/shop/oss/merchant/info/agreeCarImageApply',
    method: 'post',
    data
  })
}
// 分组列表
export function getGroupList(params) {
  return request({
    url: '/carport/oss/car/groupList',
    method: 'get',
    params
  })
}

// 调整分组
export function postUpdateGroup(data) {
  return request({
    url: '/carport/oss/car/updateGroup',
    method: 'post',
    data
  })
}

// 提交维修、救援图片审核列表
export function getShopCarImageApplyList(query) {
  return request({
    url: '/shop/oss/merchant/info/getShopCarImageApplyList',
    method: 'get',
    params: query
  })
}

// 提交维修、救援图片审核列表--详情
export function getShopCarImageApplyDetail(query) {
  return request({
    url: '/shop/oss/merchant/info/getShopCarImageApplyDetail',
    method: 'get',
    params: query
  })
}

// 电话预警列表
export function getMobileWarningList(query) {
  return request({
    url: '/shop/oss/mobile/warning/list',
    method: 'get',
    params: query
  })
}

// 更新合作项目执行状态
export function updateProjectProcessStatus(data) {
  return request({
    url: '/factory/oss/factory/order/updateProjectProcessStatus',
    method: 'post',
    data
  })
}
// 删除分组
export function postDeleteGroup(data) {
  return request({
    url: '/carport/oss/car/deleteGroup',
    method: 'post',
    data
  })
}

// 集团公司列表
export function getCompanyList(query) {
  return request({
    url: '/expands/oss/ehr/companyList',
    method: 'get',
    params: query
  })
}

// 厂家订单合作项目列表
export function getProjectList(query) {
  return request({
    url: '/factory/oss/factory/order/getProjectPage',
    method: 'get',
    params: query
  })
}

// 厂家订单合作项目数据统计
export function getProjectTotalData(query) {
  return request({
    url: '/factory/oss/factory/order/projectTotalData',
    method: 'get',
    params: query
  })
}

// 分组排序
export function postSortGroup(data) {
  return request({
    url: '/carport/oss/car/sortGroup',
    method: 'post',
    data
  })
}

// 二手车运营标记
export function postOperationMark(data) {
  return request({
    url: '/transaction/adminMotorUsedCarController/operation/mark',
    method: 'post',
    data
  })
}

// 图片下载
export function postDownload(data) {
  return request({
    url: '/pirate/media/oss/imgs/download',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 拍照识图列表
export function getRecognitionCarPage(query) {
  return request({
    url: '/carport/oss/recognition/car/page',
    method: 'get',
    params: query
  })
}

// 合同列表
export function getContractPage(query) {
  return request({
    url: '/factory/oss/factory/contract/getContractPage',
    method: 'get',
    params: query
  })
}
// 合同详情
export function getContractFullDetail(query) {
  return request({
    url: '/factory/oss/factory/contract/getContractFullDetail',
    method: 'get',
    params: query
  })
}

// 新增合同
export function addContractInfo(data) {
  return request({
    url: '/factory/oss/factory/contract/addContractInfo',
    method: 'post',
    data
  })
}

// 更新合同
export function updateContractInfo(data) {
  return request({
    url: '/factory/oss/factory/contract/updateContractInfo',
    method: 'post',
    data
  })
}

// 撤回合同
export function revertContract(data) {
  return request({
    url: '/factory/oss/factory/contract/revertContract',
    method: 'post',
    data
  })
}

// 业务订单分页
export function getBusinessOrderPage(query) {
  return request({
    url: '/factory/oss/factory/order/businessOrderPage',
    method: 'get',
    params: query
  })
}

// 业务订单详情
export function getBusinessOrderDetail(query) {
  return request({
    url: '/factory/oss/factory/order/businessOrderDetail',
    method: 'get',
    params: query
  })
}

// 新增业务订单
export function addBusinessOrderInfo(data) {
  return request({
    url: '/factory/oss/factory/order/addBusinessOrderInfo',
    method: 'post',
    data
  })
}

// 重新提交业务订单
export function reApplyBusinessOrderInfo(data) {
  return request({
    url: '/factory/oss/factory/order/reApplyBusinessOrderInfo',
    method: 'post',
    data
  })
}

// 撤回订单
export function revertOrder(data) {
  return request({
    url: '/factory/oss/factory/order/revertOrder',
    method: 'post',
    data
  })
}

// 订单绑定合同
export function bindOrderContract(data) {
  return request({
    url: '/factory/oss/factory/order/bindOrderContract',
    method: 'post',
    data
  })
}

// 订单解绑合同
export function unbindOrderContract(data) {
  return request({
    url: '/factory/oss/factory/order/unbindOrderContract',
    method: 'post',
    data
  })
}

// 相似管理页面
export function getSimilarAppealList(query) {
  return request({
    url: '/transaction/oss/appeal/similar/appeal/list',
    method: 'get',
    params: query
  })
}
/**
 * 设置销售
 * @param data
 */
export function updateShopSellerName(data) {
  return request({
    url: 'shop/oss/merchant/info/updateShop/sellerName',
    method: 'post',
    data
  })
}

/**
 * 违规标记数量
 * @param data
 */
export function getPunishCount(query) {
  return request({
    url: 'shop/oss/violation/punish/punish/count',
    method: 'get',
    params: query
  })
}

/**
 * 标记违规重置
 * @param data
 */
export function updatePunishReset(data) {
  return request({
    url: 'shop/oss/violation/punish/reset',
    method: 'post',
    data
  })
}

/**
 * 更新合同附件
 * @param data
 */
export function updateContractFile(data) {
  return request({
    url: '/factory/oss/factory/contract/updateContractFile',
    method: 'post',
    data
  })
}

/**
 * 二手车线索来源更新
 * @param data
 */
export function clueSourceUpdate(data) {
  return request({
    url: '/shop/oss/merchant/info/second/clueSourceUpdate',
    method: 'post',
    data
  })
}

// 实测列表
export function getMeasuredList(params) {
  return request({
    url: '/carport/oss/measured/list',
    method: 'get',
    params
  })
}

// 保存实测数据（新增和修改）
export function postMeasuredSave(data) {
  return request({
    url: '/carport/oss/measured/save',
    method: 'post',
    data
  })
}

// 设置商家成立时间
export function setEstablishTime(data) {
  return request({
    url: '/shop/oss/merchant/info/setEstablishTime',
    method: 'post',
    data
  })
}

// 经销商广告退款（正常）
export function advertsSrviceOrderRefund(data) {
  return request({
    url: '/shop/oss/advert/order/refund',
    method: 'post',
    data
  })
}

export function getEstablishTime(params) {
  return request({
    url: '/shop/oss/merchant/info/getEstablishTime',
    method: 'get',
    params
  })
}
// 获取处罚信息
export function getPunishInfo(params) {
  return request({
    url: '/shop/oss/merchant/info/shop/info/punish/bussinnes',
    method: 'get',
    params
  })
}
// 黑名单ID（身份证信息）
export function getCardBlack(params) {
  return request({
    url: '/transaction/oss/id/card/black/list',
    method: 'get',
    params
  })
}
// 取消处罚
export function cancelPunish(data) {
  return request({
    url: '/shop/oss/merchant/info/service/cancle/punish',
    method: 'post',
    data
  })
}
// 黑名单ID（身份证信息）保存
export function saveCardBlack(data) {
  return request({
    url: '/transaction/oss/id/card/black/save',
    method: 'post',
    data
  })
}
// 获取处罚日志
export function getlistBusinessPunishLog(params) {
  return request({
    url: '/shop/oss/merchant/info/service/getlistBusinessPunishLog',
    method: 'get',
    params
  })
}

// 黑名单ID（身份证信息）删除
export function deleteCardBlack(data) {
  return request({
    url: '/transaction/oss/id/card/black/delete',
    method: 'post',
    data
  })
}

/**
 * ugc活动权限开关
 * @param data
 */
export function updateUgcAct(data) {
  return request({
    url: '/carport/oss/brand/updateUgcAct',
    method: 'post',
    data
  })
}

// 获取关联订单金额
export function getEnterInvoiceNum(data) {
  return request({
    url: 'invoice/oss/manage/enterInvoiceNum',
    method: 'post',
    data
  })
}

// 经销商广告退卷(ehr)
export function refundAdverseOrderByOA(data) {
  return request({
    url: '/shop/oss/service/order/refundAdverseOrderByOA',
    method: 'post',
    data
  })
}
// 获取商家线索数据
export function getShopClueData(params) {
  return request({
    url: '/shop/oss/clue/number/detail',
    method: 'get',
    params
  })
}

// 二手车白名单列表 （60周岁）
export function getWhitelist(params) {
  return request({
    url: '/transaction/oss/whitelist/getPage',
    method: 'get',
    params
  })
}

// 二手车 白名单-添加 （60周岁）
export function postWhitelistAdd(params) {
  return request({
    url: '/transaction/oss/whitelist/add',
    method: 'post',
    params
  })
}

// 二手车 白名单-删除 （60周岁）
export function postWhitelistDelete(params) {
  return request({
    url: '/transaction/oss/whitelist/delete',
    method: 'post',
    params
  })
}

// 修改广告订单价格及合作项目
export function editOrderPriceAndProject(data) {
  return request({
    url: '/factory/oss/factory/order/editOrderPriceAndProject',
    method: 'post',
    data
  })
}

// 厂家发票信息列表
export function getInvoiceInfoList(params) {
  return request({
    url: '/factory/oss/factory/invoiceInfo/list',
    method: 'get',
    params
  })
}

// 厂家发票信息-新增
export function invoiceInfoAdd(data) {
  return request({
    url: '/factory/oss/factory/invoiceInfo/add',
    method: 'post',
    data
  })
}

// 厂家发票信息-修改
export function invoiceInfoEdit(data) {
  return request({
    url: '/factory/oss/factory/invoiceInfo/edit',
    method: 'post',
    data
  })
}

// 厂家发票信息-删除
export function invoiceInfoDelete(data) {
  return request({
    url: '/factory/oss/factory/invoiceInfo/delete',
    method: 'post',
    data
  })
}

// 获取审核中的订单信息(金额,合作项目)
export function getOrderDetailInAudit(params) {
  return request({
    url: '/factory/oss/factory/order/getOrderDetailInAudit',
    method: 'get',
    params
  })
}

// 负面车型
export function getNegativeCarList(params) {
  return request({
    url: '/carport/oss/score/negative/car/list',
    method: 'get',
    params
  })
}

// 经销商实人认证记录审核列表
export function getContactsAuthenList(params) {
  return request({
    url: '/shop/oss/contacts/authen/list',
    method: 'get',
    params
  })
}

// 负面车型下口碑列表
export function getNegativeList(params) {
  return request({
    url: '/carport/oss/score/negative/list',
    method: 'get',
    params
  })
}

// 新增负面车型
export function postNegativeCar(data) {
  return request({
    url: '/carport/oss/score/negative/car/save',
    method: 'post',
    data
  })
}

// 删除负面车型
export function postDelNegativeCar(data) {
  return request({
    url: '/carport/oss/score/negative/car/delete',
    method: 'post',
    data
  })
}

// 保存负面车型下口碑
export function postNegativeCarMouth(data) {
  return request({
    url: '/carport/oss/score/negative/saveOrUpdate',
    method: 'post',
    data
  })
}

// 自定义车型 列表
export function getBackList(params) {
  return request({
    url: '/carport/oss/custom/feed/back/list',
    method: 'get',
    params
  })
}

// 自定义车型 处理
export function updateBackHandle(data) {
  return request({
    url: '/carport/oss/custom/feed/back/handle',
    method: 'post',
    data
  })
}
// 经销商实人认证记录审核
export function contactsAuthenBusinessAudit(data) {
  return request({
    url: '/shop/oss/contacts/authen/businessAudit',
    method: 'post',
    data
  })
}
// 获取资质审核详情
export function getQualificationDetail(params) {
  return request({
    url: '/shop/oss/v2/shopInfo/approved/qualification',
    method: 'get',
    params
  })
}

// 合同协议店铺查询
export function getContractInfoQuery(params) {
  return request({
    url: '/shop/oss/contract/info/query',
    method: 'get',
    params
  })
}
// 合同协议操作日志
export function getContractInfoLog(params) {
  return request({
    url: '/shop/oss/contract/info/log',
    method: 'get',
    params
  })
}

// 合同协议修改合同状态
export function editContractInfo(data) {
  return request({
    url: '/shop/oss/contract/info/edit',
    method: 'post',
    data
  })
}

// add ride group
export function addModeGroup(data) {
  return request({
    url: '/carport/oss/car/modeAddGroup',
    method: 'post',
    data
  })
}
// delete ride group
export function delModeGroup(data) {
  return request({
    url: '/carport/oss/car/deleteModeGroup',
    method: 'post',
    data
  })
}
// get ride group
export function getModeGroup(params) {
  return request({
    url: '/carport/oss/car/modeGroupList',
    method: 'get',
    params
  })
}

// 封禁词分页列表
export function getCarLimitwords(params) {
  return request({
    url: '/carport/oss/limitwords/car/limitword',
    method: 'get',
    params
  })
}

// update ride group
export function updateModelGroup(data) {
  return request({
    url: '/carport/oss/car/updateModelHeight',
    method: 'post',
    data
  })
}

// 新增封禁词
export function saveLimitword(data) {
  return request({
    url: '/carport/oss/limitwords/car/saveLimitword',
    method: 'post',
    data
  })
}
// sort ride group
export function sortModelGroup(data) {
  return request({
    url: '/carport/oss/car/modeSortGroup',
    method: 'post',
    data
  })
}
// 删除封禁词
export function delLimitword(data) {
  return request({
    url: '/carport/oss/limitwords/car/delLimitword',
    method: 'post',
    data
  })
}

// 厂商&商家数据关联列表  导入记录_分页
export function getVendorMerchantAssocList(params) {
  return request({
    url: '/shop/oss/shopBrand/shopFactoryCheckImport/importRecordPage',
    method: 'get',
    params
  })
}

// 厂商&商家数据关联列表  导入结果详情
export function getVendorMerchantAssocDetail(params) {
  return request({
    url: '/shop/oss/shopBrand/shopFactoryCheckImport/importRecordDetail',
    method: 'get',
    params
  })
}

// 厂商&商家数据关联列表  关联商家导入
export function postVendorMerchantAssoc(data) {
  return request({
    url: '/shop/oss/shopBrand/shopFactoryCheckImport/importShopList',
    method: 'post',
    data
  })
}

// 厂家订单实时计算
export function calcOrderPriceAndProject(data) {
  return request({
    url: '/factory/oss/factory/order/calcOrderPriceAndProject',
    method: 'post',
    data
  })
}

// 车辆认证操作日志
export function getCertifiOperateLog(params) {
  return request({
    url: '/carport/oss/carport/certifi/operateLog',
    method: 'get',
    params
  })
}

// 修改商家在售
export function updateShowStatus(data) {
  return request({
    url: '/shop/oss/brand/agent/relation/updateShowStatus',
    method: 'post',
    data
  })
}

// 经销商合同管理列表
export function dealerContractManageList(params) {
  return request({
    url: '/shop/oss/contract/info/sub/list',
    method: 'get',
    params
  })
}

// 经销商合同管理无效
export function dealerContractManageInvalid(data) {
  return request({
    url: '/shop/oss/contract/info/sub/disabled',
    method: 'post',
    data
  })
}

// 经销商合同管理日志
export function dealerContractManageLog(params) {
  return request({
    url: '/shop/oss/contract/info/sub/log',
    method: 'get',
    params
  })
}

// 判断合同下是否有订单
export function contractHasValidOrder(params) {
  return request({
    url: '/factory/oss/factory/contract/contractHasValidOrder',
    method: 'get',
    params
  })
}

// 作废合同
export function invalidContract(data) {
  return request({
    url: '/factory/oss/factory/contract/invalidContract',
    method: 'post',
    data
  })
}

// 修改是否有效
export function updateValid(data) {
  return request({
    url: '/shop/oss/brand/agent/relation/updateValid',
    method: 'post',
    data
  })
}

// 车型公告栏审核 - 分页
export function bulletinBoardAuditList(params) {
  return request({
    url: '/factory/oss/goodsBillboard/auditPage',
    method: 'get',
    params
  })
}

// 车型公告栏审核 - 审核
export function goodsBillboardAudit(data) {
  return request({
    url: '/factory/oss/goodsBillboard/audit',
    method: 'post',
    data
  })
}

// 车型公告栏权限开关
export function updateGoodsBillboard(data) {
  return request({
    url: '/carport/oss/brand/updateGoodsBillboard',
    method: 'post',
    data
  })
}

// 年包品牌替换
export function replaceShopBrand(data) {
  return request({
    url: '/shop/oss/merchant/brand/replaceShopBrand',
    method: 'post',
    data
  })
}

// 商家成长内容列表-模块列表
export function growthCategoryList(params) {
  return request({
    url: '/shop/oss/growth/category/list',
    method: 'get',
    params
  })
}

// 商家成长内容列表-保存模块
export function growthCategorySave(data) {
  return request({
    url: '/shop/oss/growth/category/save',
    method: 'post',
    data
  })
}

// 商家成长内容列表-删除模块
export function growthCategoryDelete(data) {
  return request({
    url: '/shop/oss/growth/category/del',
    method: 'post',
    data
  })
}

// 商家成长内容列表-模块排序
export function growthCategorySort(data) {
  return request({
    url: '/shop/oss/growth/category/sort',
    method: 'post',
    data
  })
}

// 商家成长内容列表-文章排序
export function growthEssaySort(data) {
  return request({
    url: '/shop/oss/growth/essay/sort',
    method: 'post',
    data
  })
}

// 二手车-车辆详情-所有人解密
export function getOcrOwner(params) {
  return request({
    url: '/transaction/adminMotorUsedCarController/getOcrOwner',
    method: 'get',
    params
  })
}

// 二手车-车辆详情-所有人解密
export function updateOcrOwner(data) {
  return request({
    url: '/transaction/adminMotorUsedCarController/updateOcrOwner',
    method: 'post',
    data
  })
}

// 款型图片保存
export function postCarImgInfo(data) {
  return request({
    url: '/carport/oss/car/img/info',
    method: 'post',
    data
  })
}

export function getCarlabelTag(params) {
  return request({
    url: `${EMOTOFINEMANAGERURL}carport/oss/goods/car/labelTag`,
    method: 'get',
    params
  })
}

export function postAddCarlabelTag(data) {
  return request({
    url: `${EMOTOFINEMANAGERURL}carport/oss/goods/car/add/labelTag`,
    method: 'post',
    data
  })
}

export function postCarVideoInfo(data) {
  return request({
    url: `/carport/oss/car/video/info`,
    method: 'post',
    data
  })
}

/**
 * 内容白名单列表
 */
export function getAuthorBlacklist(params) {
  return request({
    url: `/carport/oss/oss/scoreUserWhite/list`,
    method: 'get',
    params
  })
}

/**
 * 保存白名单
 */
export function postSaveBlackAuthor(data) {
  return request({
    url: `/carport/oss/oss/scoreUserWhite/save`,
    method: 'post',
    data
  })
}
/**
 * 移除白名单
 */
export function postDeleteBlackAuthor(data) {
  return request({
    url: `/carport/oss/oss/scoreUserWhite/deleteById`,
    method: 'post',
    data
  })
}
export function postMessageDelete(data) {
  return request({
    url: `/uic/oss/private/message/delete`,
    method: 'post',
    data
  })
}

// 厂家发票信息列表-过滤
export function getInvoiceOrderPayer(params) {
  return request({
    url: '/factory/oss/factory/order/getOrderPayer',
    method: 'get',
    params
  })
}

// 查询该款型下的所有亮点的分类的数据
export function getLightTypeCategoryList(params) {
  return request({
    url: '/carport/oss/car/lightType/CategoryList',
    method: 'get',
    params
  })
}

export function getShopContrateExport(params) {
  return request({
    method: 'get',
    params
  })
}
//查询该款型下的所有亮点的分类
export function getLightTypeCategory(params) {
  return request({
    url: '/carport/oss/car/listLight/TypeCategory',
    method: 'get',
    params
  })
}

//查询该款型下的所有亮点的分类的数据
export function setLightTypeCategoryData(data) {
  return request({
    url: '/carport/oss/car/lightType/Category/save',
    method: 'post',
    data
  })
}

//查询该款型下的所有亮点标题
export function getLightTypeCategoryTitle(params) {
  return request({
    url: '/carport/oss/car/lightType/Category/title',
    method: 'get',
    params
  })
}

//报存该款型下的所有亮点标题
export function setLightTypeCategoryTitle(data) {
  return request({
    url: '/carport/oss/car/lightType/Category/title/save',
    method: 'post',
    data
  })
}

//保存该款型下的所有亮点标题
export function getLightTypeCategorySyschrCar(data) {
  return request({
    data
  })
}
