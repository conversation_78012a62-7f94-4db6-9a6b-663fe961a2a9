import request from '@/utils/request'
import { APIURL, MANAGERURL } from '@/utils/configData/config'

/**
 * 版本详情列表
 * @param query
 */
export function GetVersionList(data) {
  return request({
    url: '/forum/oss/appVersionController/getAppVersionList',
    method: 'post',
    data
  })
}

/**
 * 保存/更新版本详情
 * @param query
 */
export function UpdataVersion(data) {
  return request({
    url: '/forum/oss/appVersionController/appVersionSave',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10701',
    data
  })
}

/**
 * 删除版本详情
 * @param query
 */
export function DeleteVersion(data) {
  return request({
    url: '/forum/oss/appVersionController/appVersionBatchDelete',
    method: 'post',
    data
  })
}

/**
 * 获取版本详情
 * @param query
 */
export function GetVersion(data) {
  return request({
    url: '/forum/oss/appVersionController/findAppVersionDetailById',
    method: 'post',
    data
  })
}

/**
 * 上传文件
 * @param query
 */
// export function PostFile(data) {
//   return request({
//     url: `${APIURL}forum/public/upload/file_one_pkg.do`,
//     method: 'post',
//     data,
//     headers: {
//       'Content-Type': 'multipart/form-data'
//     }
//   })
// }

/**
 * 获取反馈列表
 * @param query
 */
export function GetFeedbackList(data) {
  return request({
    url: `${MANAGERURL}forum/oss/feedBackController/getFeedBacks`,
    method: 'post',
    data
  })
}

/**
 * 保存反馈信息
 * @param query
 */
export function FeedBackSave(data) {
  return request({
    url: `${MANAGERURL}forum/oss/feedBackController/feedBackSave`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10702',
    data
  })
}

/**
 * 更新是否跟进
 * @param query
 */
export function UpdateFollow(data) {
  return request({
    url: `${MANAGERURL}forum/oss/feedBackController/updateFollow`,
    method: 'post',
    data
  })
}

/**
 * 发送反馈结果
 * @param query
 */
export function FeedBackSend(data) {
  return request({
    url: `${MANAGERURL}forum/oss/feedBackController/feedBackSend`,
    method: 'post',
    data
  })
}

/**
 * 测试专用-获取获取验证码
 * @param query
 */
export function GetQaCreateCode(query) {
  return request({
    url: `/uic/oss/qa/createCode`,
    method: 'get',
    query
  })
}

/**
 * 测试专用-获取设备号
 * @param query
 */
export function GetDeviceIdByUid(query) {
  return request({
    url: `/uic/oss/qa/getDeviceId`,
    method: 'get',
    params: query
  })
}
export function IsRiskMobile(query) {
  return request({
    url: `/uic/oss/qa/isRiskMobile`,
    method: 'get',
    params: query
  })
}

/**
 * 测试专用-清除设备
 * @param data
 */
export function QaClearDevice(data) {
  return request({
    url: `/uic/oss/qa/clearDevice`,
    method: 'post',
    data
  })
}

export function ClearShumeiRiskMobile(data) {
  return request({
    url: `/uic/oss/qa/clearShumeiRiskMobile`,
    method: 'post',
    data
  })
}

/**
 * 测试专用-查询小号余量
 * @param data
 */
export function QuerySecretNoRemain(data) {
  return request({
    url: `/pretty/number/oss/info/querySecretNoRemain`,
    method: 'post',
    data
  })
}

/**
 * 测试专用-购买小号
 * @param data
 */
export function BuySecretNo(data) {
  return request({
    url: `/pretty/number/oss/info/buySecretNo`,
    method: 'post',
    data
  })
}

/**
 * 测试专用-清理指定用户行为验证缓存
 * @param data
 */
export function ClearConductVerifyCache(data) {
  return request({
    url: `/riskcontrol/oss/action/warning/clearConductVerifyCache`,
    method: 'post',
    data
  })
}

/**
 * 测试专用-清理指定用户行为验证缓存
 * @param data
 */
export function ClearQueryPriceCache(data) {
  return request({
    url: `/riskcontrol/oss/action/warning/clearQueryPriceCache`,
    method: 'post',
    data
  })
}

/**
 * 测试专用-注销用户
 * @param data
 */
export function LogoffUser(data) {
  return request({
    url: `/user/center/oss/userInfo/logoff`,
    method: 'post',
    data
  })
}

/**
 * 测试专用-清除省市缓存
 * @param data
 */
export function clearMapCache(data) {
  return request({
    url: `/expands/oss/map/clearMapCache`,
    method: 'post',
    data
  })
}

export function QuickBuySecretNo(data) {
  return request({
    url: `/pretty/number/oss/info/quickBuyNo`,
    method: 'post',
    data
  })
}

/**
 * 上传文件批量解密身份证手机号
 * @param data
 */
export function mobileDecrypt(data) {
  return request({
    url: `/senstive/oss/mobile/decrypt`,
    method: 'post',
    transformRequest(data) {
      const formData = new FormData()
      for (const item in data) {
        formData.append(item, data[item])
      }
      return formData
    },
    contentType: false,
    data
  })
}

/**
 * 用户协议列表
 * @param data
 */
export function GetAgreementList(data) {
  return request({
    url: `/expands/oss/agreementController/getAgreementList`,
    method: 'post',
    data
  })
}

/**
 * 保存用户协议
 * @param data
 */
export function PostAgreementSave(data) {
  return request({
    url: `/expands/oss/agreementController/agreementSave`,
    method: 'post',
    data
  })
}

/**
 * 获取获取验证码
 * @param data
 */
export function RequestCodeCode(data) {
  return request({
    url: `${APIURL}user/center/mobile/sendVerifyCode`,
    method: 'post',
    data: {
      ...data,
      businessType: 1
    }
  })
}

/**
 * 注册新用户
 * @param data
 */
export function LoginMobile(data) {
  return request({
    url: `${APIURL}user/center/login/mobile`,
    method: 'post',
    data
  })
}
/**
 * 检测是否已注册
 * @param data
 */
export function checkRegister(query) {
  return request({
    url: `/shop/oss/contacts/checkRegister`,
    method: 'get',
    params: query
  })
}
/**
 * 工单详情
 * @param data
 */
export function getWorkOrderDetail(query) {
  return request({
    url: `/workOrder/oss/workOrder/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 操作日志列表
 * @param data
 */
export function getWorkOrderLogList(data) {
  return request({
    url: `/workOrder/oss/workOrder/optLog/list`,
    method: 'post',
    data
  })
}
/**
 * 添加操作日志
 * @param data
 */
export function addWorkOrderLog(data) {
  return request({
    url: `/workOrder/oss/workOrder/optLog/add`,
    method: 'post',
    data
  })
}
/**
 * 全部工单
 * @param data
 */
export function getAllOrderList(data) {
  return request({
    url: `/workOrder/oss/workOrder/list`,
    method: 'post',
    data
  })
}
/**
 * 我的代办列表
 * @param data
 */
export function getMyTodoOrderList(data) {
  return request({
    url: `/workOrder/oss/workOrder/list/todo/mine`,
    method: 'post',
    data
  })
}
/**
 * 变更工单状态  6-完成;7-关闭
 * @param data
 */
export function updateWorkerOrderState(data) {
  return request({
    url: `/workOrder/oss/workOrder/updateState`,
    method: 'post',
    data
  })
}

/**
 * 创建工单
 * @param data
 */
export function createWorkOrder(data) {
  return request({
    url: `/workOrder/oss/workOrder/create`,
    method: 'post',
    data
  })
}

/**
 * 工单业务类型列表
 * @param data
 */
export function getbizTypeList(query) {
  return request({
    url: `/workOrder/oss/workOrder/bizType/list`,
    method: 'get',
    params: query
  })
}
/**
 * 分配责任人
 * @param data
 */
export function assignDuty(data) {
  return request({
    url: `/workOrder/oss/workOrder/assign`,
    method: 'post',
    data
  })
}
/**
 * 处理工单
 * @param data
 */
export function handleWorkOrder(data) {
  return request({
    url: `/workOrder/oss/workOrder/handle`,
    method: 'post',
    data
  })
}
/**
 * 驳回工单
 * @param data
 */
export function rejectWorkOrder(data) {
  return request({
    url: `/workOrder/oss/workOrder/reject`,
    method: 'post',
    data
  })
}
/**
 * 根据用户名查询工单责任人
 * @param data
 */
export function getByDutyName(query) {
  return request({
    url: `/workOrder/oss/workOrder/duty/getByName`,
    method: 'get',
    params: query
  })
}
/**
 * 查询上一个责任人
 * @param data
 */
export function getPreDuty(query) {
  return request({
    url: `/workOrder/oss/workOrder/duty/getPreDuty`,
    method: 'get',
    params: query
  })
}
/**
 * 消息模板列表
 * @param data
 */
export function getMsgTemplate(query) {
  return request({
    url: `/workOrder/oss/workOrder/msgTemplate/list`,
    method: 'get',
    params: query
  })
}
/**
 * 发送消息
 * @param data
 */
export function postSendMessage(data) {
  return request({
    url: `/workOrder/oss/workOrder/msg/send`,
    method: 'post',
    data
  })
}
/**
 * 编辑工单
 * @param data
 */
export function updateWorkOrder(data) {
  return request({
    url: `/workOrder/oss/workOrder/update`,
    method: 'post',
    data
  })
}
/**
 * 获取操作人 | 创建人
 * @param data
 */
export function getOperatorByName(query) {
  return request({
    url: `/workOrder/oss/workOrder/operator/getByName`,
    method: 'get',
    params: query
  })
}

/**
 * 群组列表
 * @param query
 */
export function workOrderGroupList(query) {
  return request({
    url: `/workOrder/oss/group/list`,
    method: 'get',
    params: query
  })
}

/**
 * 变更群组有效状态
 * @param data
 */
export function groupChangeValid(data) {
  return request({
    url: `/workOrder/oss/group/changeValid`,
    method: 'post',
    data
  })
}

/**
 * 删除群组
 * @param data
 */
export function groupDelete(data) {
  return request({
    url: `/workOrder/oss/group/delete`,
    method: 'post',
    data
  })
}

/**
 * 创建群组
 * @param data
 */
export function groupCreate(data) {
  return request({
    url: `/workOrder/oss/group/create`,
    method: 'post',
    data
  })
}

/**
 * 更新群组
 * @param data
 */
export function groupUpdate(data) {
  return request({
    url: `/workOrder/oss/group/update`,
    method: 'post',
    data
  })
}

/**
 * 根据用户名查询系统用户
 * @param query
 */
export function queryingSystemUsers(query) {
  return request({
    url: `/workOrder/oss/group/systemUser/getByName`,
    method: 'get',
    params: query
  })
}

/**
 * 群组成员列表
 * @param query
 */
export function groupMemberList(query) {
  return request({
    url: `/workOrder/oss/group/member/list`,
    method: 'get',
    params: query
  })
}

/**
 * 添加群组成员
 * @param data
 */
export function groupMemberAdd(data) {
  return request({
    url: `/workOrder/oss/group/member/add`,
    method: 'post',
    data
  })
}

/**
 * 添加群组成员
 * @param data
 */
export function groupMemberDelete(data) {
  return request({
    url: `/workOrder/oss/group/member/delete`,
    method: 'post',
    data
  })
}

/**
 * 变更成员角色
 * @param data
 */
export function updateGroupRole(data) {
  return request({
    url: `/workOrder/oss/group/member/updateGroupRole`,
    method: 'post',
    data
  })
}

/**
 * 分配工单(分配给我)
 * @param data
 */
export function workOrderAssignToMe(data) {
  return request({
    url: `/workOrder/oss/workOrder/assign/tome`,
    method: 'post',
    data
  })
}

/**
 * 群组代办工单
 * @param data
 */
export function getGroupWorkOrders(data) {
  return request({
    url: `/workOrder/oss/workOrder/list/todo/group`,
    method: 'post',
    data
  })
}

/**
 * 批量分配工单
 * @param data
 */
export function workOrderAssignBatch(data) {
  return request({
    url: `/workOrder/oss/workOrder/assign/batch`,
    method: 'post',
    data
  })
}

/**
 * 查询用户的工单群组角色
 * @param query
 */
export function groupRoleGetByUid(query) {
  return request({
    url: `/workOrder/oss/workOrder/groupRole/getByUid`,
    method: 'get',
    params: query
  })
}

/**
 * 查询用户的工单群组角色
 * @param query
 */
export function groupRelationGetByUid(query) {
  return request({
    url: `/workOrder/oss/group/relation/getByUid`,
    method: 'get',
    params: query
  })
}

/**
 * 判断工单的 问题类型是否为二手车/投诉纠纷 & 关联对象是否为经销商
 * @param query
 */
export function getWorkOrderCheckType(query) {
  return request({
    url: `/workOrder/oss/workOrder/checkType`,
    method: 'get',
    params: query
  })
}

/**
 * 企业人员列表
 * @param data
 */
export function enterpriseUserList(data) {
  return request({
    url: `/admin/auth/enterprise/user/list`,
    method: 'post',
    data
  })
}

/**
 * 新增企业人员
 * @param data
 */
export function enterpriseUserAdd(data) {
  return request({
    url: `/admin/auth/enterprise/user/add`,
    method: 'post',
    data
  })
}

/**
 * 编辑企业人员
 * @param data
 */
export function enterpriseUserEdit(data) {
  return request({
    url: `/admin/auth/enterprise/user/edit`,
    method: 'post',
    data
  })
}

/**
 * 变更有效状态
 * @param data
 */
export function enterpriseUserChangeValid(data) {
  return request({
    url: `/admin/auth/enterprise/user/changeValid`,
    method: 'post',
    data
  })
}

/**
 * 根据手机号获取用户创建信息
 * @param query
 */
export function getUserCreateInfo(query) {
  return request({
    url: `/admin/auth/enterprise/user/createInfo/getByMobile`,
    method: 'get',
    params: query
  })
}

/**
 * 配置详情
 * @param query
 */
export function appClientConfig(query) {
  return request({
    url: `/expands/oss/config/appClientConfig`,
    method: 'get',
    params: query
  })
}

/**
 * 配置修改
 * @param data
 */
export function updateClientConfig(data) {
  return request({
    url: `/expands/oss/config/updateClientConfig`,
    method: 'post',
    data
  })
}

/**
 * 安卓渠道管理 列表查询
 * @param query
 */
export function incrementPackage(query) {
  return request({
    url: `/expands/oss/increment/package`,
    method: 'get',
    params: query
  })
}

/**
 * 配置修改
 * @param data
 */
export function incrementPackageDiffPatch(data) {
  return request({
    url: `/expands/oss/increment/package/diffPatch`,
    method: 'post',
    data
  })
}
/**
 * 报警机器人 列表
 * @param query
 */
export function getMachingList(query) {
  return request({
    url: `/workOrder/oss/alert/robot/list`,
    method: 'get',
    params: query
  })
}
/**
 * 修改报警机器人
 * @param data
 */
export function editMaching(data) {
  return request({
    url: `/workOrder/oss/alert/robot/update`,
    method: 'post',
    data
  })
}
/**
 * 创建报警机器人
 * @param data
 */
export function createMaching(data) {
  return request({
    url: `/workOrder/oss/alert/robot/create`,
    method: 'post',
    data
  })
}
/**
 * 报警 列表
 * @param query
 */
export function getWarningList(query) {
  return request({
    url: `/workOrder/oss/alert/list`,
    method: 'get',
    params: query
  })
}
/**
 * 修改报警
 * @param data
 */
export function editWarning(data) {
  return request({
    url: `/workOrder/oss/alert/update`,
    method: 'post',
    data
  })
}
/**
 * 创建报警
 * @param data
 */
export function createWarning(data) {
  return request({
    url: `/workOrder/oss/alert/create`,
    method: 'post',
    data
  })
}

/**
 * 更新技术标记
 * @param data
 */
export function technologyMark(data) {
  return request({
    url: `workOrder/oss/workOrder/technology/mark`,
    method: 'post',
    data
  })
}

/**
 * 上传证书并返回最新版本号
 * @param data
 */
export function uploadCert(data) {
  return request({
    url: `${MANAGERURL}expands/oss/config/uploadCert`,
    method: 'post',
    data
  })
}

/**
 * 协议列表
 * @param query
 */
export function getProtocolList(query) {
  return request({
    url: `/expands/oss/protocol/list`,
    method: 'get',
    params: query
  })
}

/**
 * 保存或编辑协议
 * @param data
 */
export function protocolSaveOrUpdate(data) {
  return request({
    url: `/expands/oss/protocol/saveOrUpdate`,
    method: 'post',
    // transformRequest(data) {
    //   const formData = new FormData()
    //   for (const item in data) {
    //     formData.append(item, data[item])
    //   }
    //   return formData
    // },
    data
  })
}

/**
 * 协议历史记录列表
 * @param query
 */
export function getProtocolListRecord(query) {
  return request({
    url: `/expands/oss/protocol/listRecord`,
    method: 'get',
    params: query
  })
}

/**
 * 查询加密手机号
 * @param query
 */
export function encryptMobile(query) {
  return request({
    url: `/user/center/oss/user/certify/encryptMobile`,
    method: 'get',
    params: query
  })
}

/**
 * 根据设备ID查询用户UID列表
 * @param params
 */
export function getUidListByDeviceId(params) {
  return request({
    url: `/uic/oss/qa/getUidListByDeviceId`,
    method: 'get',
    params
  })
}

/**
 * 查询用户所有的设备号
 * @param params
 */
export function devicesByUid(params) {
  return request({
    url: `/uic/oss/userQueryInfo/devicesByUid`,
    method: 'get',
    params
  })
}

/**
 * 证书提醒管理列表
 * @param data
 */
export function getCertificateList(params) {
  return request({
    url: `/expands/oss/certificate/list`,
    method: 'get',
    params
  })
}

/**
 * 新增证书提醒
 * @param data
 */
export function postCertificateAdd(data) {
  return request({
    url: `/expands/oss/certificate/add`,
    method: 'post',
    data
  })
}

/**
 * 新增证书提醒
 * @param data
 */
export function postCertificateupdate(data) {
  return request({
    url: `/expands/oss/certificate/update`,
    method: 'post',
    data
  })
}

/**
 * 证书厂商列表
 * @param data
 */
export function getCertificateFactory(params) {
  return request({
    url: `/expands/oss/certificate/factory/list`,
    method: 'get',
    params
  })
}

/**
 * 业务类型列表
 * @param data
 */
export function getCertificateBusinessTypeList(params) {
  return request({
    url: `/expands/oss/certificate/businessType/list`,
    method: 'get',
    params
  })
}

/**
 * 证书厂商列表
 * @param data
 */
export function postCertificateDelete(data) {
  return request({
    url: `/expands/oss/certificate/delete`,
    method: 'post',
    data
  })
}

/**
 * 证书类型列表
 * @param params
 */
export function getCertificateTypeList(params) {
  return request({
    url: `/expands/oss/certificate/type/list`,
    method: 'get',
    params
  })
}

/**
 * 证书类型列表
 * @param params
 */
export function getOssUserList(data) {
  return request({
    url: `/admin/auth/enterprise/user/list`,
    method: 'post',
    data
  })
}
